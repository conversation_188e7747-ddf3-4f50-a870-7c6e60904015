<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSearch Agent - الواجهة المحسنة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1400px;
            padding: 30px;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .btn-primary-custom {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .form-control-custom {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control-custom:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-danger { background-color: #dc3545; }
        
        .nav-pills .nav-link {
            border-radius: 10px;
            margin: 0 5px;
            transition: all 0.3s ease;
        }
        
        .nav-pills .nav-link.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
        }
        
        .spinner-custom {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .result-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .copy-button {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 5px 10px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .code-container {
            position: relative;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .alert-custom {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-content">
            <div class="spinner-custom"></div>
            <h5 id="loadingMessage">جاري المعالجة...</h5>
            <p class="text-muted" id="loadingDetails">يرجى الانتظار</p>
        </div>
    </div>

    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="text-center mb-4">
                <h1 class="display-4 text-primary">
                    <i class="fas fa-robot"></i>
                    DeepSearch Agent Pro
                </h1>
                <p class="lead text-muted">منصة متقدمة للبحث العميق وإنتاج المقالات بالذكاء الاصطناعي</p>
                
                <!-- Status Indicators -->
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center justify-content-center">
                            <span>نظام البحث</span>
                            <span id="searchStatus" class="status-indicator status-warning"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center justify-content-center">
                            <span>Gemini AI</span>
                            <span id="geminiStatus" class="status-indicator status-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center justify-content-center">
                            <span>Blogger API</span>
                            <span id="bloggerStatus" class="status-indicator status-danger"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <ul class="nav nav-pills justify-content-center mb-4" id="mainTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="search-tab" data-bs-toggle="pill" 
                            data-bs-target="#search-panel" type="button" role="tab">
                        <i class="fas fa-search"></i> البحث العميق
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="article-tab" data-bs-toggle="pill" 
                            data-bs-target="#article-panel" type="button" role="tab">
                        <i class="fas fa-edit"></i> إنتاج المقالات
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="publish-tab" data-bs-toggle="pill" 
                            data-bs-target="#publish-panel" type="button" role="tab">
                        <i class="fas fa-upload"></i> النشر والتصدير
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="config-tab" data-bs-toggle="pill" 
                            data-bs-target="#config-panel" type="button" role="tab">
                        <i class="fas fa-cogs"></i> الإعدادات
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="mainTabContent">
                <!-- Search Panel -->
                <div class="tab-pane fade show active" id="search-panel" role="tabpanel">
                    <div class="feature-card">
                        <h4><i class="fas fa-search"></i> البحث العميق المتقدم</h4>
                        <p class="text-muted">ابحث عن موضوع واحد بعمق واستخرج معلومات شاملة عنه</p>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <input type="text" id="deepSearchQuery" class="form-control form-control-custom" 
                                       placeholder="أدخل الموضوع الذي تريد البحث عنه بعمق...">
                            </div>
                            <div class="col-md-4">
                                <button onclick="performDeepSearch()" class="btn btn-primary-custom w-100">
                                    <i class="fas fa-search"></i> بحث عميق
                                </button>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">نوع المقال المطلوب</label>
                                <select id="articleStyle" class="form-select form-control-custom">
                                    <option value="professional">مهني ورسمي</option>
                                    <option value="casual">غير رسمي</option>
                                    <option value="news">إخباري</option>
                                    <option value="analysis">تحليلي</option>
                                    <option value="tutorial">تعليمي</option>
                                    <option value="review">مراجعة</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">اللغة المستهدفة</label>
                                <select id="targetLanguage" class="form-select form-control-custom">
                                    <option value="ar">العربية</option>
                                    <option value="en">الإنجليزية</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Search Results -->
                    <div id="searchResults" style="display: none;"></div>
                </div>

                <!-- Article Generation Panel -->
                <div class="tab-pane fade" id="article-panel" role="tabpanel">
                    <div class="feature-card">
                        <h4><i class="fas fa-edit"></i> إنتاج المقالات بالذكاء الاصطناعي</h4>
                        <p class="text-muted">أنتج مقالات عالية الجودة باللهجة المصرية مع تحسين SEO</p>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <label class="form-label">موضوع المقال</label>
                                <input type="text" id="articleTopic" class="form-control form-control-custom" 
                                       placeholder="أدخل موضوع المقال...">
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <label class="form-label">نوع المقال</label>
                                <select id="articleType" class="form-select form-control-custom">
                                    <option value="professional">مهني ورسمي</option>
                                    <option value="casual">غير رسمي</option>
                                    <option value="news">إخباري</option>
                                    <option value="analysis">تحليلي</option>
                                    <option value="tutorial">تعليمي</option>
                                    <option value="review">مراجعة</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">عدد الكلمات (تقريبي)</label>
                                <select id="wordCount" class="form-select form-control-custom">
                                    <option value="500-800">قصير (500-800 كلمة)</option>
                                    <option value="800-1200" selected>متوسط (800-1200 كلمة)</option>
                                    <option value="1200-1800">طويل (1200-1800 كلمة)</option>
                                    <option value="1800-2500">مفصل (1800-2500 كلمة)</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="useDeepSearch" checked>
                                    <label class="form-check-label" for="useDeepSearch">
                                        استخدام البحث العميق
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <label class="form-label">كلمات مفتاحية SEO (اختيارية)</label>
                                <input type="text" id="seoKeywords" class="form-control form-control-custom" 
                                       placeholder="أدخل الكلمات المفتاحية مفصولة بفواصل...">
                                <small class="text-muted">مثال: ذكاء اصطناعي, تقنية, مستقبل</small>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="egyptianDialect" checked>
                                    <label class="form-check-label" for="egyptianDialect">
                                        استخدام اللهجة المصرية
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="addSpellingErrors" checked>
                                    <label class="form-check-label" for="addSpellingErrors">
                                        إضافة أخطاء إملائية بسيطة
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button onclick="generateArticle()" class="btn btn-primary-custom">
                                <i class="fas fa-magic"></i> إنتاج المقال
                            </button>
                        </div>
                    </div>
                    
                    <!-- Article Results -->
                    <div id="articleResults" style="display: none;"></div>
                </div>

                <!-- Publishing Panel -->
                <div class="tab-pane fade" id="publish-panel" role="tabpanel">
                    <div class="feature-card">
                        <h4><i class="fas fa-upload"></i> النشر والتصدير</h4>
                        <p class="text-muted">انشر المقال على Blogger أو احصل على نسخة HTML للنسخ</p>
                        
                        <!-- Publishing Options -->
                        <div id="publishingOptions" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <button onclick="publishToBlogger()" class="btn btn-success w-100 mb-2">
                                        <i class="fab fa-blogger"></i> نشر على Blogger
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button onclick="generateHTMLCopy()" class="btn btn-info w-100 mb-2">
                                        <i class="fas fa-code"></i> إنشاء نسخة HTML
                                    </button>
                                </div>
                            </div>
                            
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="publishAsDraft" checked>
                                        <label class="form-check-label" for="publishAsDraft">
                                            نشر كمسودة
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <input type="text" id="customLabels" class="form-control form-control-custom" 
                                           placeholder="تصنيفات إضافية (اختيارية)">
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info alert-custom">
                            <i class="fas fa-info-circle"></i>
                            لاستخدام ميزات النشر، يجب أولاً إنتاج مقال من تبويب "إنتاج المقالات"
                        </div>
                    </div>
                    
                    <!-- Publishing Results -->
                    <div id="publishingResults" style="display: none;"></div>
                </div>

                <!-- Configuration Panel -->
                <div class="tab-pane fade" id="config-panel" role="tabpanel">
                    <!-- Gemini Configuration -->
                    <div class="feature-card">
                        <h4><i class="fab fa-google"></i> إعدادات Gemini AI</h4>
                        <p class="text-muted">قم بتكوين API key لـ Gemini لإنتاج المقالات</p>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <label class="form-label">Gemini API Key</label>
                                <input type="password" id="geminiApiKey" class="form-control form-control-custom" 
                                       placeholder="أدخل Gemini API Key...">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">النموذج</label>
                                <select id="geminiModel" class="form-select form-control-custom">
                                    <option value="gemini-2.0-flash-exp">Gemini 2.0 Flash</option>
                                    <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                                    <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button onclick="configureGemini()" class="btn btn-primary-custom">
                                <i class="fas fa-save"></i> حفظ إعدادات Gemini
                            </button>
                            <button onclick="testGemini()" class="btn btn-outline-primary ms-2">
                                <i class="fas fa-vial"></i> اختبار الاتصال
                            </button>
                        </div>
                    </div>
                    
                    <!-- Blogger Configuration -->
                    <div class="feature-card">
                        <h4><i class="fab fa-blogger"></i> إعدادات Blogger API</h4>
                        <p class="text-muted">قم بتكوين Service Account للنشر المباشر على Blogger</p>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <label class="form-label">Service Account JSON</label>
                                <textarea id="serviceAccountJson" class="form-control form-control-custom" rows="4" 
                                          placeholder="الصق محتوى ملف Service Account JSON هنا..."></textarea>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Blog ID</label>
                                <input type="text" id="blogId" class="form-control form-control-custom" 
                                       placeholder="أدخل Blog ID...">
                                <small class="text-muted">يمكن العثور عليه في إعدادات المدونة</small>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button onclick="configureBlogger()" class="btn btn-primary-custom">
                                <i class="fas fa-save"></i> حفظ إعدادات Blogger
                            </button>
                            <button onclick="testBlogger()" class="btn btn-outline-primary ms-2">
                                <i class="fas fa-vial"></i> اختبار الاتصال
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="enhanced.js"></script>
</body>
</html>
