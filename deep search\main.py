#!/usr/bin/env python3
"""
DeepSearch Agent - Main entry point
"""

import asyncio
import argparse
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config_loader import load_config
from src.utils.logger import setup_logging
from src.core.search_engine import DeepSearchEngine
from loguru import logger


async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="DeepSearch Agent - Advanced Search Tool")
    parser.add_argument("--config", "-c", help="Configuration file path")
    parser.add_argument("--query", "-q", help="Search query")
    parser.add_argument("--max-results", "-m", type=int, default=10, help="Maximum results")
    parser.add_argument("--engines", "-e", nargs="+", help="Search engines to use")
    parser.add_argument("--rag", action="store_true", help="Use RAG search")
    parser.add_argument("--api", action="store_true", help="Start API server")
    parser.add_argument("--health", action="store_true", help="Perform health check")
    parser.add_argument("--trending", action="store_true", help="Get trending topics")
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Setup logging
    setup_logging(config)
    
    logger.info("Starting DeepSearch Agent...")
    
    if args.api:
        # Start API server
        import uvicorn
        from src.api.main import app
        
        api_config = config.get('api', {})
        logger.info("Starting API server...")
        
        uvicorn.run(
            app,
            host=api_config.get('host', '0.0.0.0'),
            port=api_config.get('port', 8000),
            workers=api_config.get('workers', 1),
            reload=api_config.get('debug', False)
        )
        
    elif args.query:
        # Perform search
        async with DeepSearchEngine(config) as engine:
            if args.rag:
                # RAG search
                logger.info(f"Performing RAG search for: {args.query}")
                result = await engine.rag_search(args.query)
                
                print(f"\n🔍 استعلام: {result['query']}")
                print(f"📝 الإجابة: {result['answer']}")
                print(f"🎯 الثقة: {result['confidence']:.2f}")
                print(f"⏱️ الوقت: {result['processing_time']:.2f}s")
                
                if result['sources']:
                    print(f"\n📚 المصادر ({len(result['sources'])}):")
                    for i, source in enumerate(result['sources'], 1):
                        print(f"  {i}. {source['title']}")
                        print(f"     {source['url']}")
                        print(f"     التشابه: {source['similarity_score']:.2f}")
                        print()
            else:
                # Regular search
                logger.info(f"Performing search for: {args.query}")
                result = await engine.search(
                    query=args.query,
                    max_results=args.max_results,
                    engines=args.engines
                )
                
                print(f"\n🔍 استعلام: {result['query']}")
                if result['enhanced_query'] != result['query']:
                    print(f"🔄 استعلام محسن: {result['enhanced_query']}")
                print(f"📊 النتائج: {result['total_results']}")
                print(f"⏱️ الوقت: {result['processing_time']:.2f}s")
                print(f"🔧 المحركات: {', '.join(result['engines_used'])}")
                
                if result['articles']:
                    print(f"\n📰 المقالات:")
                    for i, article in enumerate(result['articles'], 1):
                        print(f"\n{i}. {article['title']}")
                        print(f"   🔗 {article['url']}")
                        print(f"   📝 {article['summary'][:200]}...")
                        if article.get('sentiment'):
                            sentiment = article['sentiment']
                            print(f"   😊 المشاعر: {sentiment.get('sentiment', 'غير محدد')}")
                        if article.get('ai_keywords'):
                            keywords = ', '.join(article['ai_keywords'][:5])
                            print(f"   🏷️ الكلمات المفتاحية: {keywords}")
    
    elif args.health:
        # Health check
        async with DeepSearchEngine(config) as engine:
            logger.info("Performing health check...")
            health = await engine.health_check()
            
            print(f"\n🏥 فحص الصحة العامة")
            print(f"الحالة العامة: {health['overall_status']}")
            print(f"الوقت: {health['timestamp']}")
            
            print(f"\n🕷️ الكاشطات:")
            for engine_name, status in health.get('scrapers', {}).items():
                status_icon = "✅" if status else "❌"
                print(f"  {status_icon} {engine_name}")
            
            print(f"\n🧠 الذكاء الاصطناعي:")
            ai_status = health.get('ai', {})
            for component, status in ai_status.items():
                if isinstance(status, bool):
                    status_icon = "✅" if status else "❌"
                    print(f"  {status_icon} {component}")
            
            print(f"\n💾 قاعدة البيانات:")
            db_status = health.get('database', {})
            if db_status.get('status') == 'healthy':
                print(f"  ✅ قاعدة البيانات")
                stats = db_status.get('stats', {})
                if stats:
                    print(f"    📊 المقالات: {stats.get('total_articles', 0)}")
                    print(f"    🔍 الاستعلامات: {stats.get('total_queries', 0)}")
                    print(f"    🧮 التضمينات: {stats.get('total_embeddings', 0)}")
            else:
                print(f"  ❌ قاعدة البيانات")
    
    elif args.trending:
        # Get trending topics
        async with DeepSearchEngine(config) as engine:
            logger.info("Getting trending topics...")
            topics = await engine.get_trending_topics(limit=10)
            
            print(f"\n📈 الموضوعات الرائجة:")
            if topics:
                for i, topic in enumerate(topics, 1):
                    print(f"{i:2d}. {topic['topic']} ({topic['frequency']} مرة)")
            else:
                print("لا توجد موضوعات رائجة حالياً")
    
    else:
        # Show help
        parser.print_help()
        print(f"\n🔍 أمثلة الاستخدام:")
        print(f"  python main.py --query 'آخر أخبار الذكاء الاصطناعي'")
        print(f"  python main.py --query 'ما هو ChatGPT؟' --rag")
        print(f"  python main.py --api")
        print(f"  python main.py --health")
        print(f"  python main.py --trending")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        logger.error(f"خطأ في تشغيل البرنامج: {str(e)}")
        sys.exit(1)
