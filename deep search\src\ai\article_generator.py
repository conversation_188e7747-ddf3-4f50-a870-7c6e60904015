"""
Advanced Article Generator using Gemini AI
Specialized for creating high-quality articles with Egyptian dialect and SEO optimization
"""

import asyncio
import time
from typing import Dict, Any, List, Optional
from loguru import logger
import google.generativeai as genai
import os
import re
import random
from ..seo.seo_optimizer import SEOOptimizer
from ..localization.language_manager import LanguageManager


class ArticleGenerator:
    """Advanced article generator with Egyptian dialect and SEO features"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.ai_config = config.get('ai', {})
        self.article_config = config.get('article_generation', {})
        
        # Gemini configuration
        self.api_key = None
        self.model_name = "gemini-2.0-flash-exp"  # Default model
        self.client = None
        self.is_enabled = False
        
        # Article generation settings
        self.egyptian_dialect_enabled = self.article_config.get('egyptian_dialect', True)
        self.add_spelling_errors = self.article_config.get('add_spelling_errors', True)
        self.seo_optimization = self.article_config.get('seo_optimization', True)
        
        # Initialize SEO optimizer and language manager
        self.seo_optimizer = SEOOptimizer(config)
        self.language_manager = LanguageManager(config)

        # Egyptian dialect patterns (expanded)
        self.egyptian_replacements = {
            'هذا': 'ده',
            'هذه': 'دي',
            'ذلك': 'ده',
            'تلك': 'دي',
            'أريد': 'عايز',
            'أريدين': 'عايزة',
            'نريد': 'عايزين',
            'يريد': 'عايز',
            'تريد': 'عايزة',
            'يريدون': 'عايزين',
            'كيف': 'إزاي',
            'ماذا': 'إيه',
            'متى': 'إمتى',
            'أين': 'فين',
            'لماذا': 'ليه',
            'جداً': 'قوي',
            'كثيراً': 'كتير',
            'قليلاً': 'شوية',
            'الآن': 'دلوقتي',
            'اليوم': 'النهاردة',
            'أمس': 'إمبارح',
            'غداً': 'بكرة',
            'سوف': 'هـ',
            'سأذهب': 'هروح',
            'سنذهب': 'هنروح',
            'سيذهب': 'هيروح',
            'ستذهب': 'هتروح',
            'أذهب': 'أروح',
            'نذهب': 'نروح',
            'يذهب': 'يروح',
            'تذهب': 'تروح',
            'أعمل': 'أشتغل',
            'نعمل': 'نشتغل',
            'يعمل': 'يشتغل',
            'تعمل': 'تشتغل',
            'أقول': 'أقول',
            'نقول': 'نقول',
            'يقول': 'يقول',
            'تقول': 'تقول',
            'جميل': 'حلو',
            'جميلة': 'حلوة',
            'كبير': 'كبير',
            'كبيرة': 'كبيرة',
            'صغير': 'صغير',
            'صغيرة': 'صغيرة',
            'سريع': 'سريع',
            'سريعة': 'سريعة',
            'بطيء': 'بطيء',
            'بطيئة': 'بطيئة',
            'مهم': 'مهم',
            'مهمة': 'مهمة',
            'ضروري': 'لازم',
            'ضرورية': 'لازم',
            'ممكن': 'ممكن',
            'مستحيل': 'مستحيل',
            'بالطبع': 'أكيد',
            'طبعاً': 'أكيد',
            'نعم': 'أيوة',
            'لا': 'لأ',
            'ربما': 'يمكن',
            'أحياناً': 'ساعات',
            'دائماً': 'على طول',
            'أبداً': 'خالص',
            'فقط': 'بس',
            'أيضاً': 'كمان',
            'معاً': 'مع بعض',
            'وحدي': 'لوحدي',
            'وحدها': 'لوحدها',
            'وحده': 'لوحده'
        }
        
        # Common spelling errors and natural variations to add authenticity
        self.spelling_errors = {
            'الذي': 'اللي',
            'التي': 'اللي',
            'الذين': 'اللي',
            'اللواتي': 'اللي',
            'هؤلاء': 'دول',
            'أولئك': 'دول',
            'لكن': 'بس',
            'لكنه': 'بس هو',
            'لكنها': 'بس هي',
            'إن شاء الله': 'ان شاء الله',
            'ما شاء الله': 'ماشاء الله',
            'الحمد لله': 'الحمدلله',
            'بإذن الله': 'باذن الله',
            'إلى': 'الى',
            'على': 'علي',
            'هذا': 'هدا',
            'هذه': 'هده',
            'أيضاً': 'ايضا',
            'جداً': 'جدا',
            'كثيراً': 'كثيرا',
            'قليلاً': 'قليلا',
            'أخيراً': 'اخيرا',
            'أولاً': 'اولا',
            'ثانياً': 'ثانيا',
            'ثالثاً': 'ثالثا',
            'أساساً': 'اساسا',
            'تماماً': 'تماما',
            'خاصة': 'خاصه',
            'عامة': 'عامه',
            'مهمة': 'مهمه',
            'صعبة': 'صعبه',
            'سهلة': 'سهله',
            'جديدة': 'جديده',
            'قديمة': 'قديمه',
            'كبيرة': 'كبيره',
            'صغيرة': 'صغيره',
            'جميلة': 'جميله',
            'قبيحة': 'قبيحه'
        }
    
    def configure_gemini(self, api_key: str, model_name: str = None) -> Dict[str, Any]:
        """
        Configure Gemini API with provided credentials
        
        Args:
            api_key: Gemini API key
            model_name: Model name (optional)
            
        Returns:
            Configuration result
        """
        try:
            if not api_key:
                return {
                    "success": False,
                    "error": "API key is required"
                }
            
            # Test the API key
            genai.configure(api_key=api_key)
            
            # Set model name
            if model_name:
                self.model_name = model_name
            
            # Test model availability
            try:
                test_client = genai.GenerativeModel(self.model_name)
                test_response = test_client.generate_content(
                    "Test message",
                    generation_config=genai.types.GenerationConfig(
                        max_output_tokens=10,
                        temperature=0.1
                    )
                )
                
                if test_response and test_response.text:
                    self.api_key = api_key
                    self.client = test_client
                    self.is_enabled = True
                    
                    logger.info(f"Gemini configured successfully with model: {self.model_name}")
                    return {
                        "success": True,
                        "message": f"Successfully configured Gemini with {self.model_name}",
                        "model": self.model_name,
                        "api_key_valid": True
                    }
                else:
                    return {
                        "success": False,
                        "error": "Model test failed - empty response"
                    }
                    
            except Exception as model_error:
                return {
                    "success": False,
                    "error": f"Model '{self.model_name}' not available: {str(model_error)}"
                }
                
        except Exception as e:
            logger.error(f"Failed to configure Gemini: {str(e)}")
            return {
                "success": False,
                "error": f"API configuration failed: {str(e)}"
            }
    
    def get_available_models(self) -> List[str]:
        """Get list of available Gemini models"""
        return [
            "gemini-2.0-flash-exp",
            "gemini-1.5-pro",
            "gemini-1.5-flash",
            "gemini-pro"
        ]
    
    def is_configured(self) -> bool:
        """Check if Gemini is properly configured"""
        return self.is_enabled and self.client is not None
    
    async def generate_article(
        self,
        topic: str,
        research_data: Dict[str, Any],
        article_type: str = "professional",
        target_language: str = "ar",
        dialect: str = "egyptian",
        seo_keywords: List[str] = None,
        min_words: int = 800,
        max_words: int = 1500
    ) -> Dict[str, Any]:
        """
        Generate a high-quality article based on research data

        Args:
            topic: Article topic
            research_data: Research data from deep search
            article_type: Type of article (professional, casual, news, analysis)
            target_language: Target language
            dialect: Dialect for Arabic (egyptian, gulf, levantine, etc.)
            seo_keywords: SEO keywords to include
            min_words: Minimum word count
            max_words: Maximum word count

        Returns:
            Generated article with metadata
        """
        if not self.is_configured():
            return {
                "success": False,
                "error": "Gemini is not configured. Please provide API key first."
            }
        
        start_time = time.time()
        
        try:
            logger.info(f"Generating article for topic: {topic}")
            
            # Prepare research context
            context = self._prepare_research_context(research_data)
            
            # Generate article content
            article_content = await self._generate_article_content(
                topic, context, article_type, target_language, dialect,
                seo_keywords, min_words, max_words
            )
            
            if not article_content:
                return {
                    "success": False,
                    "error": "Failed to generate article content"
                }
            
            # Apply dialect if enabled and language is Arabic
            if self.egyptian_dialect_enabled and target_language == "ar":
                article_content = self.language_manager.apply_dialect(article_content, dialect)

            # Add natural spelling errors if enabled
            if self.add_spelling_errors:
                article_content = self.language_manager.add_natural_errors(
                    article_content, target_language, error_rate=0.02
                )
            
            # Extract and optimize SEO keywords if not provided
            if not seo_keywords:
                extracted_keywords = await self.seo_optimizer.extract_seo_keywords(
                    content=article_content,
                    title="",
                    topic=topic
                )
                seo_keywords = [kw['keyword'] for kw in extracted_keywords[:10]]

            # Generate SEO-optimized title
            seo_title = await self._generate_seo_title(topic, seo_keywords)

            # Generate meta description
            meta_description = await self._generate_meta_description(article_content, seo_keywords)

            # Perform SEO optimization analysis
            seo_analysis = await self.seo_optimizer.optimize_content_for_seo(
                content=article_content,
                target_keywords=seo_keywords,
                title=seo_title
            )
            
            # Calculate article metrics
            word_count = len(article_content.split())
            reading_time = max(1, word_count // 200)  # Assuming 200 WPM
            
            result = {
                "success": True,
                "article": {
                    "title": seo_title,
                    "content": article_content,
                    "meta_description": meta_description,
                    "seo_keywords": seo_keywords or [],
                    "article_type": article_type,
                    "language": target_language,
                    "word_count": word_count,
                    "reading_time_minutes": reading_time,
                    "egyptian_dialect_applied": self.egyptian_dialect_enabled,
                    "spelling_errors_added": self.add_spelling_errors,
                    "seo_analysis": seo_analysis
                },
                "generation_metadata": {
                    "model_used": self.model_name,
                    "processing_time": time.time() - start_time,
                    "research_sources": len(research_data.get('sources', [])),
                    "topic": topic
                }
            }
            
            logger.info(f"Article generated successfully in {result['generation_metadata']['processing_time']:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Article generation failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "processing_time": time.time() - start_time
            }

    def _prepare_research_context(self, research_data: Dict[str, Any]) -> str:
        """Prepare research context for article generation"""
        try:
            context_parts = []

            # Add main article content
            if research_data.get('selected_article'):
                article = research_data['selected_article']
                original = article.get('original_article', {})

                context_parts.append(f"المقال الأساسي: {original.get('title', '')}")
                context_parts.append(f"المحتوى: {original.get('content', '')[:3000]}")

                # Add deep analysis
                deep_analysis = article.get('deep_analysis', {})
                if deep_analysis.get('comprehensive_summary'):
                    context_parts.append(f"الملخص الشامل: {deep_analysis['comprehensive_summary']}")

                # Add related topics
                related_topics = article.get('related_topics', [])
                if related_topics:
                    context_parts.append(f"المواضيع ذات الصلة: {', '.join(related_topics)}")

            return "\n\n".join(context_parts)

        except Exception as e:
            logger.error(f"Failed to prepare research context: {str(e)}")
            return ""

    async def _generate_article_content(
        self,
        topic: str,
        context: str,
        article_type: str,
        target_language: str,
        dialect: str,
        seo_keywords: List[str],
        min_words: int,
        max_words: int
    ) -> str:
        """Generate the main article content"""
        try:
            # Prepare the prompt based on article type and language
            style_instructions = self._get_style_instructions(article_type)
            language_instructions = self.language_manager.get_language_prompt(target_language, dialect)

            # Prepare SEO keywords instruction
            seo_instruction = ""
            if seo_keywords:
                if target_language == 'ar':
                    seo_instruction = f"استخدم هذه الكلمات المفتاحية بشكل طبيعي: {', '.join(seo_keywords[:10])}"
                else:
                    seo_instruction = f"Use these keywords naturally: {', '.join(seo_keywords[:10])}"

            # Create language-specific prompt
            if target_language == 'ar':
                prompt = f"""
{language_instructions} حول الموضوع التالي:

الموضوع: {topic}

نوع المقال: {style_instructions}

{seo_instruction}

معلومات البحث المتاحة:
{context}

متطلبات المقال:
- عدد الكلمات: بين {min_words} و {max_words} كلمة
- يجب أن يكون المقال مفيداً وغنياً بالمعلومات
- استخدم عناوين فرعية واضحة
- اكتب بأسلوب جذاب ومقروء
- تأكد من دقة المعلومات
- اجعل المقال يتصدر نتائج البحث في جوجل

هيكل المقال المطلوب:
1. مقدمة جذابة
2. عدة أقسام رئيسية مع عناوين فرعية
3. خاتمة مفيدة

ابدأ الكتابة الآن:
"""
            else:
                prompt = f"""
{language_instructions} about the following topic:

Topic: {topic}

Article Type: {style_instructions}

{seo_instruction}

Available Research Information:
{context}

Article Requirements:
- Word count: between {min_words} and {max_words} words
- The article should be useful and rich in information
- Use clear subheadings
- Write in an engaging and readable style
- Ensure information accuracy
- Make the article rank high in Google search results

Required Article Structure:
1. Engaging introduction
2. Several main sections with subheadings
3. Useful conclusion

Start writing now:
"""

            # Generate content using Gemini
            generation_config = genai.types.GenerationConfig(
                temperature=0.7,
                max_output_tokens=3000,
                top_p=0.8,
                top_k=40
            )

            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.client.generate_content(
                    prompt,
                    generation_config=generation_config
                )
            )

            if response and response.text:
                return response.text.strip()
            else:
                logger.error("Empty response from Gemini")
                return ""

        except Exception as e:
            logger.error(f"Failed to generate article content: {str(e)}")
            return ""

    def _get_style_instructions(self, article_type: str) -> str:
        """Get style instructions based on article type"""
        styles = {
            "professional": "مقال مهني ورسمي بأسلوب أكاديمي متقن",
            "casual": "مقال غير رسمي بأسلوب ودود ومريح",
            "news": "مقال إخباري بأسلوب صحفي واضح ومباشر",
            "analysis": "مقال تحليلي عميق بأسلوب نقدي ومدروس",
            "tutorial": "مقال تعليمي بأسلوب شرح مبسط وواضح",
            "review": "مقال مراجعة بأسلوب تقييمي موضوعي"
        }

        return styles.get(article_type, styles["professional"])

    async def _generate_seo_title(self, topic: str, seo_keywords: List[str]) -> str:
        """Generate SEO-optimized title"""
        try:
            keywords_text = ""
            if seo_keywords:
                keywords_text = f"استخدم هذه الكلمات: {', '.join(seo_keywords[:3])}"

            prompt = f"""
اكتب عنواناً جذاباً ومحسناً لمحركات البحث للموضوع التالي:

الموضوع: {topic}
{keywords_text}

المتطلبات:
- العنوان يجب أن يكون جذاباً ويلفت الانتباه
- يحتوي على كلمات مفتاحية مهمة
- طوله بين 50-60 حرف
- يشجع على النقر
- باللغة العربية

اكتب العنوان فقط:
"""

            generation_config = genai.types.GenerationConfig(
                temperature=0.8,
                max_output_tokens=100,
                top_p=0.9
            )

            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.client.generate_content(
                    prompt,
                    generation_config=generation_config
                )
            )

            if response and response.text:
                return response.text.strip()
            else:
                return f"دليل شامل حول {topic}"

        except Exception as e:
            logger.error(f"Failed to generate SEO title: {str(e)}")
            return f"كل ما تريد معرفته عن {topic}"

    async def _generate_meta_description(self, content: str, seo_keywords: List[str]) -> str:
        """Generate meta description for SEO"""
        try:
            keywords_text = ""
            if seo_keywords:
                keywords_text = f"استخدم هذه الكلمات: {', '.join(seo_keywords[:5])}"

            # Use first paragraph or summary
            content_preview = content[:500]

            prompt = f"""
اكتب وصفاً تعريفياً (meta description) محسناً لمحركات البحث بناءً على المحتوى التالي:

المحتوى: {content_preview}...
{keywords_text}

المتطلبات:
- طول الوصف بين 150-160 حرف
- يحتوي على كلمات مفتاحية
- يلخص المحتوى بشكل جذاب
- يشجع على النقر
- باللغة العربية

اكتب الوصف فقط:
"""

            generation_config = genai.types.GenerationConfig(
                temperature=0.6,
                max_output_tokens=100,
                top_p=0.8
            )

            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.client.generate_content(
                    prompt,
                    generation_config=generation_config
                )
            )

            if response and response.text:
                return response.text.strip()
            else:
                return content[:150] + "..."

        except Exception as e:
            logger.error(f"Failed to generate meta description: {str(e)}")
            return content[:150] + "..."

    def _apply_egyptian_dialect(self, content: str) -> str:
        """Apply Egyptian dialect to the content"""
        try:
            modified_content = content

            # Apply Egyptian replacements
            for formal, egyptian in self.egyptian_replacements.items():
                # Use word boundaries to avoid partial replacements
                pattern = r'\b' + re.escape(formal) + r'\b'
                modified_content = re.sub(pattern, egyptian, modified_content)

            logger.info("Applied Egyptian dialect to article")
            return modified_content

        except Exception as e:
            logger.error(f"Failed to apply Egyptian dialect: {str(e)}")
            return content

    def _add_spelling_errors(self, content: str) -> str:
        """Add subtle spelling errors to make content appear more human"""
        try:
            modified_content = content
            error_count = 0
            max_errors = max(1, len(content.split()) // 100)  # 1 error per 100 words max

            # Apply spelling errors sparingly and naturally
            for correct, error in self.spelling_errors.items():
                if error_count >= max_errors:
                    break

                # Only replace some instances (25% chance for more subtlety)
                if random.random() < 0.25:
                    pattern = r'\b' + re.escape(correct) + r'\b'
                    matches = re.findall(pattern, modified_content)

                    if matches:
                        # Only replace first occurrence to keep it natural
                        modified_content = re.sub(pattern, error, modified_content, count=1)
                        error_count += 1

            # Add some natural Arabic typing variations
            natural_variations = {
                'ة': 'ه',  # Common ta marbuta confusion
                'ي': 'ى',  # Alif maksura variation
                'أ': 'ا',  # Hamza variations
                'إ': 'ا',
                'ؤ': 'و',
                'ئ': 'ي'
            }

            # Apply very few natural variations (1-2 max)
            variation_count = 0
            max_variations = 2

            for original, variation in natural_variations.items():
                if variation_count >= max_variations:
                    break

                if random.random() < 0.1:  # Very low chance
                    # Find words ending with the character
                    pattern = r'\b\w*' + re.escape(original) + r'\b'
                    matches = re.findall(pattern, modified_content)

                    if matches and len(matches[0]) > 3:  # Only for longer words
                        word_to_replace = matches[0]
                        new_word = word_to_replace.replace(original, variation)
                        modified_content = modified_content.replace(word_to_replace, new_word, 1)
                        variation_count += 1

            total_changes = error_count + variation_count
            logger.info(f"Added {total_changes} subtle spelling variations ({error_count} errors, {variation_count} natural variations)")
            return modified_content

        except Exception as e:
            logger.error(f"Failed to add spelling errors: {str(e)}")
            return content

    def get_status(self) -> Dict[str, Any]:
        """Get current status of the article generator"""
        return {
            "configured": self.is_configured(),
            "api_key_set": bool(self.api_key),
            "model": self.model_name,
            "egyptian_dialect_enabled": self.egyptian_dialect_enabled,
            "spelling_errors_enabled": self.add_spelling_errors,
            "seo_optimization_enabled": self.seo_optimization,
            "available_models": self.get_available_models()
        }

    async def test_generation(self) -> Dict[str, Any]:
        """Test article generation with a simple example"""
        if not self.is_configured():
            return {
                "success": False,
                "error": "Gemini is not configured"
            }

        try:
            test_data = {
                "selected_article": {
                    "original_article": {
                        "title": "اختبار النظام",
                        "content": "هذا اختبار بسيط لنظام إنتاج المقالات"
                    },
                    "deep_analysis": {
                        "comprehensive_summary": "اختبار النظام"
                    }
                }
            }

            result = await self.generate_article(
                topic="اختبار النظام",
                research_data=test_data,
                article_type="professional",
                min_words=100,
                max_words=200
            )

            return result

        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
