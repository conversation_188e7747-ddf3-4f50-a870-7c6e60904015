#!/usr/bin/env python3
"""
Setup script for DeepSearch Agent
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command, check=True):
    """Run a shell command"""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, check=check)
    return result.returncode == 0


def create_directories():
    """Create necessary directories"""
    directories = [
        "data",
        "logs",
        "data/vector_db"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")


def setup_environment():
    """Setup Python environment"""
    print("Setting up Python environment...")
    
    # Check Python version
    if sys.version_info < (3, 9):
        print("Error: Python 3.9 or higher is required")
        sys.exit(1)
    
    print(f"Python version: {sys.version}")
    
    # Install requirements
    if os.path.exists("requirements.txt"):
        print("Installing Python dependencies...")
        if not run_command("pip install -r requirements.txt"):
            print("Failed to install dependencies")
            sys.exit(1)
    else:
        print("Warning: requirements.txt not found")


def setup_config():
    """Setup configuration files"""
    print("Setting up configuration...")
    
    # Copy .env.example to .env if it doesn't exist
    if not os.path.exists(".env") and os.path.exists(".env.example"):
        shutil.copy(".env.example", ".env")
        print("Created .env file from .env.example")
        print("Please edit .env file with your API keys")
    
    # Check config file
    if not os.path.exists("config/config.yaml"):
        print("Warning: config/config.yaml not found")
        print("Using default configuration")


def check_ollama():
    """Check if Ollama is available"""
    print("Checking Ollama availability...")
    
    if run_command("ollama --version", check=False):
        print("Ollama is available")
        
        # Check if mistral model is available
        result = subprocess.run("ollama list", shell=True, capture_output=True, text=True)
        if "mistral" in result.stdout:
            print("Mistral model is available")
        else:
            print("Mistral model not found. You can install it with:")
            print("  ollama pull mistral")
    else:
        print("Ollama not found. Please install Ollama from https://ollama.ai")
        print("Or set up alternative AI provider in configuration")


def run_tests():
    """Run basic tests"""
    print("Running basic tests...")
    
    if os.path.exists("tests"):
        # Run only unit tests, skip integration tests
        if run_command("python -m pytest tests/ -m 'not integration' --tb=short", check=False):
            print("Basic tests passed")
        else:
            print("Some tests failed, but setup can continue")
    else:
        print("No tests directory found")


def main():
    """Main setup function"""
    print("🔍 DeepSearch Agent Setup")
    print("=" * 50)
    
    try:
        # Create directories
        create_directories()
        
        # Setup Python environment
        setup_environment()
        
        # Setup configuration
        setup_config()
        
        # Check Ollama
        check_ollama()
        
        # Run tests
        run_tests()
        
        print("\n✅ Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Edit .env file with your API keys")
        print("2. Install Ollama and pull mistral model (optional)")
        print("3. Run the application:")
        print("   python main.py --help")
        print("   python main.py --api")
        print("   python main.py --query 'your search query'")
        
    except KeyboardInterrupt:
        print("\nSetup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nSetup failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
