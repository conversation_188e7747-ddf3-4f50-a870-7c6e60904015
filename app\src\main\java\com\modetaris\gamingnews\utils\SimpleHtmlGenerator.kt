package com.modetaris.gamingnews.utils

import com.modetaris.gamingnews.data.model.SimpleArticle

object SimpleHtmlGenerator {
    
    fun generateHtml(article: SimpleArticle): String {
        return """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${article.title}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.8;
            margin: 40px;
            background-color: #f8f9fa;
            color: #333;
        }
        
        .article {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 40px;
        }
        
        h1 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        
        p {
            margin-bottom: 20px;
            text-align: justify;
        }
        
        .stats {
            background: #f8f9ff;
            padding: 15px;
            border-radius: 8px;
            margin-top: 30px;
            text-align: center;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="article">
        <h1>${article.title}</h1>
        <div>${article.content.replace("\n", "<br>")}</div>
        <div class="stats">
            📊 ${article.wordCount} كلمة | ⏱️ ${article.readingTime} | ⭐ ${article.qualityScore} نقطة
        </div>
    </div>
</body>
</html>
        """.trimIndent()
    }
}
