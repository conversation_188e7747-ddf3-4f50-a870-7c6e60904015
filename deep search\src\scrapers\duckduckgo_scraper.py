"""
DuckDuckGo search scraper implementation
"""

import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from loguru import logger
from duckduckgo_search import DDGS
from .base_scraper import BaseScraper


class DuckDuckGoScraper(BaseScraper):
    """Scraper using DuckDuckGo search"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.ddgs = DDGS()
    
    async def search_duckduckgo(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Search using DuckDuckGo
        
        Args:
            query: Search query
            max_results: Maximum number of results
            
        Returns:
            List of search results
        """
        try:
            # Run DuckDuckGo search in thread pool since it's synchronous
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(
                None, 
                lambda: list(self.ddgs.text(
                    query, 
                    region='wt-wt',  # Worldwide
                    safesearch='moderate',
                    timelimit='w',  # Past week
                    max_results=max_results
                ))
            )
            
            logger.info(f"DuckDuckGo search returned {len(results)} results for: {query}")
            return results
            
        except Exception as e:
            logger.error(f"DuckDuckGo search failed: {str(e)}")
            return []
    
    async def scrape_article(self, url: str, title: str = "", snippet: str = "") -> Optional[Dict[str, Any]]:
        """
        Scrape a single article from URL
        
        Args:
            url: Article URL
            title: Article title from search results
            snippet: Article snippet from search results
            
        Returns:
            Article data or None if failed
        """
        try:
            html_content = await self.fetch_page(url)
            if not html_content:
                # If scraping fails, return basic info from search results
                if title and snippet:
                    return {
                        'url': url,
                        'title': title,
                        'content': snippet,
                        'description': snippet,
                        'author': '',
                        'language': 'ar',
                        'keywords': [],
                        'scraped_at': datetime.now().isoformat(),
                        'source': 'search_snippet'
                    }
                return None
            
            soup = self.clean_html(html_content)
            text_content = self.extract_text(soup)
            metadata = self.extract_metadata(soup, url)
            
            # Use search result title if page title is empty
            if not metadata['title'] and title:
                metadata['title'] = title
            
            # Limit content length
            max_length = self.config.get('max_content_length', 10000)
            if len(text_content) > max_length:
                text_content = text_content[:max_length] + "..."
            
            # If content is too short, use snippet as fallback
            if len(text_content.strip()) < 100 and snippet:
                text_content = snippet
            
            return {
                'url': url,
                'title': metadata['title'],
                'content': text_content,
                'description': metadata['description'] or snippet,
                'author': metadata['author'],
                'language': metadata['language'],
                'keywords': metadata['keywords'],
                'scraped_at': datetime.now().isoformat(),
                'source': 'full_scrape'
            }
            
        except Exception as e:
            logger.error(f"Failed to scrape article {url}: {str(e)}")
            # Return basic info from search results as fallback
            if title and snippet:
                return {
                    'url': url,
                    'title': title,
                    'content': snippet,
                    'description': snippet,
                    'author': '',
                    'language': 'ar',
                    'keywords': [],
                    'scraped_at': datetime.now().isoformat(),
                    'source': 'search_snippet'
                }
            return None
    
    async def scrape(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Scrape articles using DuckDuckGo search
        
        Args:
            query: Search query
            max_results: Maximum number of results
            
        Returns:
            List of scraped articles
        """
        logger.info(f"Starting DuckDuckGo search for: {query}")
        
        # Search using DuckDuckGo
        search_results = await self.search_duckduckgo(query, max_results)
        if not search_results:
            logger.warning("No search results from DuckDuckGo")
            return []
        
        # Extract URLs and metadata to scrape
        scrape_tasks = []
        for result in search_results[:max_results]:
            url = result.get('href')
            title = result.get('title', '')
            snippet = result.get('body', '')
            
            if url and self._is_valid_url(url):
                scrape_tasks.append((url, title, snippet))
        
        logger.info(f"Found {len(scrape_tasks)} URLs to scrape")
        
        # Scrape articles concurrently
        semaphore = asyncio.Semaphore(self.config.get('concurrent_requests', 5))
        
        async def scrape_with_semaphore(url_data):
            url, title, snippet = url_data
            async with semaphore:
                return await self.scrape_article(url, title, snippet)
        
        tasks = [scrape_with_semaphore(url_data) for url_data in scrape_tasks]
        scraped_articles = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter successful results
        successful_articles = []
        for article in scraped_articles:
            if isinstance(article, dict) and article:
                successful_articles.append(article)
        
        logger.info(f"Successfully scraped {len(successful_articles)} articles")
        return successful_articles
    
    def _is_valid_url(self, url: str) -> bool:
        """
        Check if URL is valid for scraping
        
        Args:
            url: URL to check
            
        Returns:
            True if URL is valid for scraping
        """
        # Skip certain file types and domains
        skip_extensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']
        skip_domains = ['youtube.com', 'facebook.com', 'twitter.com', 'instagram.com']
        
        url_lower = url.lower()
        
        # Check file extensions
        for ext in skip_extensions:
            if url_lower.endswith(ext):
                return False
        
        # Check domains
        for domain in skip_domains:
            if domain in url_lower:
                return False
        
        return True
