// DeepSearch News Agent JavaScript

const API_BASE_URL = 'http://localhost:8000';

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Add enter key support
    document.getElementById('newsQuery').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchNews();
        }
    });
    
    // Load trending news on startup
    searchTrending();
});

// Search news
async function searchNews() {
    const query = document.getElementById('newsQuery').value.trim();
    if (!query) {
        showError('يرجى إدخال استعلام البحث');
        return;
    }
    
    const maxResults = parseInt(document.getElementById('maxResults').value);
    const hoursBack = parseInt(document.getElementById('hoursBack').value);
    const category = document.getElementById('category').value;
    
    showLoading();
    hideError();
    hideResults();
    
    try {
        const response = await fetch(`${API_BASE_URL}/news/search`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                query: query,
                max_results: maxResults,
                hours_back: hoursBack,
                category: category || null
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        showNewsResults(result);
        
    } catch (error) {
        console.error('News search error:', error);
        showError('حدث خطأ أثناء البحث عن الأخبار: ' + error.message);
    } finally {
        hideLoading();
    }
}

// Search by category
async function searchByCategory(categoryType) {
    // Update active button
    document.querySelectorAll('.btn-category').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    showLoading();
    hideError();
    hideResults();
    
    try {
        const response = await fetch(`${API_BASE_URL}/news/${categoryType}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                max_results: parseInt(document.getElementById('maxResults').value)
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        showNewsResults(result);
        
    } catch (error) {
        console.error('Category search error:', error);
        showError('حدث خطأ أثناء البحث: ' + error.message);
    } finally {
        hideLoading();
    }
}

// Search trending news
async function searchTrending() {
    showLoading();
    hideError();
    hideResults();
    
    try {
        const response = await fetch(`${API_BASE_URL}/news/trending?limit=10`);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        showTrendingResults(result);
        
    } catch (error) {
        console.error('Trending search error:', error);
        showError('حدث خطأ أثناء جلب الأخبار الرائجة: ' + error.message);
    } finally {
        hideLoading();
    }
}

// Show news results
function showNewsResults(result) {
    showResults();
    
    // Update search info
    const searchInfo = document.getElementById('searchInfo');
    searchInfo.innerHTML = `
        <small>
            <i class="fas fa-clock"></i> ${new Date(result.search_time).toLocaleString('ar')} |
            <i class="fas fa-list"></i> ${result.total_results} خبر |
            <i class="fas fa-stopwatch"></i> ${result.processing_time.toFixed(2)}s
        </small>
    `;
    
    // Show articles
    const container = document.getElementById('newsContainer');
    container.innerHTML = '';
    
    if (result.news_articles && result.news_articles.length > 0) {
        result.news_articles.forEach((article, index) => {
            const newsCard = createNewsCard(article, index + 1);
            container.appendChild(newsCard);
        });
    } else {
        container.innerHTML = '<div class="alert alert-info">لم يتم العثور على أخبار حديثة</div>';
    }
    
    // Show stats
    showNewsStats(result);
}

// Show trending results
function showTrendingResults(result) {
    showResults();
    
    // Update search info
    const searchInfo = document.getElementById('searchInfo');
    searchInfo.innerHTML = `
        <small>
            <i class="fas fa-fire"></i> الأخبار الرائجة |
            <i class="fas fa-clock"></i> ${new Date(result.generated_at).toLocaleString('ar')}
        </small>
    `;
    
    // Show trending topics
    const container = document.getElementById('newsContainer');
    container.innerHTML = `
        <div class="card mb-4">
            <div class="card-header">
                <h6><i class="fas fa-fire"></i> الموضوعات الرائجة</h6>
            </div>
            <div class="card-body">
                ${result.trending_topics.map(topic => 
                    `<span class="badge bg-danger me-2 mb-2">${topic}</span>`
                ).join('')}
            </div>
        </div>
    `;
    
    // Show top articles
    if (result.top_articles && result.top_articles.length > 0) {
        result.top_articles.forEach((article, index) => {
            const newsCard = createNewsCard(article, index + 1);
            container.appendChild(newsCard);
        });
    }
}

// Create news card
function createNewsCard(article, index) {
    const card = document.createElement('div');
    const isBreaking = article.news_type === 'breaking';
    
    card.className = `news-card ${isBreaking ? 'breaking-news' : ''}`;
    
    // Format date
    const articleDate = article.extracted_date || 
                       (article.scraped_at ? new Date(article.scraped_at).toLocaleDateString('ar') : 'غير محدد');
    
    card.innerHTML = `
        <div class="d-flex justify-content-between align-items-start mb-2">
            <div class="news-meta">
                <span class="badge bg-secondary me-2">${index}</span>
                <i class="fas fa-calendar"></i> ${articleDate} |
                <i class="fas fa-globe"></i> ${article.source_engine} |
                <span class="news-score">${article.news_score.toFixed(1)}</span>
                ${isBreaking ? '<span class="breaking-badge ms-2">عاجل</span>' : ''}
            </div>
        </div>
        
        <h6 class="fw-bold mb-2">${article.title || 'بدون عنوان'}</h6>
        
        <div class="mb-3">
            <h6 class="text-primary"><i class="fas fa-file-alt"></i> الملخص الإخباري:</h6>
            <p class="mb-2">${article.news_summary || article.content?.substring(0, 300) + '...' || 'لا يوجد ملخص'}</p>
        </div>
        
        ${article.key_entities && article.key_entities.length > 0 ? `
            <div class="mb-2">
                <small class="text-muted">الكيانات المستخرجة:</small><br>
                ${article.key_entities.map(entity => 
                    `<span class="entity-tag">${entity}</span>`
                ).join('')}
            </div>
        ` : ''}
        
        <div class="d-flex justify-content-between align-items-center">
            <a href="${article.url}" target="_blank" class="btn btn-outline-danger btn-sm">
                <i class="fas fa-external-link-alt"></i> قراءة المقال كاملاً
            </a>
            <div>
                <button onclick="copyArticle('${article.url}')" class="btn btn-outline-secondary btn-sm me-2">
                    <i class="fas fa-copy"></i> نسخ الرابط
                </button>
                <button onclick="shareArticle('${article.title}', '${article.url}')" class="btn btn-outline-info btn-sm">
                    <i class="fas fa-share"></i> مشاركة
                </button>
            </div>
        </div>
        
        ${article.content ? `
            <div class="mt-3">
                <details>
                    <summary class="text-muted" style="cursor: pointer;">
                        <small><i class="fas fa-eye"></i> عرض المحتوى الكامل</small>
                    </summary>
                    <div class="mt-2 p-3 bg-light rounded">
                        <small>${article.content}</small>
                    </div>
                </details>
            </div>
        ` : ''}
    `;
    
    return card;
}

// Show news stats
function showNewsStats(result) {
    const statsContainer = document.getElementById('statsContainer');
    statsContainer.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-chart-bar"></i> إحصائيات البحث الإخباري</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-danger">${result.total_results}</h4>
                            <small>أخبار وجدت</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info">${result.processing_time.toFixed(2)}s</h4>
                            <small>وقت المعالجة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success">${result.category}</h4>
                            <small>الفئة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning">${result.sources_used.length}</h4>
                            <small>مصادر مستخدمة</small>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>تفاصيل البحث:</h6>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between">
                            <span>الاستعلام الأصلي:</span>
                            <strong>${result.query}</strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>الاستعلام المحسن:</span>
                            <strong>${result.enhanced_query}</strong>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>المصادر:</span>
                            <strong>${result.sources_used.join(', ')}</strong>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    `;
}

// Copy article URL
function copyArticle(url) {
    navigator.clipboard.writeText(url).then(() => {
        // Show temporary success message
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
        btn.classList.add('btn-success');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
        }, 2000);
    });
}

// Share article
function shareArticle(title, url) {
    if (navigator.share) {
        navigator.share({
            title: title,
            url: url
        });
    } else {
        // Fallback: copy to clipboard
        const shareText = `${title}\n${url}`;
        navigator.clipboard.writeText(shareText);
        alert('تم نسخ معلومات المقال للحافظة');
    }
}

// Utility functions
function showLoading() { document.getElementById('loading').style.display = 'block'; }
function hideLoading() { document.getElementById('loading').style.display = 'none'; }
function showResults() { document.getElementById('results').style.display = 'block'; }
function hideResults() { document.getElementById('results').style.display = 'none'; }
function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('error').style.display = 'block';
}
function hideError() { document.getElementById('error').style.display = 'none'; }
