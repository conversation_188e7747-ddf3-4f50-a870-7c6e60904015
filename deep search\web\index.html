<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSearch Agent - واجهة الاختبار</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }
        
        .search-box {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .search-input {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-search {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-search:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .result-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        
        .result-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .stats-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .provider-badge {
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .sentiment-positive { color: #28a745; }
        .sentiment-negative { color: #dc3545; }
        .sentiment-neutral { color: #6c757d; }
        
        .tab-content {
            margin-top: 20px;
        }
        
        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            background: #f8f9fa;
            color: #495057;
            margin-left: 5px;
        }
        
        .nav-tabs .nav-link.active {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="text-center mb-4">
                <h1 class="display-4 text-primary">
                    <i class="fas fa-search"></i>
                    DeepSearch Agent
                </h1>
                <p class="lead text-muted">أداة البحث الذكية المتقدمة للوكلاء البرمجيين</p>
                <div class="mt-3">
                    <a href="admin.html" class="btn btn-outline-secondary">
                        <i class="fas fa-cogs"></i> لوحة الإدارة
                    </a>
                </div>
            </div>

            <!-- Search Box -->
            <div class="search-box">
                <div class="row">
                    <div class="col-md-8">
                        <input type="text" id="searchQuery" class="form-control search-input" 
                               placeholder="ابحث عن أي شيء... (مثال: آخر أخبار الذكاء الاصطناعي)">
                    </div>
                    <div class="col-md-4">
                        <button onclick="performSearch()" class="btn btn-search w-100">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>
                
                <!-- Options -->
                <div class="row mt-3">
                    <div class="col-md-3">
                        <select id="maxResults" class="form-select">
                            <option value="5">5 نتائج</option>
                            <option value="10" selected>10 نتائج</option>
                            <option value="20">20 نتيجة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select id="searchType" class="form-select">
                            <option value="search">بحث عادي</option>
                            <option value="rag">بحث RAG</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="summarize" checked>
                            <label class="form-check-label" for="summarize">
                                تلخيص المقالات
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <button onclick="checkHealth()" class="btn btn-outline-primary">
                            <i class="fas fa-heartbeat"></i> فحص الصحة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Loading -->
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <h5>جاري البحث...</h5>
                <p class="text-muted">يرجى الانتظار بينما نجمع أفضل النتائج لك</p>
            </div>

            <!-- Results -->
            <div id="results" style="display: none;">
                <!-- Tabs -->
                <ul class="nav nav-tabs" id="resultTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="articles-tab" data-bs-toggle="tab" 
                                data-bs-target="#articles" type="button" role="tab">
                            <i class="fas fa-newspaper"></i> المقالات
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="stats-tab" data-bs-toggle="tab" 
                                data-bs-target="#stats" type="button" role="tab">
                            <i class="fas fa-chart-bar"></i> الإحصائيات
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="rag-tab" data-bs-toggle="tab" 
                                data-bs-target="#rag" type="button" role="tab" style="display: none;">
                            <i class="fas fa-robot"></i> إجابة RAG
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="resultTabContent">
                    <!-- Articles Tab -->
                    <div class="tab-pane fade show active" id="articles" role="tabpanel">
                        <div id="articlesContainer"></div>
                    </div>

                    <!-- Stats Tab -->
                    <div class="tab-pane fade" id="stats" role="tabpanel">
                        <div id="statsContainer"></div>
                    </div>

                    <!-- RAG Tab -->
                    <div class="tab-pane fade" id="rag" role="tabpanel">
                        <div id="ragContainer"></div>
                    </div>
                </div>
            </div>

            <!-- Error -->
            <div id="error" class="alert alert-danger" style="display: none;">
                <h5><i class="fas fa-exclamation-triangle"></i> خطأ</h5>
                <p id="errorMessage"></p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
