#!/usr/bin/env python3
"""
Simple server starter for DeepSearch Agent
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    print("🚀 Starting DeepSearch Agent Server...")
    print("Loading configuration...")
    
    from src.utils.config_loader import load_config
    config = load_config()
    print("✅ Configuration loaded")
    
    print("Setting up logging...")
    from src.utils.logger import setup_logging
    setup_logging(config)
    print("✅ Logging configured")
    
    print("Starting FastAPI server...")
    import uvicorn
    from src.api.main import app
    
    # Get API configuration
    api_config = config.get('api', {})
    host = api_config.get('host', '0.0.0.0')
    port = api_config.get('port', 8000)
    
    print(f"🌐 Server will start on http://{host}:{port}")
    print(f"📱 Web Interface: http://localhost:{port}/web/index.html")
    print(f"⚙️ Admin Panel: http://localhost:{port}/web/admin.html")
    print(f"📚 API Docs: http://localhost:{port}/docs")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 50)
    
    # Start server
    uvicorn.run(
        app,
        host=host,
        port=port,
        reload=False,
        log_level="info"
    )
    
except KeyboardInterrupt:
    print("\n⏹️ Server stopped by user")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install required packages:")
    print("pip install fastapi uvicorn loguru pydantic")
except Exception as e:
    print(f"❌ Error starting server: {e}")
    import traceback
    traceback.print_exc()
