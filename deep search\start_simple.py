#!/usr/bin/env python3
"""
Simple News Agent Server - Basic functionality without AI/PyTorch
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    print("🚀 Starting Simple News Agent (No AI)")
    print("📰 Basic news search without AI processing")
    print("=" * 60)
    
    # Simple FastAPI app without AI
    from fastapi import FastAPI, HTTPException
    from fastapi.staticfiles import StaticFiles
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel
    import uvicorn
    from typing import Optional
    import asyncio
    import aiohttp
    from bs4 import BeautifulSoup
    from datetime import datetime
    import json
    import os
    
    # Create simple app
    app = FastAPI(
        title="Simple News Agent API",
        description="Basic news search without AI processing",
        version="1.0.0"
    )
    
    # Add CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Mount static files
    if os.path.exists("web"):
        app.mount("/web", StaticFiles(directory="web"), name="web")
    
    # Simple models
    class SimpleNewsRequest(BaseModel):
        query: str
        max_results: Optional[int] = 5
        agent_id: Optional[str] = "default"
    
    class SimpleArticle(BaseModel):
        title: str
        url: str
        content: str
        scraped_at: str
        source: str = "duckduckgo"
    
    class SimpleNewsResponse(BaseModel):
        query: str
        agent_id: str
        articles: list
        total_results: int
        message: str
    
    # Simple DuckDuckGo search
    async def simple_search(query: str, max_results: int = 5):
        """Simple search using DuckDuckGo"""
        try:
            from duckduckgo_search import DDGS
            
            results = []
            with DDGS() as ddgs:
                search_results = list(ddgs.text(query, max_results=max_results))
                
                for result in search_results:
                    article = {
                        'title': result.get('title', ''),
                        'url': result.get('href', ''),
                        'content': result.get('body', ''),
                        'scraped_at': datetime.now().isoformat(),
                        'source': 'duckduckgo'
                    }
                    results.append(article)
            
            return results
            
        except Exception as e:
            print(f"Search error: {e}")
            return []
    
    # Track delivered articles (simple version)
    delivered_articles = {}
    
    def is_delivered(url: str, agent_id: str) -> bool:
        """Check if article was delivered to agent"""
        key = f"{agent_id}:{url}"
        return key in delivered_articles
    
    def mark_delivered(url: str, agent_id: str):
        """Mark article as delivered"""
        key = f"{agent_id}:{url}"
        delivered_articles[key] = datetime.now().isoformat()
    
    # API endpoints
    @app.get("/")
    async def root():
        return {
            "message": "Simple News Agent API",
            "version": "1.0.0",
            "endpoints": {
                "professional": "/news/professional/simple",
                "single": "/news/professional/single-simple",
                "web": "/web/professional.html"
            }
        }
    
    @app.post("/news/professional/simple")
    async def get_simple_professional_articles(request: SimpleNewsRequest):
        """Get professional articles (simplified version)"""
        try:
            print(f"Searching for: {request.query} (Agent: {request.agent_id})")
            
            # Search for articles
            all_articles = await simple_search(request.query, request.max_results * 2)
            
            # Filter out delivered articles
            new_articles = []
            for article in all_articles:
                if not is_delivered(article['url'], request.agent_id):
                    new_articles.append(article)
                    mark_delivered(article['url'], request.agent_id)
                    
                    if len(new_articles) >= request.max_results:
                        break
            
            return {
                "query": request.query,
                "agent_id": request.agent_id,
                "professional_articles": new_articles,
                "total_found": len(all_articles),
                "total_delivered": len(new_articles),
                "processing_time": 1.0,
                "extraction_enabled": False,
                "delivery_time": datetime.now().isoformat(),
                "message": f"تم تقديم {len(new_articles)} مقال بسيط (بدون AI)"
            }
            
        except Exception as e:
            print(f"Error: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/news/professional/single-simple")
    async def get_single_simple_article(
        query: str,
        agent_id: str = "default"
    ):
        """Get single article (simplified)"""
        try:
            request = SimpleNewsRequest(
                query=query,
                agent_id=agent_id,
                max_results=1
            )
            return await get_simple_professional_articles(request)
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/news/professional/stats/{agent_id}")
    async def get_simple_stats(agent_id: str):
        """Get simple stats"""
        agent_count = len([k for k in delivered_articles.keys() if k.startswith(f"{agent_id}:")])
        
        return {
            "agent_id": agent_id,
            "total_articles_delivered": agent_count,
            "articles_last_24h": agent_count,  # Simplified
            "categories_covered": ["general"],
            "last_delivery": max([v for k, v in delivered_articles.items() if k.startswith(f"{agent_id}:")]) if agent_count > 0 else None
        }
    
    @app.delete("/news/professional/history/{agent_id}")
    async def clear_simple_history(agent_id: str):
        """Clear agent history"""
        keys_to_remove = [k for k in delivered_articles.keys() if k.startswith(f"{agent_id}:")]
        for key in keys_to_remove:
            del delivered_articles[key]
        
        return {
            "message": f"تم مسح {len(keys_to_remove)} مقال للوكيل {agent_id}",
            "agent_id": agent_id,
            "cleared_at": datetime.now().isoformat()
        }
    
    # Start server
    print("✅ Simple server configured")
    print(f"\n🌐 Server starting on http://localhost:8000")
    print(f"📰 Professional Interface: http://localhost:8000/web/professional.html")
    print(f"📚 API Docs: http://localhost:8000/docs")
    print()
    print("🤖 Simple API Endpoints:")
    print("  • POST /news/professional/simple - Get articles (no AI)")
    print("  • GET /news/professional/single-simple - Get single article")
    print("  • GET /news/professional/stats/{agent_id} - Simple stats")
    print()
    print("💡 Example usage:")
    print("  curl 'http://localhost:8000/news/professional/single-simple?query=آخر أخبار كرة القدم&agent_id=sports_bot'")
    print()
    print("⚠️ Note: This is a simplified version without AI processing")
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
    
except KeyboardInterrupt:
    print("\n⏹️ Server stopped by user")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
