@echo off
title DeepSearch Agent Pro - Local Server

echo.
echo ========================================
echo   DeepSearch Agent Pro - Quick Start
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo Python found. Starting server...
echo.

REM Run the local server
python run_local.py

echo.
echo Server stopped. Press any key to exit...
pause >nul
