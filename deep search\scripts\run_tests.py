#!/usr/bin/env python3
"""
Test runner script for DeepSearch Agent
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command, check=True):
    """Run a shell command"""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, check=check)
    return result.returncode == 0


def run_unit_tests():
    """Run unit tests only"""
    print("🧪 Running unit tests...")
    return run_command("python -m pytest tests/ -m 'not integration' -v")


def run_integration_tests():
    """Run integration tests"""
    print("🔗 Running integration tests...")
    print("Note: Integration tests require internet connection and may take longer")
    return run_command("python -m pytest tests/ -m 'integration' -v")


def run_all_tests():
    """Run all tests"""
    print("🚀 Running all tests...")
    return run_command("python -m pytest tests/ -v")


def run_coverage():
    """Run tests with coverage"""
    print("📊 Running tests with coverage...")
    commands = [
        "python -m pytest tests/ --cov=src --cov-report=html --cov-report=term",
        "echo 'Coverage report generated in htmlcov/index.html'"
    ]
    
    for cmd in commands:
        if not run_command(cmd, check=False):
            return False
    return True


def run_specific_test(test_path):
    """Run a specific test file or function"""
    print(f"🎯 Running specific test: {test_path}")
    return run_command(f"python -m pytest {test_path} -v")


def lint_code():
    """Run code linting"""
    print("🔍 Running code linting...")
    
    # Check if flake8 is available
    if run_command("flake8 --version", check=False):
        return run_command("flake8 src/ tests/ --max-line-length=100 --ignore=E203,W503")
    else:
        print("flake8 not installed, skipping linting")
        return True


def format_code():
    """Format code with black"""
    print("🎨 Formatting code...")
    
    # Check if black is available
    if run_command("black --version", check=False):
        return run_command("black src/ tests/ --line-length=100")
    else:
        print("black not installed, skipping formatting")
        return True


def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="DeepSearch Agent Test Runner")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("--coverage", action="store_true", help="Run tests with coverage")
    parser.add_argument("--lint", action="store_true", help="Run code linting")
    parser.add_argument("--format", action="store_true", help="Format code")
    parser.add_argument("--test", help="Run specific test file or function")
    parser.add_argument("--quick", action="store_true", help="Run quick tests (unit + lint)")
    
    args = parser.parse_args()
    
    # Change to project root directory
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    success = True
    
    try:
        if args.unit:
            success = run_unit_tests()
        elif args.integration:
            success = run_integration_tests()
        elif args.all:
            success = run_all_tests()
        elif args.coverage:
            success = run_coverage()
        elif args.lint:
            success = lint_code()
        elif args.format:
            success = format_code()
        elif args.test:
            success = run_specific_test(args.test)
        elif args.quick:
            print("🚀 Running quick tests (unit + lint)...")
            success = run_unit_tests() and lint_code()
        else:
            # Default: run unit tests
            print("No specific test type specified, running unit tests...")
            success = run_unit_tests()
        
        if success:
            print("\n✅ All tests passed!")
        else:
            print("\n❌ Some tests failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nTest runner failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
