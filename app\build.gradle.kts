plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    id("kotlin-parcelize")
}

android {
    namespace = "com.modetaris.gamingnews"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.modetaris.gamingnews"
        minSdk = 21
        targetSdk = 35
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        create("release") {
            keyAlias = "gaming-news"
            keyPassword = "gamingnews123"
            storeFile = file("../gaming-news-key.keystore")
            storePassword = "gamingnews123"
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")

            // معرفات AdMob الحقيقية للإنتاج
            resValue("string", "admob_app_id", "ca-app-pub-4259271882937197~2882200625")
            resValue("string", "admob_banner_id", "ca-app-pub-4259271882937197/6184550183")
            resValue("string", "admob_interstitial_id", "ca-app-pub-3940256099942544/1033173712")
            resValue("string", "admob_rewarded_id", "ca-app-pub-4259271882937197/6775561214")


        }
        debug {
            // معرفات الاختبار للتطوير
            resValue("string", "admob_app_id", "ca-app-pub-3940256099942544~3347511713")
            resValue("string", "admob_banner_id", "ca-app-pub-3940256099942544/6300978111")
            resValue("string", "admob_interstitial_id", "ca-app-pub-3940256099942544/1033173712")
            resValue("string", "admob_rewarded_id", "ca-app-pub-3940256099942544/5224354917")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
}

dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.constraintlayout)

    // WebView (الوحيد المطلوب للواجهة الويب)
    implementation("androidx.webkit:webkit:1.8.0")

    // Google AdMob (optional for future monetization)
    implementation("com.google.android.gms:play-services-ads:22.6.0")

    // Network Security
    implementation("androidx.security:security-crypto:1.1.0-alpha06")

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}