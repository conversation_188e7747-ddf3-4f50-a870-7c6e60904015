"""
Database manager for DeepSearch
"""

import os
import asyncio
import hashlib
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy import create_engine, and_, or_
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import IntegrityError
from loguru import logger
from .models import Base, SearchQuery, Article, Embedding, SearchResult, Cache, SystemLog, UserSession


class DatabaseManager:
    """Manages database operations for DeepSearch"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.database_url = config.get('database', {}).get('url', 'sqlite:///data/deepsearch.db')
        self.engine = None
        self.SessionLocal = None
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize database connection and create tables"""
        try:
            # Ensure data directory exists
            if 'sqlite' in self.database_url:
                db_path = self.database_url.replace('sqlite:///', '')
                os.makedirs(os.path.dirname(db_path), exist_ok=True)
            
            # Create engine
            self.engine = create_engine(
                self.database_url,
                echo=False,  # Set to True for SQL debugging
                pool_pre_ping=True
            )
            
            # Create session factory
            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
            
            # Create tables
            Base.metadata.create_all(bind=self.engine)
            
            logger.info(f"Database initialized: {self.database_url}")
            
        except Exception as e:
            logger.error(f"Database initialization failed: {str(e)}")
            raise
    
    def get_session(self) -> Session:
        """Get a database session"""
        return self.SessionLocal()
    
    async def save_search_query(
        self, 
        query: str, 
        enhanced_query: str = None,
        language: str = 'ar',
        engines_used: List[str] = None,
        processing_time: float = None
    ) -> int:
        """
        Save a search query to database
        
        Args:
            query: Original search query
            enhanced_query: AI-enhanced query
            language: Query language
            engines_used: List of search engines used
            processing_time: Time taken for processing
            
        Returns:
            Query ID
        """
        try:
            with self.get_session() as session:
                search_query = SearchQuery(
                    query=query,
                    enhanced_query=enhanced_query,
                    language=language,
                    engines_used=engines_used or [],
                    processing_time=processing_time
                )
                
                session.add(search_query)
                session.commit()
                session.refresh(search_query)
                
                logger.info(f"Saved search query: {query[:50]}...")
                return search_query.id
                
        except Exception as e:
            logger.error(f"Failed to save search query: {str(e)}")
            return 0
    
    async def save_article(self, article_data: Dict[str, Any]) -> Optional[int]:
        """
        Save an article to database
        
        Args:
            article_data: Article data dictionary
            
        Returns:
            Article ID or None if failed
        """
        try:
            with self.get_session() as session:
                # Check if article already exists
                existing = session.query(Article).filter(Article.url == article_data.get('url')).first()
                if existing:
                    logger.info(f"Article already exists: {article_data.get('url')}")
                    return existing.id
                
                article = Article(
                    url=article_data.get('url', ''),
                    title=article_data.get('title', ''),
                    content=article_data.get('content', ''),
                    summary=article_data.get('summary', ''),
                    description=article_data.get('description', ''),
                    author=article_data.get('author', ''),
                    language=article_data.get('language', 'ar'),
                    keywords=article_data.get('keywords', []),
                    ai_keywords=article_data.get('ai_keywords', []),
                    source_engine=article_data.get('source_engine', ''),
                    is_summarized=article_data.get('is_summarized', False),
                    sentiment=article_data.get('sentiment', {}),
                    content_length=len(article_data.get('content', '')),
                    similarity_score=article_data.get('similarity_score', 0.0)
                )
                
                session.add(article)
                session.commit()
                session.refresh(article)
                
                logger.info(f"Saved article: {article.title[:50]}...")
                return article.id
                
        except IntegrityError:
            logger.warning(f"Article URL already exists: {article_data.get('url')}")
            return None
        except Exception as e:
            logger.error(f"Failed to save article: {str(e)}")
            return None
    
    async def save_embedding(
        self, 
        article_id: int, 
        embedding_vector: List[float], 
        model_name: str,
        text_content: str
    ) -> Optional[int]:
        """
        Save an embedding to database
        
        Args:
            article_id: Article ID
            embedding_vector: Embedding vector
            model_name: Model used for embedding
            text_content: Original text content
            
        Returns:
            Embedding ID or None if failed
        """
        try:
            # Create hash of text content for deduplication
            text_hash = hashlib.sha256(text_content.encode()).hexdigest()
            
            with self.get_session() as session:
                # Check if embedding already exists
                existing = session.query(Embedding).filter(
                    and_(Embedding.article_id == article_id, Embedding.text_hash == text_hash)
                ).first()
                
                if existing:
                    logger.info(f"Embedding already exists for article {article_id}")
                    return existing.id
                
                embedding = Embedding(
                    article_id=article_id,
                    embedding_vector=embedding_vector,
                    embedding_model=model_name,
                    text_hash=text_hash
                )
                
                session.add(embedding)
                session.commit()
                session.refresh(embedding)
                
                logger.info(f"Saved embedding for article {article_id}")
                return embedding.id
                
        except Exception as e:
            logger.error(f"Failed to save embedding: {str(e)}")
            return None
    
    async def get_articles_by_query(
        self, 
        query: str, 
        limit: int = 20,
        language: str = None
    ) -> List[Dict[str, Any]]:
        """
        Get articles matching a query
        
        Args:
            query: Search query
            limit: Maximum number of results
            language: Filter by language
            
        Returns:
            List of article dictionaries
        """
        try:
            with self.get_session() as session:
                query_filter = or_(
                    Article.title.contains(query),
                    Article.content.contains(query),
                    Article.description.contains(query)
                )
                
                if language:
                    query_filter = and_(query_filter, Article.language == language)
                
                articles = session.query(Article).filter(query_filter).limit(limit).all()
                
                result = []
                for article in articles:
                    result.append({
                        'id': article.id,
                        'url': article.url,
                        'title': article.title,
                        'content': article.content,
                        'summary': article.summary,
                        'description': article.description,
                        'author': article.author,
                        'language': article.language,
                        'keywords': article.keywords,
                        'ai_keywords': article.ai_keywords,
                        'source_engine': article.source_engine,
                        'scraped_at': article.scraped_at.isoformat() if article.scraped_at else None,
                        'is_summarized': article.is_summarized,
                        'sentiment': article.sentiment,
                        'similarity_score': article.similarity_score
                    })
                
                logger.info(f"Retrieved {len(result)} articles for query: {query[:50]}...")
                return result
                
        except Exception as e:
            logger.error(f"Failed to get articles: {str(e)}")
            return []
    
    async def get_recent_articles(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get recent articles
        
        Args:
            limit: Maximum number of articles
            
        Returns:
            List of recent articles
        """
        try:
            with self.get_session() as session:
                articles = session.query(Article).order_by(Article.scraped_at.desc()).limit(limit).all()
                
                result = []
                for article in articles:
                    result.append({
                        'id': article.id,
                        'url': article.url,
                        'title': article.title,
                        'content': article.content,
                        'summary': article.summary,
                        'description': article.description,
                        'author': article.author,
                        'language': article.language,
                        'keywords': article.keywords,
                        'ai_keywords': article.ai_keywords,
                        'source_engine': article.source_engine,
                        'scraped_at': article.scraped_at.isoformat() if article.scraped_at else None,
                        'is_summarized': article.is_summarized,
                        'sentiment': article.sentiment,
                        'similarity_score': article.similarity_score
                    })
                
                logger.info(f"Retrieved {len(result)} recent articles")
                return result
                
        except Exception as e:
            logger.error(f"Failed to get recent articles: {str(e)}")
            return []
    
    async def cache_set(self, key: str, value: Any, ttl_seconds: int = 3600):
        """
        Set cache value
        
        Args:
            key: Cache key
            value: Value to cache
            ttl_seconds: Time to live in seconds
        """
        try:
            expires_at = datetime.now() + timedelta(seconds=ttl_seconds)
            
            with self.get_session() as session:
                # Remove existing cache entry
                session.query(Cache).filter(Cache.cache_key == key).delete()
                
                cache_entry = Cache(
                    cache_key=key,
                    cache_value=value,
                    expires_at=expires_at
                )
                
                session.add(cache_entry)
                session.commit()
                
                logger.debug(f"Cached value for key: {key}")
                
        except Exception as e:
            logger.error(f"Failed to set cache: {str(e)}")
    
    async def cache_get(self, key: str) -> Optional[Any]:
        """
        Get cache value
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found/expired
        """
        try:
            with self.get_session() as session:
                cache_entry = session.query(Cache).filter(
                    and_(
                        Cache.cache_key == key,
                        or_(Cache.expires_at.is_(None), Cache.expires_at > datetime.now())
                    )
                ).first()
                
                if cache_entry:
                    logger.debug(f"Cache hit for key: {key}")
                    return cache_entry.cache_value
                else:
                    logger.debug(f"Cache miss for key: {key}")
                    return None
                    
        except Exception as e:
            logger.error(f"Failed to get cache: {str(e)}")
            return None
    
    async def cleanup_expired_cache(self):
        """Clean up expired cache entries"""
        try:
            with self.get_session() as session:
                deleted_count = session.query(Cache).filter(
                    Cache.expires_at < datetime.now()
                ).delete()
                
                session.commit()
                
                if deleted_count > 0:
                    logger.info(f"Cleaned up {deleted_count} expired cache entries")
                    
        except Exception as e:
            logger.error(f"Failed to cleanup cache: {str(e)}")
    
    async def get_database_stats(self) -> Dict[str, int]:
        """
        Get database statistics
        
        Returns:
            Dictionary with database statistics
        """
        try:
            with self.get_session() as session:
                stats = {
                    'total_articles': session.query(Article).count(),
                    'total_queries': session.query(SearchQuery).count(),
                    'total_embeddings': session.query(Embedding).count(),
                    'total_cache_entries': session.query(Cache).count(),
                    'recent_articles_24h': session.query(Article).filter(
                        Article.scraped_at > datetime.now() - timedelta(days=1)
                    ).count()
                }
                
                logger.info(f"Database stats: {stats}")
                return stats
                
        except Exception as e:
            logger.error(f"Failed to get database stats: {str(e)}")
            return {}
