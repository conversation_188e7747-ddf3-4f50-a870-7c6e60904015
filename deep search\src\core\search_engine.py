"""
Main search engine that coordinates all components
"""

import time
import asyncio
from typing import List, Dict, Any, Optional
from loguru import logger
from ..scrapers.scraper_manager import ScraperManager
from ..ai.ai_manager import AIManager
from ..database.database_manager import DatabaseManager


class DeepSearchEngine:
    """Main search engine coordinating all components"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Initialize components
        self.scraper_manager = ScraperManager(config)
        self.database_manager = DatabaseManager(config)
        self.ai_manager = None  # Will be initialized in async context
        
        # Configuration
        self.cache_enabled = config.get('cache', {}).get('enabled', True)
        self.cache_ttl = config.get('cache', {}).get('ttl', 3600)
        self.max_results = config.get('search', {}).get('max_results', 20)
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.ai_manager = AIManager(self.config)
        await self.ai_manager.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.ai_manager:
            await self.ai_manager.__aexit__(exc_type, exc_val, exc_tb)
    
    async def search(
        self, 
        query: str, 
        max_results: int = None,
        engines: List[str] = None,
        summarize: bool = True,
        use_cache: bool = True,
        enhance_query: bool = True
    ) -> Dict[str, Any]:
        """
        Perform comprehensive search
        
        Args:
            query: Search query
            max_results: Maximum number of results
            engines: List of search engines to use
            summarize: Whether to summarize articles
            use_cache: Whether to use cache
            enhance_query: Whether to enhance query with AI
            
        Returns:
            Search results with metadata
        """
        start_time = time.time()
        
        try:
            # Use default max_results if not specified
            if max_results is None:
                max_results = self.max_results
            
            logger.info(f"Starting search for: '{query}'")
            
            # Check cache first
            cache_key = f"search:{hash(query)}:{max_results}:{str(engines)}"
            if use_cache and self.cache_enabled:
                cached_result = await self.database_manager.cache_get(cache_key)
                if cached_result:
                    logger.info("Returning cached search results")
                    return cached_result
            
            # Enhance query with AI if requested
            enhanced_query = query
            if enhance_query and self.ai_manager:
                try:
                    enhanced_query = await self.ai_manager.enhance_search_query(query)
                except Exception as e:
                    logger.warning(f"Query enhancement failed: {str(e)}")
                    enhanced_query = query
            
            # Search and scrape articles
            articles = await self.scraper_manager.search_and_scrape(
                enhanced_query, 
                max_results=max_results,
                engines=engines
            )
            
            if not articles:
                logger.warning("No articles found")
                return {
                    "query": query,
                    "enhanced_query": enhanced_query,
                    "articles": [],
                    "total_results": 0,
                    "processing_time": time.time() - start_time,
                    "engines_used": engines or [],
                    "cached": False
                }
            
            # Process articles with AI
            if self.ai_manager:
                # Summarize articles
                if summarize:
                    articles = await self.ai_manager.summarize_articles(articles)
                
                # Extract keywords
                articles = await self.ai_manager.extract_keywords_from_articles(articles)
                
                # Analyze sentiment
                articles = await self.ai_manager.analyze_article_sentiment(articles)
            
            # Save to database
            query_id = await self.database_manager.save_search_query(
                query=query,
                enhanced_query=enhanced_query,
                engines_used=engines or [],
                processing_time=time.time() - start_time
            )
            
            # Save articles to database
            saved_articles = []
            for article in articles:
                article_id = await self.database_manager.save_article(article)
                if article_id:
                    article['id'] = article_id
                    saved_articles.append(article)
            
            # Prepare result
            result = {
                "query": query,
                "enhanced_query": enhanced_query,
                "articles": saved_articles,
                "total_results": len(saved_articles),
                "processing_time": time.time() - start_time,
                "engines_used": engines or self.scraper_manager.get_available_engines(),
                "cached": False,
                "query_id": query_id
            }
            
            # Cache result
            if use_cache and self.cache_enabled:
                await self.database_manager.cache_set(cache_key, result, self.cache_ttl)
            
            logger.info(f"Search completed in {result['processing_time']:.2f}s with {len(saved_articles)} results")
            return result
            
        except Exception as e:
            logger.error(f"Search failed: {str(e)}")
            return {
                "query": query,
                "enhanced_query": query,
                "articles": [],
                "total_results": 0,
                "processing_time": time.time() - start_time,
                "engines_used": [],
                "cached": False,
                "error": str(e)
            }
    
    async def rag_search(
        self, 
        query: str, 
        max_context_articles: int = 10,
        use_recent_articles: bool = True
    ) -> Dict[str, Any]:
        """
        Perform RAG (Retrieval-Augmented Generation) search
        
        Args:
            query: Search query
            max_context_articles: Maximum articles to use for context
            use_recent_articles: Whether to include recent articles
            
        Returns:
            RAG response with answer and sources
        """
        start_time = time.time()
        
        try:
            logger.info(f"Starting RAG search for: '{query}'")
            
            # Get articles for context
            context_articles = []
            
            # First, try to get relevant articles from database
            db_articles = await self.database_manager.get_articles_by_query(
                query, limit=max_context_articles // 2
            )
            context_articles.extend(db_articles)
            
            # If we need more articles or want fresh content, search online
            if len(context_articles) < max_context_articles:
                remaining_count = max_context_articles - len(context_articles)
                online_articles = await self.scraper_manager.search_and_scrape(
                    query, max_results=remaining_count
                )
                
                # Process new articles
                if self.ai_manager and online_articles:
                    online_articles = await self.ai_manager.summarize_articles(online_articles)
                    
                    # Save new articles to database
                    for article in online_articles:
                        article_id = await self.database_manager.save_article(article)
                        if article_id:
                            article['id'] = article_id
                
                context_articles.extend(online_articles)
            
            # Add recent articles if requested
            if use_recent_articles:
                recent_articles = await self.database_manager.get_recent_articles(limit=10)
                context_articles.extend(recent_articles)
            
            # Remove duplicates based on URL
            seen_urls = set()
            unique_articles = []
            for article in context_articles:
                url = article.get('url', '')
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    unique_articles.append(article)
            
            # Perform RAG with AI manager
            if self.ai_manager and unique_articles:
                rag_result = await self.ai_manager.perform_rag_search(
                    query, unique_articles, top_k=min(5, len(unique_articles))
                )
            else:
                rag_result = {
                    "answer": "لا توجد معلومات كافية للإجابة على الاستعلام.",
                    "sources": [],
                    "confidence": 0.0
                }
            
            # Add metadata
            rag_result.update({
                "query": query,
                "total_context_articles": len(unique_articles),
                "processing_time": time.time() - start_time
            })
            
            logger.info(f"RAG search completed in {rag_result['processing_time']:.2f}s")
            return rag_result
            
        except Exception as e:
            logger.error(f"RAG search failed: {str(e)}")
            return {
                "query": query,
                "answer": "حدث خطأ أثناء معالجة الاستعلام.",
                "sources": [],
                "confidence": 0.0,
                "processing_time": time.time() - start_time,
                "error": str(e)
            }
    
    async def get_trending_topics(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get trending topics based on recent articles
        
        Args:
            limit: Maximum number of topics
            
        Returns:
            List of trending topics with metadata
        """
        try:
            # Get recent articles
            recent_articles = await self.database_manager.get_recent_articles(limit=50)
            
            if not recent_articles:
                return []
            
            # Extract keywords and analyze trends
            all_keywords = []
            for article in recent_articles:
                keywords = article.get('ai_keywords', []) or article.get('keywords', [])
                all_keywords.extend(keywords)
            
            # Count keyword frequency
            keyword_counts = {}
            for keyword in all_keywords:
                if keyword and len(keyword.strip()) > 2:
                    keyword = keyword.strip().lower()
                    keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1
            
            # Sort by frequency
            trending_keywords = sorted(
                keyword_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:limit]
            
            # Prepare trending topics
            trending_topics = []
            for keyword, count in trending_keywords:
                trending_topics.append({
                    "topic": keyword,
                    "frequency": count,
                    "articles_count": count  # Simplified for now
                })
            
            logger.info(f"Found {len(trending_topics)} trending topics")
            return trending_topics
            
        except Exception as e:
            logger.error(f"Failed to get trending topics: {str(e)}")
            return []
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on all components
        
        Returns:
            Health status of all components
        """
        health_status = {
            "timestamp": time.time(),
            "overall_status": "healthy"
        }
        
        try:
            # Check scrapers
            scraper_health = await self.scraper_manager.health_check()
            health_status["scrapers"] = scraper_health
            
            # Check AI components
            if self.ai_manager:
                ai_health = await self.ai_manager.health_check()
                health_status["ai"] = ai_health
            else:
                health_status["ai"] = {"status": "not_initialized"}
            
            # Check database
            try:
                db_stats = await self.database_manager.get_database_stats()
                health_status["database"] = {"status": "healthy", "stats": db_stats}
            except Exception as e:
                health_status["database"] = {"status": "error", "error": str(e)}
            
            # Determine overall status
            all_healthy = True
            for component, status in health_status.items():
                if isinstance(status, dict) and status.get("status") == "error":
                    all_healthy = False
                elif isinstance(status, dict) and any(not v for v in status.values() if isinstance(v, bool)):
                    all_healthy = False
            
            health_status["overall_status"] = "healthy" if all_healthy else "degraded"
            
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            health_status["overall_status"] = "error"
            health_status["error"] = str(e)
        
        return health_status
