"""
Main search engine that coordinates all components
"""

import time
import asyncio
from typing import List, Dict, Any, Optional
from loguru import logger
from ..scrapers.scraper_manager import ScraperManager
from ..ai.ai_manager import AIManager
from ..database.database_manager import DatabaseManager
from ..news.auto_news_discovery import AutoNewsDiscovery


class DeepSearchEngine:
    """Main search engine coordinating all components"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Initialize components
        self.scraper_manager = ScraperManager(config)
        self.database_manager = DatabaseManager(config)
        self.ai_manager = None  # Will be initialized in async context
        self.auto_news_discovery = AutoNewsDiscovery(config)
        
        # Configuration
        self.cache_enabled = config.get('cache', {}).get('enabled', True)
        self.cache_ttl = config.get('cache', {}).get('ttl', 3600)
        self.max_results = config.get('search', {}).get('max_results', 20)
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.ai_manager = AIManager(self.config)
        await self.ai_manager.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.ai_manager:
            await self.ai_manager.__aexit__(exc_type, exc_val, exc_tb)
    
    async def deep_single_article_search(
        self,
        query: str,
        target_language: str = "ar",
        article_style: str = "professional"
    ) -> Dict[str, Any]:
        """
        Enhanced deep search focusing on a single high-quality article

        Args:
            query: Search query
            target_language: Target language for the article
            article_style: Style of article (professional, casual, news, analysis)

        Returns:
            Comprehensive article data with deep analysis
        """
        start_time = time.time()

        try:
            logger.info(f"Starting deep single article search for: {query}")

            # Step 1: Enhanced query processing
            if self.ai_manager:
                enhanced_query = await self.ai_manager.enhance_search_query(query)
            else:
                enhanced_query = query

            # Step 2: Search for the most relevant article
            search_results = await self.scraper_manager.search_all_engines(
                enhanced_query,
                max_results=10  # Get more options to choose the best
            )

            if not search_results:
                return {
                    "success": False,
                    "error": "No search results found",
                    "processing_time": time.time() - start_time
                }

            # Step 3: Select the best article based on relevance and quality
            best_article = await self._select_best_article(search_results, query)

            if not best_article:
                return {
                    "success": False,
                    "error": "No suitable article found",
                    "processing_time": time.time() - start_time
                }

            # Step 4: Deep content extraction and analysis
            deep_analysis = await self._perform_deep_article_analysis(
                best_article, query, target_language, article_style
            )

            # Step 5: Generate comprehensive article data
            result = {
                "success": True,
                "query": query,
                "enhanced_query": enhanced_query,
                "selected_article": deep_analysis,
                "processing_time": time.time() - start_time,
                "search_metadata": {
                    "total_candidates": len(search_results),
                    "selection_criteria": "relevance_and_quality",
                    "target_language": target_language,
                    "article_style": article_style
                }
            }

            logger.info(f"Deep single article search completed in {result['processing_time']:.2f}s")
            return result

        except Exception as e:
            logger.error(f"Deep single article search failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "processing_time": time.time() - start_time
            }

    async def search(
        self,
        query: str,
        max_results: int = None,
        engines: List[str] = None,
        summarize: bool = True,
        use_cache: bool = True,
        enhance_query: bool = True
    ) -> Dict[str, Any]:
        """
        Perform comprehensive search
        
        Args:
            query: Search query
            max_results: Maximum number of results
            engines: List of search engines to use
            summarize: Whether to summarize articles
            use_cache: Whether to use cache
            enhance_query: Whether to enhance query with AI
            
        Returns:
            Search results with metadata
        """
        start_time = time.time()
        
        try:
            # Use default max_results if not specified
            if max_results is None:
                max_results = self.max_results
            
            logger.info(f"Starting search for: '{query}'")
            
            # Check cache first
            cache_key = f"search:{hash(query)}:{max_results}:{str(engines)}"
            if use_cache and self.cache_enabled:
                cached_result = await self.database_manager.cache_get(cache_key)
                if cached_result:
                    logger.info("Returning cached search results")
                    return cached_result
            
            # Enhance query with AI if requested
            enhanced_query = query
            if enhance_query and self.ai_manager:
                try:
                    enhanced_query = await self.ai_manager.enhance_search_query(query)
                except Exception as e:
                    logger.warning(f"Query enhancement failed: {str(e)}")
                    enhanced_query = query
            
            # Search and scrape articles
            articles = await self.scraper_manager.search_and_scrape(
                enhanced_query, 
                max_results=max_results,
                engines=engines
            )
            
            if not articles:
                logger.warning("No articles found")
                return {
                    "query": query,
                    "enhanced_query": enhanced_query,
                    "articles": [],
                    "total_results": 0,
                    "processing_time": time.time() - start_time,
                    "engines_used": engines or [],
                    "cached": False
                }
            
            # Process articles with AI
            if self.ai_manager:
                # Summarize articles
                if summarize:
                    articles = await self.ai_manager.summarize_articles(articles)
                
                # Extract keywords
                articles = await self.ai_manager.extract_keywords_from_articles(articles)
                
                # Analyze sentiment
                articles = await self.ai_manager.analyze_article_sentiment(articles)
            
            # Save to database
            query_id = await self.database_manager.save_search_query(
                query=query,
                enhanced_query=enhanced_query,
                engines_used=engines or [],
                processing_time=time.time() - start_time
            )
            
            # Save articles to database
            saved_articles = []
            for article in articles:
                article_id = await self.database_manager.save_article(article)
                if article_id:
                    article['id'] = article_id
                    saved_articles.append(article)
            
            # Prepare result
            result = {
                "query": query,
                "enhanced_query": enhanced_query,
                "articles": saved_articles,
                "total_results": len(saved_articles),
                "processing_time": time.time() - start_time,
                "engines_used": engines or self.scraper_manager.get_available_engines(),
                "cached": False,
                "query_id": query_id
            }
            
            # Cache result
            if use_cache and self.cache_enabled:
                await self.database_manager.cache_set(cache_key, result, self.cache_ttl)
            
            logger.info(f"Search completed in {result['processing_time']:.2f}s with {len(saved_articles)} results")
            return result
            
        except Exception as e:
            logger.error(f"Search failed: {str(e)}")
            return {
                "query": query,
                "enhanced_query": query,
                "articles": [],
                "total_results": 0,
                "processing_time": time.time() - start_time,
                "engines_used": [],
                "cached": False,
                "error": str(e)
            }
    
    async def rag_search(
        self, 
        query: str, 
        max_context_articles: int = 10,
        use_recent_articles: bool = True
    ) -> Dict[str, Any]:
        """
        Perform RAG (Retrieval-Augmented Generation) search
        
        Args:
            query: Search query
            max_context_articles: Maximum articles to use for context
            use_recent_articles: Whether to include recent articles
            
        Returns:
            RAG response with answer and sources
        """
        start_time = time.time()
        
        try:
            logger.info(f"Starting RAG search for: '{query}'")
            
            # Get articles for context
            context_articles = []
            
            # First, try to get relevant articles from database
            db_articles = await self.database_manager.get_articles_by_query(
                query, limit=max_context_articles // 2
            )
            context_articles.extend(db_articles)
            
            # If we need more articles or want fresh content, search online
            if len(context_articles) < max_context_articles:
                remaining_count = max_context_articles - len(context_articles)
                online_articles = await self.scraper_manager.search_and_scrape(
                    query, max_results=remaining_count
                )
                
                # Process new articles
                if self.ai_manager and online_articles:
                    online_articles = await self.ai_manager.summarize_articles(online_articles)
                    
                    # Save new articles to database
                    for article in online_articles:
                        article_id = await self.database_manager.save_article(article)
                        if article_id:
                            article['id'] = article_id
                
                context_articles.extend(online_articles)
            
            # Add recent articles if requested
            if use_recent_articles:
                recent_articles = await self.database_manager.get_recent_articles(limit=10)
                context_articles.extend(recent_articles)
            
            # Remove duplicates based on URL
            seen_urls = set()
            unique_articles = []
            for article in context_articles:
                url = article.get('url', '')
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    unique_articles.append(article)
            
            # Perform RAG with AI manager
            if self.ai_manager and unique_articles:
                rag_result = await self.ai_manager.perform_rag_search(
                    query, unique_articles, top_k=min(5, len(unique_articles))
                )
            else:
                rag_result = {
                    "answer": "لا توجد معلومات كافية للإجابة على الاستعلام.",
                    "sources": [],
                    "confidence": 0.0
                }
            
            # Add metadata
            rag_result.update({
                "query": query,
                "total_context_articles": len(unique_articles),
                "processing_time": time.time() - start_time
            })
            
            logger.info(f"RAG search completed in {rag_result['processing_time']:.2f}s")
            return rag_result
            
        except Exception as e:
            logger.error(f"RAG search failed: {str(e)}")
            return {
                "query": query,
                "answer": "حدث خطأ أثناء معالجة الاستعلام.",
                "sources": [],
                "confidence": 0.0,
                "processing_time": time.time() - start_time,
                "error": str(e)
            }
    
    async def get_trending_topics(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get trending topics based on recent articles
        
        Args:
            limit: Maximum number of topics
            
        Returns:
            List of trending topics with metadata
        """
        try:
            # Get recent articles
            recent_articles = await self.database_manager.get_recent_articles(limit=50)
            
            if not recent_articles:
                return []
            
            # Extract keywords and analyze trends
            all_keywords = []
            for article in recent_articles:
                keywords = article.get('ai_keywords', []) or article.get('keywords', [])
                all_keywords.extend(keywords)
            
            # Count keyword frequency
            keyword_counts = {}
            for keyword in all_keywords:
                if keyword and len(keyword.strip()) > 2:
                    keyword = keyword.strip().lower()
                    keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1
            
            # Sort by frequency
            trending_keywords = sorted(
                keyword_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:limit]
            
            # Prepare trending topics
            trending_topics = []
            for keyword, count in trending_keywords:
                trending_topics.append({
                    "topic": keyword,
                    "frequency": count,
                    "articles_count": count  # Simplified for now
                })
            
            logger.info(f"Found {len(trending_topics)} trending topics")
            return trending_topics

        except Exception as e:
            logger.error(f"Failed to get trending topics: {str(e)}")
            return []

    async def _select_best_article(self, articles: List[Dict[str, Any]], query: str) -> Optional[Dict[str, Any]]:
        """
        Select the best article from search results based on relevance and quality

        Args:
            articles: List of article candidates
            query: Original search query

        Returns:
            Best article or None
        """
        if not articles:
            return None

        try:
            # Score articles based on multiple criteria
            scored_articles = []

            for article in articles:
                score = 0

                # Title relevance (40% weight)
                title = article.get('title', '').lower()
                query_words = query.lower().split()
                title_matches = sum(1 for word in query_words if word in title)
                title_score = (title_matches / len(query_words)) * 0.4

                # URL quality (20% weight)
                url = article.get('url', '')
                url_score = 0
                if any(domain in url for domain in ['bbc.com', 'cnn.com', 'aljazeera.net', 'alarabiya.net', 'reuters.com']):
                    url_score = 0.2
                elif any(domain in url for domain in ['news', 'article', 'story']):
                    url_score = 0.1

                # Content length (20% weight)
                content = article.get('content', '')
                if len(content) > 1000:
                    content_score = 0.2
                elif len(content) > 500:
                    content_score = 0.1
                else:
                    content_score = 0

                # Freshness (20% weight)
                # Assume newer articles are better (simplified)
                freshness_score = 0.2

                total_score = title_score + url_score + content_score + freshness_score
                scored_articles.append((article, total_score))

            # Sort by score and return the best
            scored_articles.sort(key=lambda x: x[1], reverse=True)
            best_article = scored_articles[0][0]

            logger.info(f"Selected best article: {best_article.get('title', 'Unknown')} (score: {scored_articles[0][1]:.2f})")
            return best_article

        except Exception as e:
            logger.error(f"Failed to select best article: {str(e)}")
            return articles[0] if articles else None

    async def _perform_deep_article_analysis(
        self,
        article: Dict[str, Any],
        query: str,
        target_language: str,
        article_style: str
    ) -> Dict[str, Any]:
        """
        Perform deep analysis of the selected article

        Args:
            article: Selected article
            query: Original query
            target_language: Target language
            article_style: Desired article style

        Returns:
            Comprehensive article analysis
        """
        try:
            logger.info(f"Performing deep analysis for article: {article.get('title', 'Unknown')}")

            # Extract full content if not already available
            if not article.get('content') or len(article.get('content', '')) < 500:
                # Try to extract more content
                full_content = await self.scraper_manager.extract_full_content(article.get('url', ''))
                if full_content:
                    article['content'] = full_content

            analysis_result = {
                "original_article": article,
                "deep_analysis": {},
                "seo_keywords": [],
                "related_topics": [],
                "content_structure": {},
                "quality_metrics": {}
            }

            if self.ai_manager:
                content = article.get('content', '')

                # Generate comprehensive summary
                summary = await self.ai_manager._try_with_fallback(
                    lambda provider, content: provider.summarize_text(content, max_length=500),
                    content
                )
                analysis_result["deep_analysis"]["comprehensive_summary"] = summary

                # Extract SEO keywords
                seo_keywords = await self.ai_manager._try_with_fallback(
                    lambda provider, content: provider.extract_keywords(content, max_keywords=15),
                    content
                )
                analysis_result["seo_keywords"] = seo_keywords or []

                # Generate related topics for deeper research
                related_topics_prompt = f"""
                Based on this article about "{query}", suggest 5 related topics that would be valuable for deeper research:

                Article content: {content[:2000]}...

                Provide only the topic names, one per line.
                """

                related_topics_text = await self.ai_manager._try_with_fallback(
                    lambda provider, prompt: provider.generate_text(prompt),
                    related_topics_prompt
                )

                if related_topics_text:
                    analysis_result["related_topics"] = [
                        topic.strip() for topic in related_topics_text.split('\n')
                        if topic.strip()
                    ][:5]

                # Analyze content structure
                analysis_result["content_structure"] = {
                    "word_count": len(content.split()),
                    "paragraph_count": len([p for p in content.split('\n\n') if p.strip()]),
                    "estimated_reading_time": len(content.split()) // 200,  # Assuming 200 WPM
                    "language_detected": target_language
                }

                # Quality metrics
                analysis_result["quality_metrics"] = {
                    "content_depth": "high" if len(content) > 2000 else "medium" if len(content) > 1000 else "low",
                    "relevance_score": self._calculate_relevance_score(content, query),
                    "readability": "good",  # Simplified
                    "information_density": len(seo_keywords or []) / max(len(content.split()), 1) * 1000
                }

            logger.info("Deep article analysis completed successfully")
            return analysis_result

        except Exception as e:
            logger.error(f"Deep article analysis failed: {str(e)}")
            return {
                "original_article": article,
                "deep_analysis": {"error": str(e)},
                "seo_keywords": [],
                "related_topics": [],
                "content_structure": {},
                "quality_metrics": {}
            }

    def _calculate_relevance_score(self, content: str, query: str) -> float:
        """Calculate relevance score between content and query"""
        try:
            content_lower = content.lower()
            query_words = query.lower().split()

            matches = sum(1 for word in query_words if word in content_lower)
            return min(matches / len(query_words), 1.0)

        except Exception:
            return 0.5
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on all components
        
        Returns:
            Health status of all components
        """
        health_status = {
            "timestamp": time.time(),
            "overall_status": "healthy"
        }
        
        try:
            # Check scrapers
            scraper_health = await self.scraper_manager.health_check()
            health_status["scrapers"] = scraper_health
            
            # Check AI components
            if self.ai_manager:
                ai_health = await self.ai_manager.health_check()
                health_status["ai"] = ai_health
            else:
                health_status["ai"] = {"status": "not_initialized"}
            
            # Check database
            try:
                db_stats = await self.database_manager.get_database_stats()
                health_status["database"] = {"status": "healthy", "stats": db_stats}
            except Exception as e:
                health_status["database"] = {"status": "error", "error": str(e)}
            
            # Determine overall status
            all_healthy = True
            for component, status in health_status.items():
                if isinstance(status, dict) and status.get("status") == "error":
                    all_healthy = False
                elif isinstance(status, dict) and any(not v for v in status.values() if isinstance(v, bool)):
                    all_healthy = False
            
            health_status["overall_status"] = "healthy" if all_healthy else "degraded"
            
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            health_status["overall_status"] = "error"
            health_status["error"] = str(e)
        
        return health_status

    async def discover_trending_topic(
        self,
        category: str = None,
        language: str = "ar"
    ) -> Dict[str, Any]:
        """
        Automatically discover and select a trending news topic

        Args:
            category: Preferred category (technology, health, business, etc.)
            language: Target language

        Returns:
            Selected trending topic with metadata
        """
        try:
            logger.info(f"Discovering trending topic for category: {category}")

            # Get a random trending topic
            topic = await self.auto_news_discovery.get_random_trending_topic(
                language=language,
                category=category
            )

            if not topic:
                return {
                    "success": False,
                    "error": "No trending topics found"
                }

            # Perform deep search on the selected topic
            topic_title = topic.get('title', '')
            search_result = await self.deep_single_article_search(
                query=topic_title,
                target_language=language,
                article_style="news"
            )

            # Combine topic discovery with search results
            result = {
                "success": True,
                "discovered_topic": topic,
                "deep_search_result": search_result,
                "suggested_angles": topic.get('suggested_angles', []),
                "article_potential": topic.get('article_potential', {}),
                "suggested_keywords": topic.get('suggested_keywords', []),
                "auto_generated": True
            }

            logger.info(f"Successfully discovered and analyzed topic: {topic_title}")
            return result

        except Exception as e:
            logger.error(f"Failed to discover trending topic: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def configure_news_apis(self, api_configs: Dict[str, str]) -> Dict[str, Any]:
        """Configure news APIs for auto discovery"""
        results = {}

        for api_name, api_key in api_configs.items():
            result = self.auto_news_discovery.configure_news_api(api_name, api_key)
            results[api_name] = result

        return {
            "success": True,
            "configurations": results
        }

    def get_auto_news_status(self) -> Dict[str, Any]:
        """Get status of auto news discovery system"""
        return self.auto_news_discovery.get_status()
