"""
Brave Search API scraper implementation
"""

import os
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from loguru import logger
import aiohttp
from .base_scraper import BaseScraper


class BraveScraper(BaseScraper):
    """Scraper using Brave Search API"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = os.getenv('BRAVE_API_KEY')
        self.base_url = "https://api.search.brave.com/res/v1/web/search"
        
        if not self.api_key:
            logger.warning("Brave API key not found. Set BRAVE_API_KEY environment variable.")
    
    async def search_brave(self, query: str, count: int = 10, country: str = "ALL") -> Optional[Dict]:
        """
        Search using Brave Search API
        
        Args:
            query: Search query
            count: Number of results to return
            country: Country code for search
            
        Returns:
            Search results from Brave API
        """
        if not self.api_key:
            logger.error("Brave API key not available")
            return None
        
        headers = {
            "Accept": "application/json",
            "Accept-Encoding": "gzip",
            "X-Subscription-Token": self.api_key
        }
        
        params = {
            "q": query,
            "count": min(count, 20),  # Brave API limit
            "country": country,
            "search_lang": "ar",
            "ui_lang": "ar",
            "freshness": "pw",  # Past week for fresh content
            "text_decorations": False,
            "spellcheck": True
        }
        
        try:
            async with self.session.get(self.base_url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"Brave search successful for query: {query}")
                    return data
                else:
                    logger.error(f"Brave API error: {response.status}")
                    return None
                    
        except Exception as e:
            logger.error(f"Brave search failed: {str(e)}")
            return None
    
    async def scrape_article(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Scrape a single article from URL
        
        Args:
            url: Article URL
            
        Returns:
            Article data or None if failed
        """
        try:
            html_content = await self.fetch_page(url)
            if not html_content:
                return None
            
            soup = self.clean_html(html_content)
            text_content = self.extract_text(soup)
            metadata = self.extract_metadata(soup, url)
            
            # Limit content length
            max_length = self.config.get('max_content_length', 10000)
            if len(text_content) > max_length:
                text_content = text_content[:max_length] + "..."
            
            return {
                'url': url,
                'title': metadata['title'],
                'content': text_content,
                'description': metadata['description'],
                'author': metadata['author'],
                'language': metadata['language'],
                'keywords': metadata['keywords'],
                'scraped_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to scrape article {url}: {str(e)}")
            return None
    
    async def scrape(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Scrape articles using Brave Search
        
        Args:
            query: Search query
            max_results: Maximum number of results
            
        Returns:
            List of scraped articles
        """
        logger.info(f"Starting Brave search for: {query}")
        
        # Search using Brave API
        search_results = await self.search_brave(query, max_results)
        if not search_results or 'web' not in search_results:
            logger.warning("No search results from Brave API")
            return []
        
        web_results = search_results['web'].get('results', [])
        if not web_results:
            logger.warning("No web results found")
            return []
        
        # Extract URLs to scrape
        urls_to_scrape = []
        for result in web_results[:max_results]:
            url = result.get('url')
            if url and self._is_valid_url(url):
                urls_to_scrape.append(url)
        
        logger.info(f"Found {len(urls_to_scrape)} URLs to scrape")
        
        # Scrape articles concurrently
        semaphore = asyncio.Semaphore(self.config.get('concurrent_requests', 5))
        
        async def scrape_with_semaphore(url):
            async with semaphore:
                return await self.scrape_article(url)
        
        tasks = [scrape_with_semaphore(url) for url in urls_to_scrape]
        scraped_articles = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter successful results
        successful_articles = []
        for article in scraped_articles:
            if isinstance(article, dict) and article:
                successful_articles.append(article)
        
        logger.info(f"Successfully scraped {len(successful_articles)} articles")
        return successful_articles
    
    def _is_valid_url(self, url: str) -> bool:
        """
        Check if URL is valid for scraping
        
        Args:
            url: URL to check
            
        Returns:
            True if URL is valid for scraping
        """
        # Skip certain file types and domains
        skip_extensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']
        skip_domains = ['youtube.com', 'facebook.com', 'twitter.com', 'instagram.com']
        
        url_lower = url.lower()
        
        # Check file extensions
        for ext in skip_extensions:
            if url_lower.endswith(ext):
                return False
        
        # Check domains
        for domain in skip_domains:
            if domain in url_lower:
                return False
        
        return True
