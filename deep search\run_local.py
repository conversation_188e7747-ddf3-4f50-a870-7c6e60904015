#!/usr/bin/env python3
"""
Local development server with automatic browser opening
Quick start for DeepSearch Agent Pro
"""

import asyncio
import sys
import os
import time
import webbrowser
import threading
from pathlib import Path
import subprocess
import signal

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config_loader import load_config
from src.utils.logger import setup_logging
from loguru import logger


class LocalServer:
    """Local development server manager"""
    
    def __init__(self):
        self.server_process = None
        self.config = None
        self.host = "localhost"
        self.port = 8000
        
    def print_startup_banner(self):
        """Print colorful startup banner"""
        banner = f"""
\033[96m╔══════════════════════════════════════════════════════════════╗
║                    🚀 DeepSearch Agent Pro 🚀                ║
║                                                              ║
║  \033[93mمنصة متقدمة للبحث العميق وإنتاج المقالات بالذكاء الاصطناعي\033[96m  ║
║                                                              ║
║  \033[92m✨ المميزات الجديدة:\033[96m                                          ║
║  \033[94m🔍 البحث العميق المحسن مع KeyBERT & YAKE\033[96m                ║
║  \033[94m🤖 إنتاج المقالات بـ Gemini 2.5 Pro/Flash\033[96m               ║
║  \033[94m📊 تحسين SEO متقدم وكلمات مفتاحية ذكية\033[96m                 ║
║  \033[94m📝 نشر مباشر على Blogger مع Service Account\033[96m            ║
║  \033[94m🌍 دعم لغات وهجات متعددة (مصرية، خليجية، شامية)\033[96m        ║
║  \033[94m📰 اكتشاف الأخبار التلقائي مع Brave & Tavily\033[96m           ║
║  \033[94m💻 واجهة مستخدم محسنة مع تصميم عصري\033[96m                   ║
║                                                              ║
║  \033[91m🌐 Server: http://{self.host}:{self.port}\033[96m                        ║
║  \033[91m🎯 Interface: /web/enhanced.html\033[96m                         ║
║  \033[91m📚 API Docs: /docs\033[96m                                       ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝\033[0m
        """
        print(banner)
    
    def check_dependencies(self):
        """Check if required dependencies are installed"""
        logger.info("Checking dependencies...")
        
        required_packages = [
            'fastapi',
            'uvicorn',
            'loguru',
            'httpx',
            'pydantic'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                logger.debug(f"✅ {package} - OK")
            except ImportError:
                missing_packages.append(package)
                logger.error(f"❌ {package} - Missing")
        
        if missing_packages:
            logger.error(f"Missing packages: {', '.join(missing_packages)}")
            logger.info("Install with: pip install -r requirements.txt")
            return False
        
        logger.success("All dependencies are installed!")
        return True
    
    def load_configuration(self):
        """Load and validate configuration"""
        try:
            self.config = load_config()
            api_config = self.config.get('api', {})
            
            self.host = api_config.get('host', 'localhost')
            self.port = api_config.get('port', 8000)
            
            logger.info("Configuration loaded successfully")
            return True
            
        except Exception as e:
            logger.warning(f"Failed to load config: {str(e)}")
            logger.info("Using default configuration")
            self.config = {}
            return True
    
    def open_browser_delayed(self, delay=3):
        """Open browser after a delay"""
        def _open_browser():
            time.sleep(delay)
            try:
                url = f"http://{self.host}:{self.port}/web/enhanced.html"
                logger.info(f"Opening browser: {url}")
                webbrowser.open(url)
            except Exception as e:
                logger.error(f"Failed to open browser: {str(e)}")
        
        thread = threading.Thread(target=_open_browser, daemon=True)
        thread.start()
    
    def start_server(self):
        """Start the FastAPI server"""
        try:
            logger.info("Starting DeepSearch Agent Pro server...")
            
            # Import here to avoid circular imports
            import uvicorn
            from src.api.main import app
            
            # Configure uvicorn
            config = uvicorn.Config(
                app,
                host=self.host,
                port=self.port,
                log_level="info",
                reload=False,
                workers=1
            )
            
            server = uvicorn.Server(config)
            
            # Open browser after server starts
            self.open_browser_delayed(delay=2)
            
            # Start server
            logger.success(f"🚀 Server starting on http://{self.host}:{self.port}")
            logger.info("Press Ctrl+C to stop the server")
            
            # Run server
            asyncio.run(server.serve())
            
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
        except Exception as e:
            logger.error(f"Server failed to start: {str(e)}")
            return False
        
        return True
    
    def show_quick_start_guide(self):
        """Show quick start guide"""
        guide = """
\033[93m📖 دليل البدء السريع:\033[0m

\033[96m1️⃣ إعداد Gemini AI:\033[0m
   • انتقل لتبويب "الإعدادات" في الواجهة
   • أدخل Gemini API Key من Google AI Studio
   • اختر النموذج المطلوب (Gemini 2.5 Pro أو Flash)
   • اضغط "حفظ إعدادات Gemini"

\033[96m2️⃣ إعداد APIs البحث (اختياري):\033[0m
   • أضف Brave Search API Key لتحسين البحث
   • أضف Tavily API Key للبحث المتقدم
   • أضف NewsAPI أو GNews API للأخبار التلقائية

\033[96m3️⃣ إعداد Blogger (اختياري):\033[0m
   • أنشئ Service Account في Google Cloud Console
   • حمل ملف JSON للـ Service Account
   • أضف Service Account كمحرر في مدونة Blogger
   • في الواجهة، الصق محتوى JSON وأدخل Blog ID

\033[96m4️⃣ استخدام النظام:\033[0m
   • \033[92mالبحث العميق:\033[0m أدخل موضوع واحصل على تحليل شامل
   • \033[92mإنتاج المقالات:\033[0m اكتب مقالات عالية الجودة بالذكاء الاصطناعي
   • \033[92mاكتشاف الأخبار:\033[0m دع النظام يختار الأخبار الرائجة تلقائياً
   • \033[92mالنشر:\033[0m انشر مباشرة على Blogger أو احصل على HTML

\033[93m🔗 روابط مفيدة:\033[0m
   • الواجهة المحسنة: http://{self.host}:{self.port}/web/enhanced.html
   • API Documentation: http://{self.host}:{self.port}/docs
   • الواجهة الأساسية: http://{self.host}:{self.port}/web/index.html

\033[93m💡 نصائح:\033[0m
   • استخدم اللهجة المصرية للمقالات الطبيعية
   • فعل الأخطاء الإملائية البسيطة لتجنب كشف الذكاء الاصطناعي
   • استخدم الكلمات المفتاحية المقترحة لتحسين SEO
   • جرب أنواع المقالات المختلفة (مهني، إخباري، تحليلي)
        """
        print(guide)
    
    def run(self):
        """Main run method"""
        # Print banner
        self.print_startup_banner()
        
        # Setup logging
        setup_logging({})
        
        # Check dependencies
        if not self.check_dependencies():
            logger.error("❌ Dependency check failed")
            sys.exit(1)
        
        # Load configuration
        if not self.load_configuration():
            logger.error("❌ Configuration failed")
            sys.exit(1)
        
        # Show quick start guide
        self.show_quick_start_guide()
        
        # Start server
        logger.info("🚀 Starting local development server...")
        
        try:
            success = self.start_server()
            if not success:
                sys.exit(1)
        except KeyboardInterrupt:
            logger.info("👋 Goodbye!")
        except Exception as e:
            logger.error(f"💥 Unexpected error: {str(e)}")
            sys.exit(1)


def main():
    """Main entry point"""
    try:
        server = LocalServer()
        server.run()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"💥 Fatal error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
