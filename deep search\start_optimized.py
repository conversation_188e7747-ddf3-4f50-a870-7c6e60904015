#!/usr/bin/env python3
"""
Optimized server starter for DeepSearch Agent - Low token usage
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    print("🚀 Starting DeepSearch Agent Server (OPTIMIZED MODE)")
    print("⚡ This mode is optimized for minimal API token usage")
    print("=" * 60)
    
    # Load optimized configuration
    print("Loading optimized configuration...")
    from src.utils.config_loader import load_config
    
    # Use optimized config
    config = load_config("config/optimized_config.yaml")
    print("✅ Optimized configuration loaded")
    
    # Display optimization settings
    ai_config = config.get('ai', {})
    print("\n🔧 Optimization Settings:")
    print(f"  • Max content length: {ai_config.get('max_content_length', 2000)} chars")
    print(f"  • Summary length: {ai_config.get('summary_max_length', 150)} chars")
    print(f"  • Max tokens per request: {ai_config.get('max_tokens', 500)}")
    print(f"  • Rate limit: {ai_config.get('requests_per_minute', 5)} requests/min")
    print(f"  • Keyword extraction: {'✅' if ai_config.get('enable_keyword_extraction', False) else '❌'}")
    print(f"  • Sentiment analysis: {'✅' if ai_config.get('enable_sentiment_analysis', False) else '❌'}")
    print(f"  • Query enhancement: {'✅' if ai_config.get('enable_query_enhancement', False) else '❌'}")
    
    print("\nSetting up logging...")
    from src.utils.logger import setup_logging
    setup_logging(config)
    print("✅ Logging configured")
    
    print("Starting FastAPI server...")
    import uvicorn
    from src.api.main import app
    
    # Get API configuration
    api_config = config.get('api', {})
    host = api_config.get('host', '0.0.0.0')
    port = api_config.get('port', 8000)
    
    print(f"\n🌐 Server starting on http://{host}:{port}")
    print(f"📱 Web Interface: http://localhost:{port}/web/index.html")
    print(f"⚙️ Admin Panel: http://localhost:{port}/web/admin.html")
    print(f"📚 API Docs: http://localhost:{port}/docs")
    print()
    print("💡 Tips for minimal token usage:")
    print("  • Use shorter search queries")
    print("  • Search for fewer results (5-10 max)")
    print("  • Disable summarization for very short articles")
    print("  • Use cache when possible")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    # Start server
    uvicorn.run(
        app,
        host=host,
        port=port,
        reload=False,
        log_level="warning"  # Reduced logging
    )
    
except KeyboardInterrupt:
    print("\n⏹️ Server stopped by user")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install required packages:")
    print("pip install fastapi uvicorn loguru pydantic")
except Exception as e:
    print(f"❌ Error starting server: {e}")
    import traceback
    traceback.print_exc()
