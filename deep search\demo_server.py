#!/usr/bin/env python3
"""
Demo Professional News API - Working version
"""

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os

app = FastAPI(title='Demo Professional News API')

app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)

if os.path.exists('web'):
    app.mount('/web', StaticFiles(directory='web'), name='web')

@app.get('/')
def root():
    return {
        'message': 'Demo Professional News API - Working!',
        'endpoints': {
            'single': '/news/professional/single',
            'web': '/web/professional.html',
            'docs': '/docs'
        }
    }

@app.get('/news/professional/single')
def demo_article(query: str = 'test', agent_id: str = 'demo'):
    """Get single professional article (demo)"""
    
    if 'كرة القدم' in query or 'football' in query.lower():
        title = 'برشلونة يحقق فوزاً مهماً في الدوري الإسباني'
        content = '''فاز فريق برشلونة على ضيفه ريال بيتيس بنتيجة 3-1 في المباراة التي أقيمت على ملعب كامب نو ضمن منافسات الجولة العاشرة من الدوري الإسباني.

سجل أهداف برشلونة كل من روبرت ليفاندوفسكي (هدفين) وبيدري، بينما سجل هدف بيتيس الوحيد اللاعب بورخا إغليسياس.

بهذا الفوز، يرتقي برشلونة إلى المركز الثاني في جدول ترتيب الدوري برصيد 27 نقطة، متقدماً بفارق نقطة واحدة عن ريال مدريد الذي يحتل المركز الثالث.'''
        
        entities = ['برشلونة', 'ريال بيتيس', 'ليفاندوفسكي', 'بيدري', 'كامب نو']
        insights = [
            'برشلونة يواصل صحوته في الدوري الإسباني',
            'ليفاندوفسكي يؤكد أهميته الهجومية للفريق',
            'الفريق يتقدم على ريال مدريد في الترتيب'
        ]
        
    elif 'بيتكوين' in query or 'bitcoin' in query.lower():
        title = 'البيتكوين يرتفع إلى مستويات قياسية جديدة'
        content = '''شهدت عملة البيتكوين ارتفاعاً قوياً خلال تداولات اليوم، حيث تجاوزت حاجز 45,000 دولار للمرة الأولى منذ شهرين.

ويعزو المحللون هذا الارتفاع إلى عدة عوامل، أبرزها الإقبال المتزايد من المستثمرين المؤسسيين وتحسن المعنويات في السوق.

كما ساهمت التصريحات الإيجابية من بعض البنوك المركزية حول العملات الرقمية في دعم هذا الاتجاه الصاعد.'''
        
        entities = ['البيتكوين', 'المستثمرون المؤسسيون', 'البنوك المركزية']
        insights = [
            'اختراق مستوى 45,000 دولار يعتبر إنجازاً تقنياً مهماً',
            'المستثمرون المؤسسيون يقودون موجة الشراء الحالية',
            'تحسن المعنويات العامة في سوق العملات الرقمية'
        ]
        
    elif 'تكنولوجيا' in query or 'ذكاء اصطناعي' in query:
        title = 'شركة جوجل تكشف عن تقنيات جديدة في الذكاء الاصطناعي'
        content = '''أعلنت شركة جوجل عن إطلاق مجموعة جديدة من تقنيات الذكاء الاصطناعي التي تهدف إلى تحسين تجربة المستخدمين عبر منتجاتها المختلفة.

وتشمل التحديثات الجديدة تطوير نموذج لغوي متقدم قادر على فهم السياق بشكل أفضل، بالإضافة إلى تحسينات في تقنيات التعرف على الصور والصوت.

وأكد الرئيس التنفيذي للشركة أن هذه التطورات ستساهم في جعل التكنولوجيا أكثر سهولة وفائدة للمستخدمين حول العالم.'''
        
        entities = ['جوجل', 'الذكاء الاصطناعي', 'نموذج لغوي', 'الرئيس التنفيذي']
        insights = [
            'جوجل تواصل ريادتها في مجال الذكاء الاصطناعي',
            'التركيز على تحسين تجربة المستخدم النهائي',
            'التطوير يشمل معالجة اللغة والصور والصوت'
        ]
        
    else:
        title = f'تطورات مهمة في موضوع: {query}'
        content = f'''تشهد الساحة الدولية تطورات مهمة في موضوع "{query}" حيث تتابع الأوساط المختصة هذه التطورات باهتمام كبير.

وتشير التقارير الأولية إلى أن هناك تقدماً ملحوظاً في هذا الملف، مما يبشر بنتائج إيجابية في المستقبل القريب.

من جانبهم، أكد الخبراء أن هذه التطورات تأتي في إطار الجهود المستمرة لتحقيق التقدم في هذا المجال المهم.'''
        
        entities = ['الخبراء', 'الأوساط المختصة', 'التقارير الأولية']
        insights = [
            'الموضوع يحظى باهتمام واسع من الخبراء',
            'هناك تقدم ملحوظ في الملف',
            'التطورات تبشر بنتائج إيجابية'
        ]
    
    professional_summary = content[:200] + '...'
    
    return {
        'query': query,
        'agent_id': agent_id,
        'professional_articles': [{
            'title': title,
            'url': 'https://demo-news.com/article-1',
            'news_summary': professional_summary,
            'content': content,
            'full_content': content + '\n\n[هذا مقال تجريبي للاختبار - المحتوى الكامل متاح]',
            'professional_summary': professional_summary,
            'key_insights': insights,
            'writing_suggestions': {
                'suggested_angles': [
                    'التركيز على التأثير المستقبلي',
                    'تحليل ردود الأفعال',
                    'مقارنة مع أحداث مشابهة'
                ],
                'key_quotes': [f'تصريح مهم حول {query}'],
                'writing_tips': [
                    'ابدأ بالحدث الرئيسي',
                    'استخدم الأرقام والإحصائيات',
                    'اختتم بالتوقعات المستقبلية'
                ]
            },
            'source_engine': 'demo',
            'scraped_at': '2024-01-01T12:00:00',
            'news_score': 8.5,
            'selection_score': 9.0,
            'extracted_date': 'اليوم',
            'key_entities': entities,
            'news_type': 'regular',
            'has_full_content': True,
            'professional_enhancement': True,
            'word_count': len(content.split()),
            'estimated_reading_time': 'دقيقتان',
            'content_quality_score': 8.5
        }],
        'total_found': 1,
        'total_new': 1,
        'total_delivered': 1,
        'processing_time': 1.5,
        'extraction_enabled': True,
        'delivery_time': '2024-01-01T12:00:00',
        'message': 'تم تقديم مقال تجريبي احترافي بنجاح'
    }

@app.post('/news/professional')
def demo_articles(request: dict):
    """Get multiple professional articles (demo)"""
    query = request.get('query', 'test')
    agent_id = request.get('agent_id', 'demo')
    max_articles = min(request.get('max_articles', 1), 3)
    
    # Get single article and duplicate it
    single_result = demo_article(query, agent_id)
    articles = []
    
    for i in range(max_articles):
        article = single_result['professional_articles'][0].copy()
        article['title'] = f"{article['title']} - مقال {i+1}"
        article['url'] = f"https://demo-news.com/article-{i+1}"
        articles.append(article)
    
    result = single_result.copy()
    result['professional_articles'] = articles
    result['total_delivered'] = len(articles)
    result['message'] = f'تم تقديم {len(articles)} مقال تجريبي احترافي'
    
    return result

if __name__ == '__main__':
    import webbrowser
    import threading
    import time

    def open_browser():
        """Open browser after server starts"""
        time.sleep(2)  # Wait for server to start
        webbrowser.open('http://localhost:8001/web/professional.html')

    print('🚀 Starting Demo Professional News API')
    print('📰 Professional Interface: http://localhost:8001/web/professional.html')
    print('📚 API Docs: http://localhost:8001/docs')
    print('💡 Test: http://localhost:8001/news/professional/single?query=آخر أخبار كرة القدم')
    print('⚠️ This is a DEMO version with sample articles')
    print('🌐 Browser will open automatically...')

    # Start browser opening in background
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()

    uvicorn.run(app, host='0.0.0.0', port=8001)
