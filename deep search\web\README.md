# واجهة الويب - DeepSearch Agent

واجهة ويب تفاعلية لاختبار وإدارة أداة البحث الذكية DeepSearch Agent.

## 📋 الملفات

### 🔍 واجهة البحث الرئيسية
- **index.html**: الواجهة الرئيسية للبحث والاختبار
- **app.js**: منطق JavaScript للواجهة الرئيسية

### ⚙️ لوحة الإدارة
- **admin.html**: لوحة إدارة مقدمي خدمات الذكاء الاصطناعي

## 🚀 التشغيل

### الطريقة الأولى: عبر API
```bash
# تشغيل API مع دعم الملفات الثابتة
python main.py --api

# الوصول للواجهة
http://localhost:8000/web/index.html
http://localhost:8000/web/admin.html
```

### الطريقة الثانية: خادم محلي
```bash
# في مجلد web
python -m http.server 8080

# الوصول للواجهة
http://localhost:8080/index.html
http://localhost:8080/admin.html
```

## ✨ الميزات

### 🔍 واجهة البحث الرئيسية
- **بحث ذكي**: بحث عادي أو RAG
- **خيارات متقدمة**: عدد النتائج، التلخيص، محركات البحث
- **نتائج تفاعلية**: عرض المقالات مع التلخيص والكلمات المفتاحية
- **تحليل المشاعر**: عرض مشاعر المحتوى
- **إحصائيات مفصلة**: وقت المعالجة، المحركات المستخدمة
- **إجابات RAG**: إجابات ذكية مع المصادر

### ⚙️ لوحة الإدارة
- **إدارة المقدمين**: عرض وتبديل مقدمي الذكاء الاصطناعي
- **معلومات المقدمين**: حالة API، النماذج المدعومة
- **التبديل التلقائي**: إعداد التبديل للمقدم الاحتياطي
- **فحص الصحة**: مراقبة حالة النظام
- **تحديث فوري**: تحديث معلومات المقدمين

## 🎯 الاستخدام

### البحث العادي
1. أدخل استعلام البحث
2. اختر عدد النتائج المطلوبة
3. حدد "بحث عادي"
4. اضغط "بحث"

### البحث RAG
1. أدخل سؤالاً واضحاً
2. حدد "بحث RAG"
3. اضغط "بحث"
4. اطلع على الإجابة والمصادر

### إدارة المقدمين
1. افتح لوحة الإدارة
2. اطلع على المقدم الحالي
3. اختر مقدماً جديداً للتبديل
4. راقب حالة النظام

## 🔧 التكوين

### متغيرات JavaScript
```javascript
// في app.js
const API_BASE_URL = 'http://localhost:8000';
```

### مقدمو الخدمة المدعومون
- **Google Gemini**: Gemini 2.0 Flash
- **Mistral AI**: Mistral Large
- **Ollama**: النماذج المحلية

## 🎨 التصميم

### الألوان الرئيسية
- **الأساسي**: `#667eea` (أزرق بنفسجي)
- **الثانوي**: `#764ba2` (بنفسجي)
- **النجاح**: `#28a745` (أخضر)
- **الخطر**: `#dc3545` (أحمر)

### المكونات
- **Bootstrap 5**: إطار العمل الأساسي
- **Font Awesome**: الأيقونات
- **CSS مخصص**: تدرجات وتأثيرات

## 📱 الاستجابة

الواجهة مصممة لتعمل على:
- **سطح المكتب**: شاشات كبيرة
- **الأجهزة اللوحية**: شاشات متوسطة
- **الهواتف**: شاشات صغيرة

## 🔍 أمثلة البحث

### استعلامات عربية
- "آخر أخبار الذكاء الاصطناعي"
- "تطورات تقنية البلوك تشين"
- "أفضل لغات البرمجة 2024"

### أسئلة RAG
- "ما هو ChatGPT وكيف يعمل؟"
- "ما الفرق بين الذكاء الاصطناعي والتعلم الآلي؟"
- "كيف تؤثر التكنولوجيا على التعليم؟"

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

#### لا يمكن الوصول للAPI
```
خطأ: لا يمكن الوصول إلى API
الحل: تأكد من تشغيل الخادم على المنفذ 8000
```

#### فشل البحث
```
خطأ: فشل في البحث
الحل: تحقق من مفاتيح API في .env
```

#### مقدم الخدمة غير متاح
```
خطأ: مقدم الخدمة غير صحي
الحل: تبديل لمقدم آخر في لوحة الإدارة
```

### فحص الاتصال
```javascript
// في وحدة تحكم المتصفح
fetch('http://localhost:8000/health')
  .then(r => r.json())
  .then(console.log);
```

## 🔒 الأمان

### اعتبارات الإنتاج
- تكوين CORS بشكل صحيح
- استخدام HTTPS
- حماية مفاتيح API
- تحديد معدل الطلبات

### التوصيات
- لا تعرض مفاتيح API في الكود
- استخدم متغيرات البيئة
- فعّل المصادقة للوحة الإدارة

## 📈 التطوير المستقبلي

### ميزات مقترحة
- **مصادقة المستخدمين**: تسجيل دخول وإدارة الجلسات
- **حفظ البحثات**: تاريخ البحثات المفضلة
- **تصدير النتائج**: PDF، Excel، JSON
- **إعدادات متقدمة**: تخصيص المظهر والسلوك
- **إشعارات فورية**: تنبيهات للنتائج الجديدة
- **تحليلات الاستخدام**: إحصائيات مفصلة

### تحسينات تقنية
- **PWA**: تطبيق ويب تقدمي
- **WebSocket**: تحديثات فورية
- **Service Worker**: عمل بدون اتصال
- **IndexedDB**: تخزين محلي متقدم
