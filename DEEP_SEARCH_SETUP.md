# إعداد وتشغيل خدمة البحث العميق 🔍

هذا الدليل يوضح كيفية إعداد وتشغيل خدمة البحث العميق التي يستخدمها تطبيق أخبار الألعاب.

## 📋 المتطلبات الأساسية

### 🐍 Python
- Python 3.9 أو أحدث
- pip (مدير حزم Python)
- venv (لإنشاء البيئة الافتراضية)

### 🔑 مفاتيح API (اختيارية)
للحصول على أفضل النتائج، يمكنك الحصول على مفاتيح API من:
- **Brave Search API**: للبحث المتقدم
- **Google Gemini API**: للذكاء الاصطناعي
- **Mistral AI API**: للتحليل النصي

## 🚀 خطوات التثبيت

### 1. الانتقال إلى مجلد الخدمة
```bash
cd "deep search"
```

### 2. إنشاء البيئة الافتراضية
```bash
# إنشاء البيئة الافتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# على Windows:
venv\Scripts\activate

# على Linux/Mac:
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. تكوين الخدمة

#### إنشاء ملف التكوين
```bash
# نسخ ملف التكوين النموذجي
cp config/config.example.yaml config/config.yaml
```

#### إعداد مفاتيح API (اختياري)
أنشئ ملف `.env` في مجلد `deep search`:
```bash
# مفاتيح API (اختيارية)
BRAVE_API_KEY=your_brave_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here
OLLAMA_BASE_URL=http://localhost:11434
```

## ▶️ تشغيل الخدمة

### 🎯 التشغيل السريع
```bash
# تشغيل الخدمة الأساسية
python start_simple.py
```

### ⚙️ خيارات التشغيل المختلفة

#### 1. الوضع الأساسي
```bash
python start_basic.py
```
- بحث أساسي بدون AI
- سريع ومناسب للاختبار

#### 2. الوضع المحسن
```bash
python start_optimized.py
```
- بحث محسن مع تحليل أساسي
- توازن بين السرعة والجودة

#### 3. الوضع الاحترافي
```bash
python start_professional.py
```
- جميع الميزات المتقدمة
- أفضل جودة للنتائج

#### 4. تشغيل الخادم
```bash
python start_server.py
```
- تشغيل كخادم ويب
- واجهة API كاملة

### 🌐 تشغيل الواجهة التفاعلية
```bash
# تشغيل الخادم مع الواجهة
python main.py --api

# أو باستخدام uvicorn مباشرة
uvicorn src.api.main:app --reload --host 0.0.0.0 --port 8000
```

## 🔧 التحقق من التشغيل

### 1. اختبار الخدمة
```bash
# اختبار بسيط
python quick_start.py
```

### 2. اختبار API
افتح المتصفح وانتقل إلى:
- **الواجهة التفاعلية**: `http://localhost:8000/web/index.html`
- **لوحة الإدارة**: `http://localhost:8000/web/admin.html`
- **وثائق API**: `http://localhost:8000/docs`

### 3. اختبار من التطبيق
في ملف `NetworkModule.kt`:
```kotlin
// تغيير إلى الوضع الحقيقي
private const val USE_MOCK_SERVICE = false
```

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في تثبيت المتطلبات
```bash
# تحديث pip
python -m pip install --upgrade pip

# إعادة تثبيت المتطلبات
pip install -r requirements.txt --force-reinstall
```

#### 2. مشكلة في البورت
```bash
# تغيير البورت
python start_server.py --port 8001
```

#### 3. مشاكل الذاكرة
```bash
# تشغيل بوضع محدود الذاكرة
python start_basic.py --memory-limit
```

### 📊 مراقبة الأداء
```bash
# عرض السجلات
tail -f logs/app.log

# مراقبة استخدام الموارد
python scripts/monitor.py
```

## 🔧 التخصيص المتقدم

### تعديل إعدادات البحث
في ملف `config/config.yaml`:
```yaml
search:
  max_results: 10
  timeout: 30
  engines:
    - duckduckgo
    - brave  # يتطلب API key

ai:
  provider: gemini  # أو mistral أو ollama
  model: gemini-2.0-flash
  temperature: 0.7
```

### إضافة مصادر جديدة
```python
# في src/scrapers/
class CustomScraper(BaseScraper):
    def scrape(self, query):
        # تنفيذ البحث المخصص
        pass
```

## 📈 تحسين الأداء

### 1. استخدام Cache
```bash
# تفعيل التخزين المؤقت
export ENABLE_CACHE=true
python start_server.py
```

### 2. تشغيل متعدد العمليات
```bash
# تشغيل مع عدة workers
uvicorn src.api.main:app --workers 4
```

### 3. استخدام قاعدة بيانات خارجية
```yaml
# في config.yaml
database:
  type: postgresql
  url: postgresql://user:pass@localhost/dbname
```

## 🔒 الأمان

### 1. تأمين API
```bash
# إضافة مفتاح API
export API_KEY=your_secret_key
python start_server.py --secure
```

### 2. تشفير البيانات
```yaml
# في config.yaml
security:
  encrypt_data: true
  ssl_enabled: true
```

## 📱 ربط التطبيق

### تحديث عنوان الخدمة
في `NetworkModule.kt`:
```kotlin
// تحديث العنوان
private const val BASE_URL = "http://YOUR_SERVER_IP:8000/"
```

### اختبار الاتصال
```bash
# اختبار من الهاتف/المحاكي
curl http://YOUR_SERVER_IP:8000/news/search \
  -H "Content-Type: application/json" \
  -d '{"query": "آخر أخبار الألعاب", "maxResults": 5}'
```

## 🆘 الدعم

### الحصول على المساعدة
- **السجلات**: تحقق من ملفات `logs/`
- **الاختبارات**: شغّل `pytest tests/`
- **الوثائق**: راجع مجلد `docs/`

### الإبلاغ عن المشاكل
عند الإبلاغ عن مشكلة، يرجى تضمين:
- نسخة Python المستخدمة
- رسالة الخطأ كاملة
- خطوات إعادة إنتاج المشكلة
- محتوى ملف السجل

---

**ملاحظة**: هذه الخدمة مصممة للعمل مع تطبيق أخبار الألعاب وتوفر APIs متقدمة للبحث وتحليل المحتوى.
