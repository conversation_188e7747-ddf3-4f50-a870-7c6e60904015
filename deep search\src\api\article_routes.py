"""
Article generation API routes
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
from loguru import logger

from .main import get_search_engine
from ..core.search_engine import DeepSearchEngine


router = APIRouter(prefix="/articles", tags=["articles"])


class GeminiConfigRequest(BaseModel):
    api_key: str
    model_name: Optional[str] = "gemini-2.0-flash-exp"


class ArticleGenerationRequest(BaseModel):
    topic: str
    article_type: Optional[str] = "professional"
    target_language: Optional[str] = "ar"
    seo_keywords: Optional[List[str]] = None
    min_words: Optional[int] = 800
    max_words: Optional[int] = 1500
    use_deep_search: Optional[bool] = True
    egyptian_dialect: Optional[bool] = True
    add_spelling_errors: Optional[bool] = True


class SEOAnalysisRequest(BaseModel):
    content: str
    title: Optional[str] = ""
    target_keywords: Optional[List[str]] = None
    topic: Optional[str] = ""


class ArticleResponse(BaseModel):
    success: bool
    article: Optional[Dict[str, Any]] = None
    generation_metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class ConfigResponse(BaseModel):
    success: bool
    message: str
    model: Optional[str] = None
    api_key_valid: Optional[bool] = None
    error: Optional[str] = None


class StatusResponse(BaseModel):
    configured: bool
    api_key_set: bool
    model: str
    egyptian_dialect_enabled: bool
    spelling_errors_enabled: bool
    seo_optimization_enabled: bool
    available_models: List[str]


@router.post("/configure-gemini", response_model=ConfigResponse)
async def configure_gemini(
    request: GeminiConfigRequest,
    engine: DeepSearchEngine = Depends(get_search_engine)
):
    """
    Configure Gemini API for article generation
    
    This endpoint configures the Gemini API with the provided credentials
    and tests the connection and model availability.
    """
    try:
        async with engine:
            article_generator = engine.ai_manager.article_generator
            
            result = article_generator.configure_gemini(
                api_key=request.api_key,
                model_name=request.model_name
            )
            
            return ConfigResponse(**result)
            
    except Exception as e:
        logger.error(f"Failed to configure Gemini: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=StatusResponse)
async def get_article_generator_status(
    engine: DeepSearchEngine = Depends(get_search_engine)
):
    """
    Get current status of the article generator
    
    Returns information about configuration, enabled features, and available models.
    """
    try:
        async with engine:
            article_generator = engine.ai_manager.article_generator
            status = article_generator.get_status()
            
            return StatusResponse(**status)
            
    except Exception as e:
        logger.error(f"Failed to get article generator status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models")
async def get_available_models(
    engine: DeepSearchEngine = Depends(get_search_engine)
):
    """
    Get list of available Gemini models
    
    Returns the list of Gemini models that can be used for article generation.
    """
    try:
        async with engine:
            article_generator = engine.ai_manager.article_generator
            models = article_generator.get_available_models()
            
            return {
                "success": True,
                "models": models,
                "default_model": "gemini-2.0-flash-exp"
            }
            
    except Exception as e:
        logger.error(f"Failed to get available models: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate", response_model=ArticleResponse)
async def generate_article(
    request: ArticleGenerationRequest,
    engine: DeepSearchEngine = Depends(get_search_engine)
):
    """
    Generate a high-quality article based on the provided topic
    
    This endpoint performs deep search on the topic and generates a comprehensive
    article using Gemini AI with Egyptian dialect and SEO optimization.
    """
    try:
        async with engine:
            article_generator = engine.ai_manager.article_generator
            
            # Check if Gemini is configured
            if not article_generator.is_configured():
                raise HTTPException(
                    status_code=400, 
                    detail="Gemini is not configured. Please configure API key first."
                )
            
            research_data = {}
            
            # Perform deep search if requested
            if request.use_deep_search:
                logger.info(f"Performing deep search for topic: {request.topic}")
                search_result = await engine.deep_single_article_search(
                    query=request.topic,
                    target_language=request.target_language,
                    article_style=request.article_type
                )
                
                if search_result.get('success'):
                    research_data = search_result
                else:
                    logger.warning(f"Deep search failed: {search_result.get('error')}")
                    # Continue with empty research data
            
            # Generate article
            result = await article_generator.generate_article(
                topic=request.topic,
                research_data=research_data,
                article_type=request.article_type,
                target_language=request.target_language,
                seo_keywords=request.seo_keywords,
                min_words=request.min_words,
                max_words=request.max_words
            )
            
            return ArticleResponse(**result)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Article generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test", response_model=ArticleResponse)
async def test_article_generation(
    engine: DeepSearchEngine = Depends(get_search_engine)
):
    """
    Test article generation with a simple example
    
    This endpoint tests the article generation system with a predefined example
    to verify that everything is working correctly.
    """
    try:
        async with engine:
            article_generator = engine.ai_manager.article_generator
            
            if not article_generator.is_configured():
                raise HTTPException(
                    status_code=400,
                    detail="Gemini is not configured. Please configure API key first."
                )
            
            result = await article_generator.test_generation()
            return ArticleResponse(**result)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Article generation test failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/article-types")
async def get_article_types():
    """
    Get available article types
    
    Returns the list of article types that can be used for generation.
    """
    return {
        "success": True,
        "article_types": [
            {
                "id": "professional",
                "name": "مهني ورسمي",
                "description": "مقال مهني بأسلوب أكاديمي متقن"
            },
            {
                "id": "casual",
                "name": "غير رسمي",
                "description": "مقال غير رسمي بأسلوب ودود ومريح"
            },
            {
                "id": "news",
                "name": "إخباري",
                "description": "مقال إخباري بأسلوب صحفي واضح ومباشر"
            },
            {
                "id": "analysis",
                "name": "تحليلي",
                "description": "مقال تحليلي عميق بأسلوب نقدي ومدروس"
            },
            {
                "id": "tutorial",
                "name": "تعليمي",
                "description": "مقال تعليمي بأسلوب شرح مبسط وواضح"
            },
            {
                "id": "review",
                "name": "مراجعة",
                "description": "مقال مراجعة بأسلوب تقييمي موضوعي"
            }
        ]
    }


@router.post("/seo-analysis")
async def analyze_seo(
    request: SEOAnalysisRequest,
    engine: DeepSearchEngine = Depends(get_search_engine)
):
    """
    Analyze content for SEO optimization

    This endpoint analyzes the provided content and returns SEO recommendations,
    keyword analysis, and optimization suggestions.
    """
    try:
        async with engine:
            article_generator = engine.ai_manager.article_generator
            seo_optimizer = article_generator.seo_optimizer

            # Extract SEO keywords if not provided
            if not request.target_keywords:
                extracted_keywords = await seo_optimizer.extract_seo_keywords(
                    content=request.content,
                    title=request.title,
                    topic=request.topic
                )
                target_keywords = [kw['keyword'] for kw in extracted_keywords[:10]]
            else:
                target_keywords = request.target_keywords

            # Perform SEO analysis
            seo_analysis = await seo_optimizer.optimize_content_for_seo(
                content=request.content,
                target_keywords=target_keywords,
                title=request.title
            )

            # Extract detailed keyword data
            keyword_analysis = await seo_optimizer.extract_seo_keywords(
                content=request.content,
                title=request.title,
                topic=request.topic,
                target_keywords=target_keywords
            )

            return {
                "success": True,
                "seo_analysis": seo_analysis,
                "keyword_analysis": keyword_analysis,
                "recommendations": {
                    "high_priority": [s for s in seo_analysis['suggestions'] if s.get('priority') == 'high'],
                    "medium_priority": [s for s in seo_analysis['suggestions'] if s.get('priority') == 'medium'],
                    "low_priority": [s for s in seo_analysis['suggestions'] if s.get('priority') == 'low']
                }
            }

    except Exception as e:
        logger.error(f"SEO analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/extract-keywords")
async def extract_keywords(
    content: str,
    title: str = "",
    topic: str = "",
    max_keywords: int = 15,
    engine: DeepSearchEngine = Depends(get_search_engine)
):
    """
    Extract SEO keywords from content

    This endpoint extracts and scores potential SEO keywords from the provided content.
    """
    try:
        async with engine:
            article_generator = engine.ai_manager.article_generator
            seo_optimizer = article_generator.seo_optimizer

            keywords = await seo_optimizer.extract_seo_keywords(
                content=content,
                title=title,
                topic=topic
            )

            # Limit results
            limited_keywords = keywords[:max_keywords]

            return {
                "success": True,
                "keywords": limited_keywords,
                "total_extracted": len(keywords),
                "returned": len(limited_keywords)
            }

    except Exception as e:
        logger.error(f"Keyword extraction failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
