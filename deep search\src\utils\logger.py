"""
Logging configuration utility
"""

import os
import sys
from loguru import logger
from typing import Dict, Any


def setup_logging(config: Dict[str, Any]):
    """
    Setup logging configuration
    
    Args:
        config: Configuration dictionary
    """
    logging_config = config.get('logging', {})
    
    # Remove default logger
    logger.remove()
    
    # Console logging
    logger.add(
        sys.stdout,
        level=logging_config.get('level', 'INFO'),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # File logging
    log_file = logging_config.get('file', 'logs/deepsearch.log')
    if log_file:
        # Ensure log directory exists
        log_dir = os.path.dirname(log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        logger.add(
            log_file,
            level=logging_config.get('level', 'INFO'),
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation=logging_config.get('max_size', '10MB'),
            retention=logging_config.get('backup_count', 5),
            compression="zip"
        )
    
    logger.info("Logging configured successfully")


def get_logger(name: str):
    """
    Get a logger instance
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logger.bind(name=name)
