# DeepSearch Agent Pro 🚀

منصة متقدمة للبحث العميق وإنتاج المقالات بالذكاء الاصطناعي مع تحسين SEO ونشر مباشر على Blogger.

## 🌟 المميزات الجديدة

### 🔍 البحث العميق المحسن
- بحث مركز على مقال واحد عالي الجودة
- تحليل شامل للمحتوى واستخراج المعلومات
- اختيار أفضل المقالات بناءً على معايير الجودة والصلة

### 🤖 نظام إنتاج المقالات بـ Gemini AI
- دعم Gemini 2.5 Pro و Gemini 2.5 Flash
- إنتاج مقالات عالية الجودة باللهجة المصرية
- إضافة أخطاء إملائية بسيطة لتجنب كشف الذكاء الاصطناعي
- خيارات متعددة لأنواع المقالات (مهني، إخباري، تحليلي، إلخ)

### 📊 نظام SEO متقدم
- استخراج وتحسين الكلمات المفتاحية تلقائياً
- تحليل كثافة الكلمات المفتاحية
- اقتراحات تحسين المحتوى لتصدر نتائج البحث
- تقييم شامل لجودة SEO

### 📝 نشر مباشر على Blogger
- مصادقة آمنة باستخدام Service Account
- نشر تلقائي مع تحسين SEO
- إمكانية النشر كمسودة أو مقال منشور
- إضافة التصنيفات والكلمات المفتاحية تلقائياً

### 💻 واجهة مستخدم محسنة
- تصميم عصري وسهل الاستخدام
- إدارة شاملة للإعدادات
- نسخ HTML للصق مباشر في Blogger
- مؤشرات حالة النظام في الوقت الفعلي

## 🛠️ التقنيات المستخدمة

- **Python 3.8+** - لغة البرمجة الأساسية
- **FastAPI** - إطار عمل API السريع
- **Google Gemini AI** - نموذج الذكاء الاصطناعي لإنتاج المقالات
- **Blogger API** - للنشر المباشر على المدونات
- **Bootstrap 5** - للواجهة الأمامية
- **SQLAlchemy** - لإدارة قاعدة البيانات

## 📋 الوصف الأصلي

DeepSearch Agent هي أداة ذكية للبحث العميق في الإنترنت تجمع المحتوى، تنظّفه، تحلله دلالياً، وتقدمه جاهزًا للوكلاء البرمجيين لاستخدامه.

### ✨ الميزات الرئيسية

- 🕸️ **جمع البيانات الذكي**: كشط متقدم للمواقع باستخدام Scrapy
- 🧠 **معالجة ذكية متعددة**: دعم Gemini 2.0 Flash، Mistral AI، و Ollama مع التبديل التلقائي
- 🔍 **بحث متقدم**: دعم Brave Search API و DuckDuckGo
- 📊 **تحليل دلالي**: Embeddings و RAG للفهم العميق
- 💾 **تخزين ذكي**: قاعدة بيانات SQLite مع ChromaDB للبحث المتجه
- 🚀 **API سريع**: واجهة REST API باستخدام FastAPI
- 🌐 **واجهة ويب تفاعلية**: واجهة HTML حديثة للاختبار والإدارة

### 🎯 الاستخدامات

- متابعة آخر الأخبار والمستجدات
- أبحاث السوق والتحليل التنافسي
- تحليل الاتجاهات والموضوعات الرائجة
- البحث العلمي والأكاديمي
- جمع المعلومات للوكلاء الذكية

## 🏗️ هيكل المشروع

```
deep-search/
├── src/
│   ├── core/           # المكونات الأساسية
│   ├── scrapers/       # وحدات كشط المواقع
│   ├── ai/            # معالجة AI والتحليل
│   ├── database/      # قاعدة البيانات والتخزين
│   ├── api/           # واجهة برمجة التطبيقات
│   └── utils/         # أدوات مساعدة
├── tests/             # الاختبارات
├── config/            # ملفات التكوين
├── data/              # البيانات المحلية
└── docs/              # الوثائق
```

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية

- Python 3.9+
- pip أو conda

### التثبيت

```bash
# استنساخ المشروع
git clone <repository-url>
cd deep-search

# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows

# تثبيت المتطلبات
pip install -r requirements.txt
```

### التكوين

1. انسخ ملف التكوين النموذجي:
```bash
cp config/config.example.yaml config/config.yaml
```

2. أضف مفاتيح API الخاصة بك في `.env`:
```bash
BRAVE_API_KEY=your_brave_api_key
GEMINI_API_KEY=your_gemini_api_key
MISTRAL_API_KEY=your_mistral_api_key
OLLAMA_BASE_URL=http://localhost:11434
```

### التشغيل

```bash
# تشغيل API
python main.py --api

# أو تشغيل مباشر
uvicorn src.api.main:app --reload --host 0.0.0.0 --port 8000

# الوصول للواجهة التفاعلية
http://localhost:8000/web/index.html
```

## 📖 الاستخدام

### عبر API

```python
import requests

# بحث بسيط
response = requests.post("http://localhost:8000/search", json={
    "query": "آخر أخبار الذكاء الاصطناعي",
    "max_results": 10,
    "summarize": True
})

results = response.json()
```

### عبر Python مباشرة

```python
from src.core.search_engine import DeepSearchEngine

engine = DeepSearchEngine()
results = await engine.search("آخر أخبار التكنولوجيا", max_results=5)
```

## 🌐 الواجهة التفاعلية

### الواجهة المحسنة الجديدة
- **الرابط**: `http://localhost:8000/web/enhanced.html`
- **المميزات الجديدة**:
  - البحث العميق المتقدم
  - إنتاج المقالات بالذكاء الاصطناعي
  - نشر مباشر على Blogger
  - تحليل SEO متقدم
  - إعدادات شاملة للنظام

### واجهة البحث الأساسية
- **الرابط**: `http://localhost:8000/web/index.html`
- **الميزات**: بحث تفاعلي، عرض النتائج، إحصائيات مفصلة
- **أنواع البحث**: بحث عادي، بحث RAG (الإجابة الذكية)

### لوحة الإدارة
- **الرابط**: `http://localhost:8000/web/admin.html`
- **الميزات**: إدارة مقدمي الذكاء الاصطناعي، فحص الصحة، التبديل بين المقدمين

## 🚀 الاستخدام المتقدم

### 1. إعداد Gemini AI
```bash
# في الواجهة المحسنة، انتقل لتبويب "الإعدادات"
# أدخل Gemini API Key
# اختر النموذج المطلوب (Gemini 2.5 Pro أو Flash)
# اضغط "حفظ إعدادات Gemini"
```

### 2. إعداد Blogger API
```bash
# إنشاء Service Account في Google Cloud Console
# تحميل ملف JSON للـ Service Account
# إضافة Service Account كمحرر في مدونة Blogger
# في الواجهة، لصق محتوى JSON وإدخال Blog ID
```

### 3. سير العمل الكامل
```python
# 1. البحث العميق
response = requests.post("http://localhost:8000/deep-search", data={
    "query": "الذكاء الاصطناعي في التعليم",
    "target_language": "ar",
    "article_style": "professional"
})

# 2. إنتاج المقال
article_response = requests.post("http://localhost:8000/articles/generate", json={
    "topic": "الذكاء الاصطناعي في التعليم",
    "article_type": "professional",
    "use_deep_search": True,
    "egyptian_dialect": True,
    "seo_keywords": ["ذكاء اصطناعي", "تعليم", "تكنولوجيا"]
})

# 3. النشر على Blogger
publish_response = requests.post("http://localhost:8000/publishing/blogger/publish", json={
    "title": article_response.json()["article"]["title"],
    "content": article_response.json()["article"]["content"],
    "is_draft": False
})
```

### 4. تحليل SEO
```python
seo_response = requests.post("http://localhost:8000/articles/seo-analysis", json={
    "content": "محتوى المقال...",
    "title": "عنوان المقال",
    "target_keywords": ["كلمة مفتاحية 1", "كلمة مفتاحية 2"]
})
```

### مقدمو الذكاء الاصطناعي المدعومون
- **Google Gemini 2.0 Flash**: الأحدث والأسرع من Google
- **Mistral AI**: نماذج متقدمة للنصوص والتحليل
- **Ollama**: تشغيل النماذج محلياً (Mistral، Llama، إلخ)

### التبديل التلقائي
النظام يدعم التبديل التلقائي بين المقدمين:
- المقدم الأساسي: Gemini (افتراضي)
- المقدم الاحتياطي: Ollama
- التبديل عند الفشل أو عدم توفر الخدمة

## 🧪 الاختبارات

```bash
# تشغيل جميع الاختبارات
pytest

# تشغيل اختبارات محددة
pytest tests/test_scrapers.py -v

# تشغيل مع تغطية الكود
pytest --cov=src tests/
```

## 📚 الوثائق

- [دليل التطوير](docs/development.md)
- [مرجع API](docs/api_reference.md)
- [أمثلة الاستخدام](docs/examples.md)

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 🆘 الدعم

إذا واجهت أي مشاكل أو لديك أسئلة، يرجى فتح issue في GitHub.
