#!/usr/bin/env python3
"""
Quick Demo Launcher - Professional News Agent
"""

import webbrowser
import threading
import time
import subprocess
import sys
import os

def open_browser_delayed():
    """Open browser after server starts"""
    print("⏳ Waiting for server to start...")
    time.sleep(3)
    
    urls_to_try = [
        'http://localhost:8001/web/professional.html',
        'http://localhost:8001/docs',
        'http://localhost:8001/'
    ]
    
    for url in urls_to_try:
        try:
            print(f"🌐 Opening: {url}")
            webbrowser.open(url)
            break
        except Exception as e:
            print(f"❌ Failed to open {url}: {e}")
            continue

def main():
    print("🚀 DeepSearch Professional News Agent - Quick Demo")
    print("=" * 60)
    print("📰 Starting professional news API for AI agents...")
    print("🤖 Features:")
    print("  • Single article mode (default)")
    print("  • Maximum 3 articles when multiple requested")
    print("  • Full content extraction")
    print("  • Professional summaries")
    print("  • Duplicate prevention")
    print("  • Writing suggestions")
    print("=" * 60)
    
    # Check if demo_server.py exists
    if not os.path.exists('demo_server.py'):
        print("❌ demo_server.py not found!")
        print("Please make sure you're in the correct directory.")
        return
    
    # Start browser opening in background
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # Run the demo server
        print("🔄 Starting server...")
        subprocess.run([sys.executable, 'demo_server.py'], check=True)
    except KeyboardInterrupt:
        print("\n⏹️ Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Server failed to start: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure FastAPI is installed: pip install fastapi uvicorn")
        print("2. Check if port 8001 is available")
        print("3. Try running: python demo_server.py")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == '__main__':
    main()
