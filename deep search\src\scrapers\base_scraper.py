"""
Base scraper class for web scraping functionality
"""

import asyncio
import aiohttp
from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import time
import random
from loguru import logger


class BaseScraper(ABC):
    """Base class for all web scrapers"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.session = None
        self.user_agent = config.get('user_agent', 'DeepSearch-Agent/1.0')
        self.request_delay = config.get('request_delay', 1)
        self.timeout = config.get('download_timeout', 30)
        self.retry_attempts = config.get('retry_attempts', 3)
        
    async def __aenter__(self):
        """Async context manager entry"""
        connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'User-Agent': self.user_agent}
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def fetch_page(self, url: str, **kwargs) -> Optional[str]:
        """
        Fetch a single page content
        
        Args:
            url: URL to fetch
            **kwargs: Additional parameters for the request
            
        Returns:
            Page content as string or None if failed
        """
        for attempt in range(self.retry_attempts):
            try:
                await asyncio.sleep(self.request_delay + random.uniform(0, 0.5))
                
                async with self.session.get(url, **kwargs) as response:
                    if response.status == 200:
                        content = await response.text()
                        logger.info(f"Successfully fetched: {url}")
                        return content
                    else:
                        logger.warning(f"HTTP {response.status} for {url}")
                        
            except Exception as e:
                logger.error(f"Attempt {attempt + 1} failed for {url}: {str(e)}")
                if attempt < self.retry_attempts - 1:
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                    
        logger.error(f"Failed to fetch {url} after {self.retry_attempts} attempts")
        return None
    
    def clean_html(self, html_content: str) -> BeautifulSoup:
        """
        Clean and parse HTML content
        
        Args:
            html_content: Raw HTML content
            
        Returns:
            BeautifulSoup object
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Remove unwanted elements
        for element in soup(['script', 'style', 'nav', 'footer', 'header', 'aside']):
            element.decompose()
            
        return soup
    
    def extract_text(self, soup: BeautifulSoup) -> str:
        """
        Extract clean text from BeautifulSoup object
        
        Args:
            soup: BeautifulSoup object
            
        Returns:
            Clean text content
        """
        # Get text and clean it
        text = soup.get_text(separator=' ', strip=True)
        
        # Remove extra whitespace
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        return '\n'.join(lines)
    
    def extract_metadata(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """
        Extract metadata from the page
        
        Args:
            soup: BeautifulSoup object
            url: Original URL
            
        Returns:
            Dictionary containing metadata
        """
        metadata = {
            'url': url,
            'title': '',
            'description': '',
            'keywords': [],
            'author': '',
            'published_date': '',
            'language': 'ar'
        }
        
        # Extract title
        title_tag = soup.find('title')
        if title_tag:
            metadata['title'] = title_tag.get_text().strip()
        
        # Extract meta description
        desc_tag = soup.find('meta', attrs={'name': 'description'})
        if desc_tag:
            metadata['description'] = desc_tag.get('content', '').strip()
        
        # Extract keywords
        keywords_tag = soup.find('meta', attrs={'name': 'keywords'})
        if keywords_tag:
            keywords = keywords_tag.get('content', '')
            metadata['keywords'] = [k.strip() for k in keywords.split(',') if k.strip()]
        
        # Extract author
        author_tag = soup.find('meta', attrs={'name': 'author'})
        if author_tag:
            metadata['author'] = author_tag.get('content', '').strip()
        
        # Extract language
        lang_tag = soup.find('html')
        if lang_tag and lang_tag.get('lang'):
            metadata['language'] = lang_tag.get('lang')
        
        return metadata
    
    @abstractmethod
    async def scrape(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Abstract method for scraping implementation
        
        Args:
            query: Search query
            max_results: Maximum number of results to return
            
        Returns:
            List of scraped articles/content
        """
        pass
