2025-07-25 15:37:41 | INFO     | src.utils.logger:setup_logging:48 - Logging configured successfully
2025-07-25 15:38:13 | INFO     | src.utils.logger:setup_logging:48 - Logging configured successfully
2025-07-25 15:38:13 | INFO     | __main__:main:40 - Starting DeepSearch Agent...
2025-07-25 15:38:13 | INFO     | src.utils.logger:setup_logging:48 - Logging configured successfully
2025-07-25 15:38:13 | INFO     | __main__:main:40 - Starting DeepSearch Agent...
2025-07-25 15:38:13 | WARNING  | src.scrapers.brave_scraper:__init__:22 - Brave API key not found. Set BRAVE_API_KEY environment variable.
2025-07-25 15:38:13 | INFO     | src.scrapers.scraper_manager:_initialize_scrapers:27 - Brave scraper initialized
2025-07-25 15:38:13 | INFO     | src.scrapers.scraper_manager:_initialize_scrapers:34 - DuckDuckGo scraper initialized
2025-07-25 15:38:13 | INFO     | __main__:main:48 - Starting API server...
2025-07-25 15:38:17 | INFO     | src.database.database_manager:_initialize_database:48 - Database initialized: sqlite:///data/deepsearch.db
2025-07-25 15:38:17 | INFO     | src.ai.gemini_provider:_initialize_client:32 - Gemini client initialized with model: gemini-2.0-flash-exp
2025-07-25 15:38:17 | INFO     | src.ai.ai_manager:_initialize_providers:38 - Initialized primary AI provider: gemini
2025-07-25 15:38:17 | INFO     | src.ai.ai_manager:_initialize_providers:44 - Initialized fallback AI provider: ollama
2025-07-25 15:38:18 | INFO     | src.api.main:lifespan:35 - Starting DeepSearch API...
2025-07-25 15:38:18 | INFO     | src.utils.config_loader:load_config:98 - Configuration loaded from: config/config.yaml
2025-07-25 15:38:18 | WARNING  | src.scrapers.brave_scraper:__init__:22 - Brave API key not found. Set BRAVE_API_KEY environment variable.
2025-07-25 15:38:18 | INFO     | src.scrapers.scraper_manager:_initialize_scrapers:27 - Brave scraper initialized
2025-07-25 15:38:18 | INFO     | src.scrapers.scraper_manager:_initialize_scrapers:34 - DuckDuckGo scraper initialized
2025-07-25 15:38:18 | INFO     | src.database.database_manager:_initialize_database:48 - Database initialized: sqlite:///data/deepsearch.db
2025-07-25 15:38:18 | INFO     | src.api.main:lifespan:43 - DeepSearch API started successfully
2025-07-25 15:38:57 | INFO     | src.ai.gemini_provider:_initialize_client:32 - Gemini client initialized with model: gemini-2.0-flash-exp
2025-07-25 15:38:57 | INFO     | src.ai.ai_manager:_initialize_providers:38 - Initialized primary AI provider: gemini
2025-07-25 15:38:57 | INFO     | src.ai.ai_manager:_initialize_providers:44 - Initialized fallback AI provider: ollama
2025-07-25 15:42:09 | INFO     | src.ai.embedding_manager:_initialize_model:26 - Embedding model initialized: sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
2025-07-25 15:42:09 | INFO     | src.ai.embedding_manager:_initialize_model:26 - Embedding model initialized: sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
2025-07-25 15:42:09 | INFO     | __main__:main:110 - Performing health check...
2025-07-25 15:42:09 | INFO     | src.scrapers.scraper_manager:health_check:202 - brave scraper is healthy
2025-07-25 15:42:09 | INFO     | src.scrapers.scraper_manager:health_check:202 - brave scraper is healthy
2025-07-25 15:42:09 | INFO     | src.scrapers.scraper_manager:health_check:202 - duckduckgo scraper is healthy
2025-07-25 15:42:09 | INFO     | src.scrapers.scraper_manager:health_check:202 - duckduckgo scraper is healthy
2025-07-25 15:42:09 | INFO     | src.ai.gemini_provider:_initialize_client:32 - Gemini client initialized with model: gemini-2.0-flash-exp
2025-07-25 15:42:09 | INFO     | src.ai.ai_manager:_initialize_providers:38 - Initialized primary AI provider: gemini
2025-07-25 15:42:09 | INFO     | src.ai.ai_manager:_initialize_providers:44 - Initialized fallback AI provider: ollama
2025-07-25 15:42:11 | INFO     | src.ai.gemini_provider:generate_text:83 - Generated text with Gemini model: gemini-2.0-flash-exp
2025-07-25 15:42:11 | INFO     | src.ai.gemini_provider:health_check:208 - Gemini health check passed
2025-07-25 15:42:11 | INFO     | src.database.database_manager:get_database_stats:393 - Database stats: {'total_articles': 0, 'total_queries': 0, 'total_embeddings': 0, 'total_cache_entries': 0, 'recent_articles_24h': 0}
2025-07-25 15:42:13 | INFO     | src.ai.embedding_manager:_initialize_model:26 - Embedding model initialized: sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
2025-07-25 15:42:13 | INFO     | src.core.search_engine:search:71 - Starting search for: 'ابحث عن اخر خبر يخصالكرة القدم'
2025-07-25 15:42:13 | INFO     | src.ai.gemini_provider:generate_text:83 - Generated text with Gemini model: gemini-2.0-flash-exp
2025-07-25 15:42:13 | INFO     | src.ai.gemini_provider:health_check:208 - Gemini health check passed
2025-07-25 15:42:13 | INFO     | src.database.database_manager:get_database_stats:393 - Database stats: {'total_articles': 0, 'total_queries': 0, 'total_embeddings': 0, 'total_cache_entries': 0, 'recent_articles_24h': 0}
2025-07-25 15:42:16 | INFO     | src.ai.gemini_provider:generate_text:83 - Generated text with Gemini model: gemini-2.0-flash-exp
2025-07-25 15:42:16 | INFO     | src.ai.ai_manager:enhance_search_query:159 - Enhanced query: 'ابحث عن اخر خبر يخصالكرة القدم' -> 'الاستعلام المحسن: **"آخر أخبار كرة القدم" أو "أحدث أخبار كرة القدم"**

**شرح التحسين:**

*   **"آخر" أو "أحدث":** استخدام كلمات أكثر دقة مثل "آخر" أو "أحدث" بدلاً من "اخر" يضمن أن محركات البحث ستركز على الأخبار الحديثة فقط.
*   **"أخبار كرة القدم":** ترتيب الكلمات بشكل طبيعي ("أخبار كرة القدم") يجعل الاستعلام أكثر وضوحًا لمحركات البحث.

**خيارات إضافية (حسب الحاجة):**

*   **تحديد مصدر الأخبار:**  إذا كنت تفضل مصدرًا معينًا للأخبار، يمكنك إضافة اسم المصدر إلى الاستعلام، مثل: "آخر أخبار كرة القدم [اسم المصدر]".
*   **تحديد دوري أو فريق:** إذا كنت مهتمًا بدوري أو فريق معين، يمكنك إضافة اسم الدوري أو الفريق، مثل: "آخر أخبار الدوري الإنجليزي الممتاز" أو "أحدث أخبار ريال مدريد".
*   **تحديد فترة زمنية:** إذا كنت تبحث عن أخبار خلال فترة زمنية محددة، يمكنك استخدام أدوات البحث المتقدمة في محرك البحث لتحديد النطاق الزمني.'
2025-07-25 15:42:16 | INFO     | src.scrapers.scraper_manager:search_and_scrape:67 - Starting search with engines: ['brave', 'duckduckgo']
2025-07-25 15:42:16 | INFO     | src.scrapers.brave_scraper:scrape:121 - Starting Brave search for: الاستعلام المحسن: **"آخر أخبار كرة القدم" أو "أحدث أخبار كرة القدم"**

**شرح التحسين:**

*   **"آخر" أو "أحدث":** استخدام كلمات أكثر دقة مثل "آخر" أو "أحدث" بدلاً من "اخر" يضمن أن محركات البحث ستركز على الأخبار الحديثة فقط.
*   **"أخبار كرة القدم":** ترتيب الكلمات بشكل طبيعي ("أخبار كرة القدم") يجعل الاستعلام أكثر وضوحًا لمحركات البحث.

**خيارات إضافية (حسب الحاجة):**

*   **تحديد مصدر الأخبار:**  إذا كنت تفضل مصدرًا معينًا للأخبار، يمكنك إضافة اسم المصدر إلى الاستعلام، مثل: "آخر أخبار كرة القدم [اسم المصدر]".
*   **تحديد دوري أو فريق:** إذا كنت مهتمًا بدوري أو فريق معين، يمكنك إضافة اسم الدوري أو الفريق، مثل: "آخر أخبار الدوري الإنجليزي الممتاز" أو "أحدث أخبار ريال مدريد".
*   **تحديد فترة زمنية:** إذا كنت تبحث عن أخبار خلال فترة زمنية محددة، يمكنك استخدام أدوات البحث المتقدمة في محرك البحث لتحديد النطاق الزمني.
2025-07-25 15:42:16 | ERROR    | src.scrapers.brave_scraper:search_brave:37 - Brave API key not available
2025-07-25 15:42:16 | WARNING  | src.scrapers.brave_scraper:scrape:126 - No search results from Brave API
2025-07-25 15:42:16 | INFO     | src.scrapers.scraper_manager:search_and_scrape:82 - brave returned 0 results
2025-07-25 15:42:16 | INFO     | src.scrapers.duckduckgo_scraper:scrape:138 - Starting DuckDuckGo search for: الاستعلام المحسن: **"آخر أخبار كرة القدم" أو "أحدث أخبار كرة القدم"**

**شرح التحسين:**

*   **"آخر" أو "أحدث":** استخدام كلمات أكثر دقة مثل "آخر" أو "أحدث" بدلاً من "اخر" يضمن أن محركات البحث ستركز على الأخبار الحديثة فقط.
*   **"أخبار كرة القدم":** ترتيب الكلمات بشكل طبيعي ("أخبار كرة القدم") يجعل الاستعلام أكثر وضوحًا لمحركات البحث.

**خيارات إضافية (حسب الحاجة):**

*   **تحديد مصدر الأخبار:**  إذا كنت تفضل مصدرًا معينًا للأخبار، يمكنك إضافة اسم المصدر إلى الاستعلام، مثل: "آخر أخبار كرة القدم [اسم المصدر]".
*   **تحديد دوري أو فريق:** إذا كنت مهتمًا بدوري أو فريق معين، يمكنك إضافة اسم الدوري أو الفريق، مثل: "آخر أخبار الدوري الإنجليزي الممتاز" أو "أحدث أخبار ريال مدريد".
*   **تحديد فترة زمنية:** إذا كنت تبحث عن أخبار خلال فترة زمنية محددة، يمكنك استخدام أدوات البحث المتقدمة في محرك البحث لتحديد النطاق الزمني.
2025-07-25 15:42:16 | INFO     | src.scrapers.duckduckgo_scraper:search_duckduckgo:44 - DuckDuckGo search returned 10 results for: الاستعلام المحسن: **"آخر أخبار كرة القدم" أو "أحدث أخبار كرة القدم"**

**شرح التحسين:**

*   **"آخر" أو "أحدث":** استخدام كلمات أكثر دقة مثل "آخر" أو "أحدث" بدلاً من "اخر" يضمن أن محركات البحث ستركز على الأخبار الحديثة فقط.
*   **"أخبار كرة القدم":** ترتيب الكلمات بشكل طبيعي ("أخبار كرة القدم") يجعل الاستعلام أكثر وضوحًا لمحركات البحث.

**خيارات إضافية (حسب الحاجة):**

*   **تحديد مصدر الأخبار:**  إذا كنت تفضل مصدرًا معينًا للأخبار، يمكنك إضافة اسم المصدر إلى الاستعلام، مثل: "آخر أخبار كرة القدم [اسم المصدر]".
*   **تحديد دوري أو فريق:** إذا كنت مهتمًا بدوري أو فريق معين، يمكنك إضافة اسم الدوري أو الفريق، مثل: "آخر أخبار الدوري الإنجليزي الممتاز" أو "أحدث أخبار ريال مدريد".
*   **تحديد فترة زمنية:** إذا كنت تبحث عن أخبار خلال فترة زمنية محددة، يمكنك استخدام أدوات البحث المتقدمة في محرك البحث لتحديد النطاق الزمني.
2025-07-25 15:42:16 | INFO     | src.scrapers.duckduckgo_scraper:scrape:156 - Found 10 URLs to scrape
2025-07-25 15:42:18 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://support.microsoft.com/en-us/topic/why-has-my-enter-key-turned-into-a-send-button-in-whatsapp-2084e9da-aa73-45a7-a591-e8220d951b29
2025-07-25 15:42:18 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://support.microsoft.com/en-us/windows/configure-startup-applications-in-windows-115a420a-0bff-4a6f-90e0-1934c844e473
2025-07-25 15:42:19 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://support.microsoft.com/en-us/windows/turn-on-app-permissions-for-your-microphone-in-windows-94991183-f69d-b4cf-4679-c98ca45f577a
2025-07-25 15:42:19 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://support.microsoft.com/en-us/topic/customer-service-phone-numbers-c0389ade-5640-e588-8b0e-28de8afeb3f2
2025-07-25 15:42:19 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://support.microsoft.com/en-us/topic/troubleshooting-calls-in-the-phone-link-1644add0-a51e-ff32-1b55-82b1ecf6b359
2025-07-25 15:42:20 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://support.microsoft.com/en-us/windows/fix-microphone-problems-5f230348-106d-bfa4-1db5-336f35576011
2025-07-25 15:42:20 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://support.microsoft.com/en-us/windows/app-permissions-aea98a7c-b61a-1930-6ed0-47f0ed2ee15c
2025-07-25 15:42:21 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://support.microsoft.com/en-us/office/share-your-powerpoint-presentation-with-others-a6308d9d-a0a8-443b-8e1c-0f4983f0afd1
2025-07-25 15:42:21 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://support.microsoft.com/en-us/windows/fix-bluetooth-problems-in-windows-723e092f-03fa-858b-5c80-131ec3fba75c
2025-07-25 15:42:21 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://www.answers.com/travel-information/Is-it-safe-to-send-a-passport-copy-through-whatsapp
2025-07-25 15:42:21 | INFO     | src.scrapers.duckduckgo_scraper:scrape:175 - Successfully scraped 10 articles
2025-07-25 15:42:21 | INFO     | src.scrapers.scraper_manager:search_and_scrape:82 - duckduckgo returned 10 results
2025-07-25 15:42:21 | INFO     | src.scrapers.scraper_manager:_merge_results:145 - Merged results: 10 unique articles from 2 engines
2025-07-25 15:42:23 | INFO     | src.ai.gemini_provider:generate_text:83 - Generated text with Gemini model: gemini-2.0-flash-exp
2025-07-25 15:42:25 | INFO     | src.ai.gemini_provider:generate_text:83 - Generated text with Gemini model: gemini-2.0-flash-exp
2025-07-25 15:42:27 | INFO     | src.ai.gemini_provider:generate_text:83 - Generated text with Gemini model: gemini-2.0-flash-exp
2025-07-25 15:42:29 | INFO     | src.ai.gemini_provider:generate_text:83 - Generated text with Gemini model: gemini-2.0-flash-exp
2025-07-25 15:42:31 | INFO     | src.ai.gemini_provider:generate_text:83 - Generated text with Gemini model: gemini-2.0-flash-exp
2025-07-25 15:42:33 | INFO     | src.ai.gemini_provider:generate_text:83 - Generated text with Gemini model: gemini-2.0-flash-exp
2025-07-25 15:42:36 | INFO     | src.ai.gemini_provider:generate_text:83 - Generated text with Gemini model: gemini-2.0-flash-exp
2025-07-25 15:42:37 | INFO     | src.ai.gemini_provider:generate_text:83 - Generated text with Gemini model: gemini-2.0-flash-exp
2025-07-25 15:42:37 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 22
}
]
2025-07-25 15:42:37 | WARNING  | src.ai.ai_manager:_try_with_fallback:85 - Primary provider returned empty result, trying fallback
2025-07-25 15:42:37 | ERROR    | src.ai.ollama_provider:generate_text:83 - Ollama generation failed: Session is closed
2025-07-25 15:42:37 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 22
}
]
2025-07-25 15:42:37 | WARNING  | src.ai.ai_manager:_try_with_fallback:85 - Primary provider returned empty result, trying fallback
2025-07-25 15:42:37 | ERROR    | src.ai.ollama_provider:generate_text:83 - Ollama generation failed: Session is closed
2025-07-25 15:42:37 | INFO     | src.ai.ai_manager:summarize_articles:140 - Summarized 10 articles
2025-07-25 15:42:38 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 21
}
]
2025-07-25 15:42:38 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 21
}
]
2025-07-25 15:42:38 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 21
}
]
2025-07-25 15:42:38 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 21
}
]
2025-07-25 15:42:38 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 21
}
]
2025-07-25 15:42:38 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 21
}
]
2025-07-25 15:42:39 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 20
}
]
2025-07-25 15:42:39 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 20
}
]
2025-07-25 15:42:39 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 20
}
]
2025-07-25 15:42:39 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 20
}
]
2025-07-25 15:42:39 | INFO     | src.ai.ai_manager:extract_keywords_from_articles:203 - Extracted keywords for 10 articles
2025-07-25 15:42:39 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 20
}
]
2025-07-25 15:42:39 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 20
}
]
2025-07-25 15:42:39 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 20
}
]
2025-07-25 15:42:40 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 19
}
]
2025-07-25 15:42:40 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 19
}
]
2025-07-25 15:42:40 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 19
}
]
2025-07-25 15:42:40 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 19
}
]
2025-07-25 15:42:40 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 19
}
]
2025-07-25 15:42:40 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 19
}
]
2025-07-25 15:42:41 | ERROR    | src.ai.gemini_provider:generate_text:90 - Gemini generation failed: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 18
}
]
2025-07-25 15:42:41 | INFO     | src.ai.ai_manager:analyze_article_sentiment:334 - Analyzed sentiment for 10 articles
2025-07-25 15:42:41 | INFO     | src.database.database_manager:save_search_query:93 - Saved search query: ابحث عن اخر خبر يخصالكرة القدم...
2025-07-25 15:42:41 | INFO     | src.database.database_manager:save_article:139 - Saved article: Customer service phone numbers - Microsoft Support...
2025-07-25 15:42:41 | INFO     | src.database.database_manager:save_article:139 - Saved article: App permissions - Microsoft Support...
2025-07-25 15:42:41 | INFO     | src.database.database_manager:save_article:139 - Saved article: Fix Bluetooth problems in Windows - Microsoft Supp...
2025-07-25 15:42:41 | INFO     | src.database.database_manager:save_article:139 - Saved article: Share your PowerPoint presentation with others - M...
2025-07-25 15:42:42 | INFO     | src.database.database_manager:save_article:139 - Saved article: Fix microphone problems - Microsoft Support...
2025-07-25 15:42:42 | INFO     | src.database.database_manager:save_article:139 - Saved article: Troubleshooting calls in the Phone Link  - Microso...
2025-07-25 15:42:42 | INFO     | src.database.database_manager:save_article:139 - Saved article: Configure Startup Applications in Windows - Micros...
2025-07-25 15:42:42 | INFO     | src.database.database_manager:save_article:139 - Saved article: Is it safe to send a passport copy through WhatsAp...
2025-07-25 15:42:42 | INFO     | src.database.database_manager:save_article:139 - Saved article: Turn on app permissions for your microphone in Win...
2025-07-25 15:42:43 | INFO     | src.database.database_manager:save_article:139 - Saved article: Why has my enter key turned into a send button in ...
2025-07-25 15:42:43 | INFO     | src.core.search_engine:search:153 - Search completed in 29.28s with 10 results
2025-07-25 15:42:43 | ERROR    | src.api.main:search:141 - Search failed: 10 validation errors for SearchResponse
articles.0.scraped_at
  Input should be a valid string [type=string_type, input_value=221545.8516597, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
articles.1.scraped_at
  Input should be a valid string [type=string_type, input_value=221547.3570065, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
articles.2.scraped_at
  Input should be a valid string [type=string_type, input_value=221548.0618883, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
articles.3.scraped_at
  Input should be a valid string [type=string_type, input_value=221547.7723845, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
articles.4.scraped_at
  Input should be a valid string [type=string_type, input_value=221547.273591, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
articles.5.scraped_at
  Input should be a valid string [type=string_type, input_value=221545.913094, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
articles.6.scraped_at
  Input should be a valid string [type=string_type, input_value=221545.5659178, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
articles.7.scraped_at
  Input should be a valid string [type=string_type, input_value=221548.0800873, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
articles.8.scraped_at
  Input should be a valid string [type=string_type, input_value=221545.6270431, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
articles.9.scraped_at
  Input should be a valid string [type=string_type, input_value=221545.510931, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
2025-07-25 15:49:59 | INFO     | src.utils.logger:setup_logging:48 - Logging configured successfully
2025-07-25 15:50:18 | INFO     | src.api.main:lifespan:35 - Starting DeepSearch API...
2025-07-25 15:50:18 | INFO     | src.utils.config_loader:load_config:98 - Configuration loaded from: config/config.yaml
2025-07-25 15:50:18 | WARNING  | src.scrapers.brave_scraper:__init__:23 - Brave API key not found. Set BRAVE_API_KEY environment variable.
2025-07-25 15:50:18 | INFO     | src.scrapers.scraper_manager:_initialize_scrapers:27 - Brave scraper initialized
2025-07-25 15:50:18 | INFO     | src.scrapers.scraper_manager:_initialize_scrapers:34 - DuckDuckGo scraper initialized
2025-07-25 15:50:18 | INFO     | src.database.database_manager:_initialize_database:48 - Database initialized: sqlite:///data/deepsearch.db
2025-07-25 15:50:18 | INFO     | src.api.main:lifespan:43 - DeepSearch API started successfully
2025-07-25 15:53:33 | INFO     | src.ai.ai_manager:_initialize_providers:50 - Initialized primary AI provider: mistral
2025-07-25 15:53:33 | INFO     | src.ai.ai_manager:_initialize_providers:56 - Initialized fallback AI provider: ollama
2025-07-25 15:53:40 | INFO     | src.ai.embedding_manager:_initialize_model:26 - Embedding model initialized: sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
2025-07-25 15:53:40 | INFO     | src.core.search_engine:search:71 - Starting search for: 'ابحث عن اخبر اخبار عن العاب'
2025-07-25 15:53:40 | INFO     | src.scrapers.scraper_manager:search_and_scrape:67 - Starting search with engines: ['brave', 'duckduckgo']
2025-07-25 15:53:40 | INFO     | src.scrapers.brave_scraper:scrape:122 - Starting Brave search for: ابحث عن اخبر اخبار عن العاب
2025-07-25 15:53:40 | ERROR    | src.scrapers.brave_scraper:search_brave:38 - Brave API key not available
2025-07-25 15:53:40 | WARNING  | src.scrapers.brave_scraper:scrape:127 - No search results from Brave API
2025-07-25 15:53:40 | INFO     | src.scrapers.scraper_manager:search_and_scrape:82 - brave returned 0 results
2025-07-25 15:53:40 | INFO     | src.scrapers.duckduckgo_scraper:scrape:139 - Starting DuckDuckGo search for: ابحث عن اخبر اخبار عن العاب
2025-07-25 15:53:41 | INFO     | src.scrapers.duckduckgo_scraper:search_duckduckgo:45 - DuckDuckGo search returned 3 results for: ابحث عن اخبر اخبار عن العاب
2025-07-25 15:53:41 | INFO     | src.scrapers.duckduckgo_scraper:scrape:157 - Found 3 URLs to scrape
2025-07-25 15:53:42 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://www.bbc.com/arabic/articles/cm2ljvz25r0o
2025-07-25 15:53:43 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://t.me/s/argames?before=110404
2025-07-25 15:53:43 | WARNING  | src.scrapers.base_scraper:fetch_page:65 - HTTP 403 for https://www.alarabiya.net/technology/video-games/2025/07/22/العاب-جديدة-الاسبوع-الثاني-من-كاس-العالم-للرياضات-الالكترونية-2025
2025-07-25 15:53:44 | WARNING  | src.scrapers.base_scraper:fetch_page:65 - HTTP 403 for https://www.alarabiya.net/technology/video-games/2025/07/22/العاب-جديدة-الاسبوع-الثاني-من-كاس-العالم-للرياضات-الالكترونية-2025
2025-07-25 15:53:45 | WARNING  | src.scrapers.base_scraper:fetch_page:65 - HTTP 403 for https://www.alarabiya.net/technology/video-games/2025/07/22/العاب-جديدة-الاسبوع-الثاني-من-كاس-العالم-للرياضات-الالكترونية-2025
2025-07-25 15:53:45 | ERROR    | src.scrapers.base_scraper:fetch_page:72 - Failed to fetch https://www.alarabiya.net/technology/video-games/2025/07/22/العاب-جديدة-الاسبوع-الثاني-من-كاس-العالم-للرياضات-الالكترونية-2025 after 3 attempts
2025-07-25 15:53:45 | INFO     | src.scrapers.duckduckgo_scraper:scrape:176 - Successfully scraped 3 articles
2025-07-25 15:53:45 | INFO     | src.scrapers.scraper_manager:search_and_scrape:82 - duckduckgo returned 3 results
2025-07-25 15:53:45 | INFO     | src.scrapers.scraper_manager:_merge_results:145 - Merged results: 3 unique articles from 2 engines
2025-07-25 15:53:57 | INFO     | src.ai.mistral_provider:generate_text:89 - Generated text with Mistral model: mistral-large-latest
2025-07-25 15:54:06 | INFO     | src.ai.mistral_provider:generate_text:89 - Generated text with Mistral model: mistral-large-latest
2025-07-25 15:54:06 | INFO     | src.ai.ai_manager:summarize_articles:223 - Summarized 3 articles (2 processed with AI)
2025-07-25 15:54:08 | INFO     | src.ai.mistral_provider:generate_text:89 - Generated text with Mistral model: mistral-large-latest
2025-07-25 15:54:09 | INFO     | src.ai.mistral_provider:generate_text:89 - Generated text with Mistral model: mistral-large-latest
2025-07-25 15:54:09 | INFO     | src.ai.ai_manager:extract_keywords_from_articles:301 - Extracted keywords for 3 articles (2 processed with AI)
2025-07-25 15:54:09 | INFO     | src.ai.ai_manager:analyze_article_sentiment:447 - Processed sentiment for 3 articles (analysis disabled)
2025-07-25 15:54:09 | INFO     | src.database.database_manager:save_search_query:93 - Saved search query: ابحث عن اخبر اخبار عن العاب...
2025-07-25 15:54:09 | INFO     | src.database.database_manager:save_article:139 - Saved article: ألعاب جديدة.. الأسبوع الثاني من كأس العالم للرياضا...
2025-07-25 15:54:09 | INFO     | src.database.database_manager:save_article:139 - Saved article: أخبار الألعاب – Telegram...
2025-07-25 15:54:10 | INFO     | src.database.database_manager:save_article:139 - Saved article: كيف تحوّلت ألعاب الفيديو إلى وظيفة مربحة للشباب؟ -...
2025-07-25 15:54:10 | INFO     | src.core.search_engine:search:153 - Search completed in 29.58s with 3 results
2025-07-25 16:28:58 | INFO     | src.utils.logger:setup_logging:48 - Logging configured successfully
2025-07-25 16:29:13 | INFO     | src.api.main:lifespan:36 - Starting DeepSearch API...
2025-07-25 16:29:13 | INFO     | src.utils.config_loader:load_config:98 - Configuration loaded from: config/config.yaml
2025-07-25 16:29:13 | WARNING  | src.scrapers.brave_scraper:__init__:23 - Brave API key not found. Set BRAVE_API_KEY environment variable.
2025-07-25 16:29:13 | INFO     | src.scrapers.scraper_manager:_initialize_scrapers:27 - Brave scraper initialized
2025-07-25 16:29:13 | INFO     | src.scrapers.scraper_manager:_initialize_scrapers:34 - DuckDuckGo scraper initialized
2025-07-25 16:29:14 | INFO     | src.database.database_manager:_initialize_database:48 - Database initialized: sqlite:///data/deepsearch.db
2025-07-25 16:29:14 | INFO     | src.api.main:lifespan:44 - DeepSearch API started successfully
2025-07-25 16:29:29 | INFO     | src.utils.config_loader:load_config:98 - Configuration loaded from: config/config.yaml
2025-07-25 16:29:29 | WARNING  | src.scrapers.brave_scraper:__init__:23 - Brave API key not found. Set BRAVE_API_KEY environment variable.
2025-07-25 16:29:29 | INFO     | src.scrapers.scraper_manager:_initialize_scrapers:27 - Brave scraper initialized
2025-07-25 16:29:29 | INFO     | src.scrapers.scraper_manager:_initialize_scrapers:34 - DuckDuckGo scraper initialized
2025-07-25 16:29:29 | INFO     | src.database.database_manager:_initialize_database:48 - Database initialized: sqlite:///data/deepsearch.db
2025-07-25 16:29:29 | INFO     | src.ai.ai_manager:_initialize_providers:50 - Initialized primary AI provider: mistral
2025-07-25 16:29:29 | INFO     | src.ai.ai_manager:_initialize_providers:56 - Initialized fallback AI provider: ollama
2025-07-25 16:29:49 | INFO     | src.ai.embedding_manager:_initialize_model:26 - Embedding model initialized: sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
2025-07-25 16:29:49 | INFO     | src.news.news_engine:search_latest_news:175 - Searching latest news for: 'أخبار عاجلة اليوم'
2025-07-25 16:29:49 | INFO     | src.news.news_engine:search_latest_news:179 - Enhanced news query: 'أخبار عاجلة اليوم اليوم 2025-07-25 آخر الأخبار خبر'
2025-07-25 16:29:49 | INFO     | src.scrapers.scraper_manager:search_and_scrape:67 - Starting search with engines: ['duckduckgo']
2025-07-25 16:29:49 | INFO     | src.scrapers.duckduckgo_scraper:scrape:139 - Starting DuckDuckGo search for: أخبار عاجلة اليوم اليوم 2025-07-25 آخر الأخبار خبر
2025-07-25 16:29:54 | INFO     | src.scrapers.duckduckgo_scraper:search_duckduckgo:45 - DuckDuckGo search returned 17 results for: أخبار عاجلة اليوم اليوم 2025-07-25 آخر الأخبار خبر
2025-07-25 16:29:54 | INFO     | src.scrapers.duckduckgo_scraper:scrape:157 - Found 17 URLs to scrape
2025-07-25 16:29:56 | WARNING  | src.scrapers.base_scraper:fetch_page:65 - HTTP 403 for https://www.alarabiya.net/
2025-07-25 16:29:56 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://www.youm7.com/Section/أخبار-عاجلة/65/1
2025-07-25 16:29:56 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://www.aljazeera.net/
2025-07-25 16:29:56 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://www.shahrekhabar.com/
2025-07-25 16:29:56 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://www.skynewsarabia.com/breaking-news-الأخبار-العاجلة
2025-07-25 16:29:58 | WARNING  | src.scrapers.base_scraper:fetch_page:65 - HTTP 403 for https://www.alarabiya.net/
2025-07-25 16:29:58 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://www.aljazeeramubasher.net/news/
2025-07-25 16:29:58 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://amp.skynewsarabia.com/
2025-07-25 16:29:58 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://www.skynewsarabia.com/latest-news-أحدث-الأخبار
2025-07-25 16:29:58 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://www.elnashra.com/
2025-07-25 16:29:59 | WARNING  | src.scrapers.base_scraper:fetch_page:65 - HTTP 403 for https://www.alarabiya.net/
2025-07-25 16:29:59 | ERROR    | src.scrapers.base_scraper:fetch_page:72 - Failed to fetch https://www.alarabiya.net/ after 3 attempts
2025-07-25 16:29:59 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://aawsat.com/
2025-07-25 16:30:00 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://kora25.com/أخبار-نادى-الزمالك-إصابة-ثنائى-الزمال/
2025-07-25 16:30:00 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://samaworld.com/2025/07/25/بطولة-العالم-للبلياردو-في-جدة-32-لاعبًا/
2025-07-25 16:30:02 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://www.directoriocubano.info/ar/cuba/tasas-de-cambio-hoy-en-cuba-el-euro-sigue-subiendo-y-se-amplia-la-brecha-con-el-peso-cubano/
2025-07-25 16:30:02 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://modonnew.com/ar/2025/07/25/الليلة-الأهلي-يواجه-البنزرتي-التونسي/
2025-07-25 16:30:03 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://alkhabaralyemeni.net/2025/07/21/306945/
2025-07-25 16:30:03 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://www.lbcgroup.tv/news/world/868730/الأونروا-موظفو-الوكالة-والأطباء-يغمى-عليهم-بسبب-الجوع-في-غزة/ar
2025-07-25 16:30:09 | INFO     | src.scrapers.base_scraper:fetch_page:62 - Successfully fetched: https://112112.org/797894/
2025-07-25 16:30:09 | INFO     | src.scrapers.duckduckgo_scraper:scrape:176 - Successfully scraped 17 articles
2025-07-25 16:30:09 | INFO     | src.scrapers.scraper_manager:search_and_scrape:82 - duckduckgo returned 17 results
2025-07-25 16:30:09 | INFO     | src.scrapers.scraper_manager:_merge_results:145 - Merged results: 17 unique articles from 1 engines
2025-07-25 16:30:16 | INFO     | src.ai.mistral_provider:generate_text:89 - Generated text with Mistral model: mistral-large-latest
2025-07-25 16:30:16 | ERROR    | src.ai.mistral_provider:generate_text:96 - Mistral API error 429: {"object":"error","message":"Service tier capacity exceeded for this model.","type":"service_tier_capacity_exceeded","param":null,"code":"3505"}
2025-07-25 16:30:17 | ERROR    | src.ai.mistral_provider:generate_text:96 - Mistral API error 429: {"object":"error","message":"Service tier capacity exceeded for this model.","type":"service_tier_capacity_exceeded","param":null,"code":"3505"}
2025-07-25 16:30:19 | ERROR    | src.ai.mistral_provider:generate_text:96 - Mistral API error 429: {"object":"error","message":"Service tier capacity exceeded for this model.","type":"service_tier_capacity_exceeded","param":null,"code":"3505"}
2025-07-25 16:30:23 | INFO     | src.ai.mistral_provider:generate_text:89 - Generated text with Mistral model: mistral-large-latest
2025-07-25 16:30:23 | ERROR    | src.ai.mistral_provider:generate_text:96 - Mistral API error 429: {"object":"error","message":"Service tier capacity exceeded for this model.","type":"service_tier_capacity_exceeded","param":null,"code":"3505"}
2025-07-25 16:30:23 | ERROR    | src.ai.mistral_provider:generate_text:96 - Mistral API error 429: {"object":"error","message":"Service tier capacity exceeded for this model.","type":"service_tier_capacity_exceeded","param":null,"code":"3505"}
2025-07-25 16:30:29 | INFO     | src.ai.mistral_provider:generate_text:89 - Generated text with Mistral model: mistral-large-latest
2025-07-25 16:30:29 | ERROR    | src.ai.mistral_provider:generate_text:96 - Mistral API error 429: {"object":"error","message":"Service tier capacity exceeded for this model.","type":"service_tier_capacity_exceeded","param":null,"code":"3505"}
2025-07-25 16:30:29 | ERROR    | src.ai.mistral_provider:generate_text:96 - Mistral API error 429: {"object":"error","message":"Service tier capacity exceeded for this model.","type":"service_tier_capacity_exceeded","param":null,"code":"3505"}
2025-07-25 16:30:29 | INFO     | src.database.database_manager:save_article:139 - Saved article: الجزيرة نت: آخر أخبار اليوم حول العالم...
2025-07-25 16:30:29 | INFO     | src.database.database_manager:save_article:139 - Saved article: العربية | أبرز الأخبار العالمية والمحلية العاجلة...
2025-07-25 16:30:29 | INFO     | src.database.database_manager:save_article:139 - Saved article: أخبار عاجلة - اليوم السابع...
2025-07-25 16:30:30 | INFO     | src.database.database_manager:save_article:139 - Saved article: الشرق الأوسط: تغطية شاملة لأخبار اليوم العربية وال...
2025-07-25 16:30:30 | INFO     | src.database.database_manager:save_article:139 - Saved article: النشرة أخبار سياسية من لبنان، الشرق الأوسط والعالم...
2025-07-25 16:30:30 | INFO     | src.database.database_manager:save_article:139 - Saved article: الأونروا: موظفو الوكالة والأطباء يغمى عليهم بسبب ا...
2025-07-25 16:30:30 | INFO     | src.database.database_manager:save_article:139 - Saved article: أخبار اليوم | سكاي نيوز عربية...
2025-07-25 16:30:30 | INFO     | src.database.database_manager:save_article:139 - Saved article: وسط غضب عالمي مع اشتداد المجاعة.. بريطانيا ترفع ال...
2025-07-25 16:30:30 | INFO     | src.database.database_manager:save_article:139 - Saved article: أسعار الصرف اليوم في كوبا: اليورو يواصل ارتفاعه وا...
2025-07-25 16:30:30 | INFO     | src.database.database_manager:save_article:139 - Saved article: الليلة .. الأهلي يواجه البنزرتي التونسي فى ختام مب...
2025-07-25 16:30:30 | INFO     | src.news.news_engine:search_latest_news:244 - Found 10 news articles in 41.15s
2025-07-25 17:05:49 | INFO     | src.utils.logger:setup_logging:48 - Logging configured successfully
2025-07-25 17:06:23 | INFO     | src.utils.logger:setup_logging:48 - Logging configured successfully
2025-07-25 17:06:47 | INFO     | src.utils.logger:setup_logging:48 - Logging configured successfully
2025-07-25 17:07:57 | INFO     | src.utils.logger:setup_logging:48 - Logging configured successfully
2025-07-25 17:08:50 | INFO     | src.utils.logger:setup_logging:48 - Logging configured successfully
2025-07-25 17:09:26 | INFO     | src.utils.logger:setup_logging:48 - Logging configured successfully
2025-07-25 17:10:01 | INFO     | src.utils.logger:setup_logging:48 - Logging configured successfully
2025-07-25 17:10:38 | INFO     | src.utils.logger:setup_logging:48 - Logging configured successfully
