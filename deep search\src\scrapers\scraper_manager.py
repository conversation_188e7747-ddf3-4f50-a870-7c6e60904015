"""
Scraper manager to coordinate multiple scrapers
"""

import asyncio
from typing import List, Dict, Any, Optional
from loguru import logger
from .brave_scraper import BraveScraper
from .duckduckgo_scraper import DuckDuckGoScraper
from .enhanced_search_apis import EnhancedSearchManager


class ScraperManager:
    """Manages multiple scrapers and coordinates their execution"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.scrapers = {}
        self.enhanced_search = EnhancedSearchManager(config)
        self._initialize_scrapers()
    
    def _initialize_scrapers(self):
        """Initialize available scrapers"""
        scraping_config = self.config.get('scraping', {})
        
        # Initialize Brave scraper
        try:
            self.scrapers['brave'] = BraveScraper(scraping_config)
            logger.info("Brave scraper initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize Brave scraper: {str(e)}")
        
        # Initialize DuckDuckGo scraper
        try:
            self.scrapers['duckduckgo'] = DuckDuckGoScraper(scraping_config)
            logger.info("DuckDuckGo scraper initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize DuckDuckGo scraper: {str(e)}")
    
    async def search_and_scrape(
        self, 
        query: str, 
        max_results: int = 10,
        engines: Optional[List[str]] = None,
        merge_results: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Search and scrape using multiple engines
        
        Args:
            query: Search query
            max_results: Maximum number of results per engine
            engines: List of engines to use (None for all available)
            merge_results: Whether to merge results from all engines
            
        Returns:
            List of scraped articles
        """
        if engines is None:
            engines = list(self.scrapers.keys())
        
        # Filter available engines
        available_engines = [engine for engine in engines if engine in self.scrapers]
        
        if not available_engines:
            logger.error("No available scrapers for the requested engines")
            return []
        
        logger.info(f"Starting search with engines: {available_engines}")
        
        # Run scrapers concurrently
        tasks = []
        for engine in available_engines:
            scraper = self.scrapers[engine]
            task = self._run_scraper_with_context(scraper, query, max_results)
            tasks.append((engine, task))
        
        # Wait for all scrapers to complete
        results_by_engine = {}
        for engine, task in tasks:
            try:
                results = await task
                results_by_engine[engine] = results
                logger.info(f"{engine} returned {len(results)} results")
            except Exception as e:
                logger.error(f"Scraper {engine} failed: {str(e)}")
                results_by_engine[engine] = []
        
        if merge_results:
            return self._merge_results(results_by_engine, max_results)
        else:
            return results_by_engine
    
    async def _run_scraper_with_context(
        self, 
        scraper, 
        query: str, 
        max_results: int
    ) -> List[Dict[str, Any]]:
        """
        Run scraper within its async context
        
        Args:
            scraper: Scraper instance
            query: Search query
            max_results: Maximum results
            
        Returns:
            List of scraped articles
        """
        async with scraper:
            return await scraper.scrape(query, max_results)
    
    def _merge_results(
        self, 
        results_by_engine: Dict[str, List[Dict[str, Any]]], 
        max_results: int
    ) -> List[Dict[str, Any]]:
        """
        Merge results from multiple engines
        
        Args:
            results_by_engine: Results grouped by engine
            max_results: Maximum number of final results
            
        Returns:
            Merged and deduplicated results
        """
        all_results = []
        seen_urls = set()
        
        # Collect all results
        for engine, results in results_by_engine.items():
            for result in results:
                url = result.get('url', '')
                if url and url not in seen_urls:
                    result['source_engine'] = engine
                    all_results.append(result)
                    seen_urls.add(url)
        
        # Sort by relevance (you can implement more sophisticated scoring)
        all_results.sort(key=lambda x: len(x.get('content', '')), reverse=True)
        
        # Limit results
        final_results = all_results[:max_results]
        
        logger.info(f"Merged results: {len(final_results)} unique articles from {len(results_by_engine)} engines")
        return final_results
    
    async def search_single_engine(
        self, 
        query: str, 
        engine: str, 
        max_results: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Search using a single engine
        
        Args:
            query: Search query
            engine: Engine name
            max_results: Maximum results
            
        Returns:
            List of scraped articles
        """
        if engine not in self.scrapers:
            logger.error(f"Engine {engine} not available")
            return []
        
        scraper = self.scrapers[engine]
        async with scraper:
            results = await scraper.scrape(query, max_results)
            
        # Add source engine to results
        for result in results:
            result['source_engine'] = engine
            
        return results
    
    def get_available_engines(self) -> List[str]:
        """
        Get list of available scraper engines
        
        Returns:
            List of available engine names
        """
        return list(self.scrapers.keys())
    
    async def health_check(self) -> Dict[str, bool]:
        """
        Check health of all scrapers
        
        Returns:
            Dictionary with health status of each scraper
        """
        health_status = {}
        
        for engine_name, scraper in self.scrapers.items():
            try:
                # Simple health check - try to initialize session
                async with scraper:
                    health_status[engine_name] = True
                    logger.info(f"{engine_name} scraper is healthy")
            except Exception as e:
                health_status[engine_name] = False
                logger.error(f"{engine_name} scraper health check failed: {str(e)}")
        
        return health_status

    def configure_brave_api(self, api_key: str) -> Dict[str, Any]:
        """Configure Brave Search API"""
        return self.enhanced_search.configure_brave(api_key)

    def configure_tavily_api(self, api_key: str) -> Dict[str, Any]:
        """Configure Tavily Search API"""
        return self.enhanced_search.configure_tavily(api_key)

    async def search_enhanced_apis(self, query: str, max_results: int = 10) -> Dict[str, Any]:
        """Search using enhanced APIs (Brave + Tavily)"""
        return await self.enhanced_search.search_all_enhanced(query, max_results)

    def get_enhanced_apis_status(self) -> Dict[str, Any]:
        """Get status of enhanced search APIs"""
        return self.enhanced_search.get_status()
