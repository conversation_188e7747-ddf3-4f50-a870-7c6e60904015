"""
Multi-language and dialect support for article generation
"""

import re
import random
from typing import Dict, List, Any, Optional
from loguru import logger


class LanguageManager:
    """Manager for multiple languages and dialects"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.localization_config = config.get('localization', {})
        
        # Supported languages
        self.supported_languages = {
            'ar': 'Arabic',
            'en': 'English', 
            'fr': 'French',
            'es': 'Spanish',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ru': 'Russian',
            'tr': 'Turkish',
            'ur': 'Urdu'
        }
        
        # Arabic dialects
        self.arabic_dialects = {
            'egyptian': 'المصرية',
            'gulf': 'الخليجية',
            'levantine': 'الشامية',
            'maghrebi': 'المغاربية',
            'iraqi': 'العراقية',
            'sudanese': 'السودانية',
            'yemeni': 'اليمنية',
            'standard': 'الفصحى'
        }
        
        # Initialize dialect patterns
        self._initialize_dialect_patterns()
    
    def _initialize_dialect_patterns(self):
        """Initialize dialect conversion patterns"""
        
        # Egyptian dialect patterns (expanded)
        self.egyptian_patterns = {
            'هذا': 'ده', 'هذه': 'دي', 'ذلك': 'ده', 'تلك': 'دي',
            'أريد': 'عايز', 'تريد': 'عايزة', 'يريد': 'عايز', 'نريد': 'عايزين',
            'كيف': 'إزاي', 'ماذا': 'إيه', 'متى': 'إمتى', 'أين': 'فين', 'لماذا': 'ليه',
            'جداً': 'قوي', 'كثيراً': 'كتير', 'قليلاً': 'شوية',
            'الآن': 'دلوقتي', 'اليوم': 'النهاردة', 'أمس': 'إمبارح', 'غداً': 'بكرة',
            'سوف': 'هـ', 'أذهب': 'أروح', 'أعمل': 'أشتغل', 'أقول': 'أقول',
            'جميل': 'حلو', 'جميلة': 'حلوة', 'ضروري': 'لازم', 'ممكن': 'ممكن',
            'بالطبع': 'أكيد', 'نعم': 'أيوة', 'لا': 'لأ', 'فقط': 'بس', 'أيضاً': 'كمان'
        }
        
        # Gulf dialect patterns
        self.gulf_patterns = {
            'هذا': 'هذا', 'هذه': 'هذي', 'ذلك': 'ذاك', 'تلك': 'ذيك',
            'أريد': 'أبي', 'تريد': 'تبين', 'يريد': 'يبي', 'نريد': 'نبي',
            'كيف': 'شلون', 'ماذا': 'شنو', 'متى': 'متى', 'أين': 'وين', 'لماذا': 'ليش',
            'جداً': 'واجد', 'كثيراً': 'هرج', 'قليلاً': 'شوي',
            'الآن': 'الحين', 'اليوم': 'اليوم', 'أمس': 'أمس', 'غداً': 'باجر',
            'جميل': 'زين', 'جميلة': 'زينة', 'ضروري': 'لازم', 'ممكن': 'يمكن',
            'بالطبع': 'أكيد', 'نعم': 'إي', 'لا': 'لا', 'فقط': 'بس', 'أيضاً': 'بعد'
        }
        
        # Levantine dialect patterns
        self.levantine_patterns = {
            'هذا': 'هاد', 'هذه': 'هاي', 'ذلك': 'هاداك', 'تلك': 'هاديك',
            'أريد': 'بدي', 'تريد': 'بدك', 'يريد': 'بده', 'نريد': 'بدنا',
            'كيف': 'كيف', 'ماذا': 'شو', 'متى': 'إيمتى', 'أين': 'وين', 'لماذا': 'ليش',
            'جداً': 'كتير', 'كثيراً': 'كتير', 'قليلاً': 'شوي',
            'الآن': 'هلأ', 'اليوم': 'اليوم', 'أمس': 'مبارح', 'غداً': 'بكرا',
            'جميل': 'حلو', 'جميلة': 'حلوة', 'ضروري': 'لازم', 'ممكن': 'ممكن',
            'بالطبع': 'أكيد', 'نعم': 'أي', 'لا': 'لأ', 'فقط': 'بس', 'أيضاً': 'كمان'
        }
        
        # Maghrebi dialect patterns
        self.maghrebi_patterns = {
            'هذا': 'هادا', 'هذه': 'هادي', 'ذلك': 'داك', 'تلك': 'ديك',
            'أريد': 'بغيت', 'تريد': 'بغيتي', 'يريد': 'بغا', 'نريد': 'بغينا',
            'كيف': 'كيفاش', 'ماذا': 'أش', 'متى': 'فوقاش', 'أين': 'فين', 'لماذا': 'علاش',
            'جداً': 'بزاف', 'كثيراً': 'بزاف', 'قليلاً': 'شوية',
            'الآن': 'دابا', 'اليوم': 'اليوم', 'أمس': 'البارح', 'غداً': 'غدا',
            'جميل': 'زوين', 'جميلة': 'زوينة', 'ضروري': 'خاص', 'ممكن': 'يمكن',
            'بالطبع': 'طبعاً', 'نعم': 'أيه', 'لا': 'لا', 'فقط': 'غير', 'أيضاً': 'حتى'
        }
        
        # Iraqi dialect patterns
        self.iraqi_patterns = {
            'هذا': 'هاذا', 'هذه': 'هاي', 'ذلك': 'هاذاك', 'تلك': 'هايچ',
            'أريد': 'أريد', 'تريد': 'تريد', 'يريد': 'يريد', 'نريد': 'نريد',
            'كيف': 'شلون', 'ماذا': 'شنو', 'متى': 'متى', 'أين': 'وين', 'لماذا': 'ليش',
            'جداً': 'هواية', 'كثيراً': 'هواية', 'قليلاً': 'شوية',
            'الآن': 'الآن', 'اليوم': 'اليوم', 'أمس': 'أمس', 'غداً': 'باچر',
            'جميل': 'حلو', 'جميلة': 'حلوة', 'ضروري': 'لازم', 'ممكن': 'ممكن',
            'بالطبع': 'أكيد', 'نعم': 'إي', 'لا': 'لا', 'فقط': 'بس', 'أيضاً': 'كمان'
        }
    
    def apply_dialect(self, text: str, dialect: str = 'egyptian') -> str:
        """Apply specific Arabic dialect to text"""
        try:
            if dialect not in self.arabic_dialects:
                logger.warning(f"Unsupported dialect: {dialect}, using Egyptian")
                dialect = 'egyptian'
            
            if dialect == 'standard':
                return text  # No changes for standard Arabic
            
            # Get dialect patterns
            patterns = getattr(self, f'{dialect}_patterns', self.egyptian_patterns)
            
            modified_text = text
            
            # Apply dialect replacements
            for formal, dialect_form in patterns.items():
                # Use word boundaries to avoid partial replacements
                pattern = r'\b' + re.escape(formal) + r'\b'
                modified_text = re.sub(pattern, dialect_form, modified_text)
            
            logger.info(f"Applied {dialect} dialect to text")
            return modified_text
            
        except Exception as e:
            logger.error(f"Failed to apply {dialect} dialect: {str(e)}")
            return text
    
    def add_natural_errors(self, text: str, language: str = 'ar', error_rate: float = 0.02) -> str:
        """Add natural spelling errors and variations"""
        try:
            if language == 'ar':
                return self._add_arabic_errors(text, error_rate)
            elif language == 'en':
                return self._add_english_errors(text, error_rate)
            else:
                logger.info(f"Natural errors not implemented for {language}")
                return text
                
        except Exception as e:
            logger.error(f"Failed to add natural errors: {str(e)}")
            return text
    
    def _add_arabic_errors(self, text: str, error_rate: float) -> str:
        """Add natural Arabic spelling errors"""
        try:
            # Common Arabic spelling variations
            error_patterns = {
                'ة': 'ه',  # Ta marbuta confusion
                'ي': 'ى',  # Alif maksura variation
                'أ': 'ا',  # Hamza variations
                'إ': 'ا',
                'ؤ': 'و',
                'ئ': 'ي',
                'الذي': 'اللي',
                'التي': 'اللي',
                'إن شاء الله': 'ان شاء الله',
                'ما شاء الله': 'ماشاء الله',
                'الحمد لله': 'الحمدلله',
                'إلى': 'الى',
                'على': 'علي'
            }
            
            words = text.split()
            total_words = len(words)
            max_errors = max(1, int(total_words * error_rate))
            errors_added = 0
            
            for i, word in enumerate(words):
                if errors_added >= max_errors:
                    break
                
                if random.random() < error_rate and len(word) > 3:
                    # Try to apply an error pattern
                    for correct, error in error_patterns.items():
                        if correct in word and random.random() < 0.3:
                            words[i] = word.replace(correct, error, 1)
                            errors_added += 1
                            break
            
            return ' '.join(words)
            
        except Exception as e:
            logger.error(f"Failed to add Arabic errors: {str(e)}")
            return text
    
    def _add_english_errors(self, text: str, error_rate: float) -> str:
        """Add natural English spelling errors"""
        try:
            # Common English spelling errors
            error_patterns = {
                'receive': 'recieve',
                'definitely': 'definately',
                'separate': 'seperate',
                'occurred': 'occured',
                'beginning': 'begining',
                'necessary': 'neccessary',
                'embarrass': 'embarass',
                'accommodate': 'accomodate'
            }
            
            words = text.split()
            total_words = len(words)
            max_errors = max(1, int(total_words * error_rate))
            errors_added = 0
            
            for i, word in enumerate(words):
                if errors_added >= max_errors:
                    break
                
                clean_word = re.sub(r'[^\w]', '', word.lower())
                if clean_word in error_patterns and random.random() < 0.3:
                    # Preserve original case and punctuation
                    original_case = word
                    error_word = error_patterns[clean_word]
                    
                    # Apply case pattern
                    if original_case.isupper():
                        error_word = error_word.upper()
                    elif original_case.istitle():
                        error_word = error_word.capitalize()
                    
                    # Replace in original word preserving punctuation
                    words[i] = re.sub(re.escape(clean_word), error_word, word, flags=re.IGNORECASE)
                    errors_added += 1
            
            return ' '.join(words)
            
        except Exception as e:
            logger.error(f"Failed to add English errors: {str(e)}")
            return text
    
    def get_language_prompt(self, language: str, dialect: str = None) -> str:
        """Get language-specific prompt instructions"""
        prompts = {
            'ar': 'اكتب باللغة العربية بأسلوب واضح ومفهوم',
            'en': 'Write in clear and understandable English',
            'fr': 'Écrivez en français clair et compréhensible',
            'es': 'Escribe en español claro y comprensible',
            'de': 'Schreiben Sie in klarem und verständlichem Deutsch',
            'it': 'Scrivi in italiano chiaro e comprensibile',
            'pt': 'Escreva em português claro e compreensível',
            'ru': 'Пишите на ясном и понятном русском языке',
            'tr': 'Açık ve anlaşılır Türkçe yazın',
            'ur': 'واضح اور سمجھ میں آنے والی اردو میں لکھیں'
        }
        
        base_prompt = prompts.get(language, prompts['en'])
        
        if language == 'ar' and dialect and dialect != 'standard':
            dialect_name = self.arabic_dialects.get(dialect, dialect)
            base_prompt += f' باللهجة {dialect_name}'
        
        return base_prompt
    
    def get_supported_languages(self) -> Dict[str, str]:
        """Get list of supported languages"""
        return self.supported_languages.copy()
    
    def get_supported_dialects(self) -> Dict[str, str]:
        """Get list of supported Arabic dialects"""
        return self.arabic_dialects.copy()
    
    def validate_language_dialect_combination(self, language: str, dialect: str = None) -> bool:
        """Validate if language and dialect combination is supported"""
        if language not in self.supported_languages:
            return False
        
        if language == 'ar' and dialect:
            return dialect in self.arabic_dialects
        
        return True
    
    def get_status(self) -> Dict[str, Any]:
        """Get language manager status"""
        return {
            'supported_languages': len(self.supported_languages),
            'supported_dialects': len(self.arabic_dialects),
            'languages': list(self.supported_languages.keys()),
            'arabic_dialects': list(self.arabic_dialects.keys())
        }
