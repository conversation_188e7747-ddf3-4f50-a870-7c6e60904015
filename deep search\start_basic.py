#!/usr/bin/env python3
"""
Basic News Agent Server - Minimal functionality for demonstration
"""

import sys
import os
from pathlib import Path

try:
    print("🚀 Starting Basic News Agent")
    print("📰 Minimal news functionality for testing")
    print("=" * 60)
    
    # Basic FastAPI app
    from fastapi import Fast<PERSON><PERSON>, HTTPException
    from fastapi.staticfiles import StaticFiles
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel
    import uvicorn
    from typing import Optional, List
    from datetime import datetime
    import json
    
    # Create app
    app = FastAPI(
        title="Basic News Agent API",
        description="Minimal news functionality for AI agents",
        version="1.0.0"
    )
    
    # Add CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Mount static files
    if os.path.exists("web"):
        app.mount("/web", StaticFiles(directory="web"), name="web")
    
    # Models
    class ProfessionalArticleRequest(BaseModel):
        query: str
        agent_id: Optional[str] = "default"
        max_articles: Optional[int] = 1
        extract_full_content: Optional[bool] = True
    
    class ProfessionalArticle(BaseModel):
        title: str
        url: str
        news_summary: str
        content: str
        full_content: Optional[str] = None
        professional_summary: Optional[str] = None
        key_insights: List[str] = []
        writing_suggestions: Optional[dict] = None
        source_engine: str = "demo"
        scraped_at: str
        news_score: float = 5.0
        selection_score: Optional[float] = 5.0
        extracted_date: Optional[str] = None
        key_entities: List[str] = []
        news_type: str = "regular"
        has_full_content: bool = True
        professional_enhancement: bool = True
        word_count: Optional[int] = None
        estimated_reading_time: Optional[str] = None
        content_quality_score: Optional[float] = 8.5
    
    class ProfessionalArticleResponse(BaseModel):
        query: str
        agent_id: str
        professional_articles: List[ProfessionalArticle]
        total_found: int
        total_new: Optional[int] = None
        total_delivered: int
        processing_time: float
        extraction_enabled: bool
        delivery_time: str
        message: str
        error: Optional[str] = None
    
    # Simple article storage
    delivered_articles = {}
    
    def create_demo_article(query: str, index: int = 1) -> ProfessionalArticle:
        """Create a demo article for testing"""
        
        # Demo content based on query
        if "كرة القدم" in query or "football" in query.lower():
            title = f"برشلونة يحقق فوزاً مهماً في الدوري الإسباني - مقال {index}"
            content = """فاز فريق برشلونة على ضيفه ريال بيتيس بنتيجة 3-1 في المباراة التي أقيمت على ملعب كامب نو ضمن منافسات الجولة العاشرة من الدوري الإسباني. 
            
سجل أهداف برشلونة كل من روبرت ليفاندوفسكي (هدفين) وبيدري، بينما سجل هدف بيتيس الوحيد اللاعب بورخا إغليسياس.

بهذا الفوز، يرتقي برشلونة إلى المركز الثاني في جدول ترتيب الدوري برصيد 27 نقطة، متقدماً بفارق نقطة واحدة عن ريال مدريد الذي يحتل المركز الثالث.

وأشاد المدرب تشافي هيرنانديز بأداء فريقه، مؤكداً أن الفريق يسير في الاتجاه الصحيح نحو تحقيق أهدافه هذا الموسم."""
            
            professional_summary = "برشلونة يحقق فوزاً مهماً 3-1 على ريال بيتيس في كامب نو، ليرتقي للمركز الثاني في الدوري الإسباني برصيد 27 نقطة. ليفاندوفسكي سجل هدفين وبيدري هدف واحد، بينما رد بيتيس بهدف وحيد. المدرب تشافي أشاد بأداء الفريق وأكد السير في الاتجاه الصحيح."
            
            key_insights = [
                "برشلونة يواصل صحوته في الدوري الإسباني",
                "ليفاندوفسكي يؤكد أهميته الهجومية للفريق",
                "الفريق يتقدم على ريال مدريد في الترتيب",
                "تشافي راضٍ عن مستوى الأداء الجماعي"
            ]
            
            entities = ["برشلونة", "ريال بيتيس", "ليفاندوفسكي", "تشافي", "كامب نو"]
            
        elif "بيتكوين" in query or "bitcoin" in query.lower() or "عملة" in query:
            title = f"البيتكوين يرتفع إلى مستويات قياسية جديدة - تحليل {index}"
            content = """شهدت عملة البيتكوين ارتفاعاً قوياً خلال تداولات اليوم، حيث تجاوزت حاجز 45,000 دولار للمرة الأولى منذ شهرين.
            
ويعزو المحللون هذا الارتفاع إلى عدة عوامل، أبرزها الإقبال المتزايد من المستثمرين المؤسسيين وتحسن المعنويات في السوق.

كما ساهمت التصريحات الإيجابية من بعض البنوك المركزية حول العملات الرقمية في دعم هذا الاتجاه الصاعد.

من جانبه، توقع محلل العملات الرقمية جون سميث أن تواصل البيتكوين ارتفاعها لتصل إلى 50,000 دولار خلال الأسابيع القادمة."""
            
            professional_summary = "البيتكوين تحقق اختراقاً مهماً بتجاوز 45,000 دولار لأول مرة منذ شهرين، مدفوعة بإقبال المستثمرين المؤسسيين وتحسن المعنويات. التصريحات الإيجابية من البنوك المركزية عززت الاتجاه الصاعد، والمحللون يتوقعون وصولها لـ50,000 دولار قريباً."
            
            key_insights = [
                "اختراق مستوى 45,000 دولار يعتبر إنجازاً تقنياً مهماً",
                "المستثمرون المؤسسيون يقودون موجة الشراء الحالية",
                "تحسن المعنويات العامة في سوق العملات الرقمية",
                "التوقعات تشير لمواصلة الارتفاع نحو 50,000 دولار"
            ]
            
            entities = ["البيتكوين", "جون سميث", "البنوك المركزية", "المستثمرون المؤسسيون"]
            
        elif "تكنولوجيا" in query or "ذكاء اصطناعي" in query:
            title = f"شركة جوجل تكشف عن تقنيات جديدة في الذكاء الاصطناعي - تقرير {index}"
            content = """أعلنت شركة جوجل عن إطلاق مجموعة جديدة من تقنيات الذكاء الاصطناعي التي تهدف إلى تحسين تجربة المستخدمين عبر منتجاتها المختلفة.
            
وتشمل التحديثات الجديدة تطوير نموذج لغوي متقدم قادر على فهم السياق بشكل أفضل، بالإضافة إلى تحسينات في تقنيات التعرف على الصور والصوت.

وأكد الرئيس التنفيذي للشركة أن هذه التطورات ستساهم في جعل التكنولوجيا أكثر سهولة وفائدة للمستخدمين حول العالم.

من المتوقع أن تبدأ الشركة في طرح هذه التقنيات تدريجياً خلال الأشهر القادمة عبر تطبيقاتها الرئيسية."""
            
            professional_summary = "جوجل تطلق تقنيات ذكاء اصطناعي جديدة تتضمن نموذج لغوي متطور وتحسينات في التعرف على الصور والصوت. الرئيس التنفيذي يؤكد أن التطورات ستجعل التكنولوجيا أكثر سهولة وفائدة، مع بدء الطرح التدريجي خلال الأشهر القادمة."
            
            key_insights = [
                "جوجل تواصل ريادتها في مجال الذكاء الاصطناعي",
                "التركيز على تحسين تجربة المستخدم النهائي",
                "التطوير يشمل معالجة اللغة والصور والصوت",
                "الطرح التدريجي يضمن الاستقرار والجودة"
            ]
            
            entities = ["جوجل", "الذكاء الاصطناعي", "الرئيس التنفيذي", "نموذج لغوي"]
            
        else:
            title = f"تطورات مهمة في الأخبار العالمية - تقرير {index}"
            content = f"""تشهد الساحة الدولية تطورات مهمة في موضوع "{query}" حيث تتابع الأوساط المختصة هذه التطورات باهتمام كبير.
            
وتشير التقارير الأولية إلى أن هناك تقدماً ملحوظاً في هذا الملف، مما يبشر بنتائج إيجابية في المستقبل القريب.

من جانبهم، أكد الخبراء أن هذه التطورات تأتي في إطار الجهود المستمرة لتحقيق التقدم في هذا المجال المهم.

ومن المتوقع أن تتضح الصورة أكثر خلال الأيام القادمة مع توفر المزيد من المعلومات والتفاصيل."""
            
            professional_summary = f"تطورات مهمة في موضوع '{query}' تحظى باهتمام الأوساط المختصة. التقارير تشير لتقدم ملحوظ يبشر بنتائج إيجابية قريباً. الخبراء يؤكدون أن التطورات تأتي ضمن جهود مستمرة، مع توقع وضوح أكبر خلال الأيام القادمة."
            
            key_insights = [
                "الموضوع يحظى باهتمام واسع من الخبراء",
                "هناك تقدم ملحوظ في الملف",
                "التطورات تبشر بنتائج إيجابية",
                "المزيد من التفاصيل متوقع قريباً"
            ]
            
            entities = ["الخبراء", "الأوساط المختصة", "التقارير الأولية"]
        
        # Create article
        article = ProfessionalArticle(
            title=title,
            url=f"https://demo-news.com/article-{index}",
            news_summary=professional_summary[:200] + "...",
            content=content,
            full_content=content + "\n\n[هذا مقال تجريبي للاختبار - المحتوى الكامل متاح]",
            professional_summary=professional_summary,
            key_insights=key_insights,
            writing_suggestions={
                "suggested_angles": [
                    "التركيز على التأثير المستقبلي",
                    "تحليل ردود الأفعال",
                    "مقارنة مع أحداث مشابهة"
                ],
                "key_quotes": [f"تصريح مهم حول {query}"],
                "writing_tips": [
                    "ابدأ بالحدث الرئيسي",
                    "استخدم الأرقام والإحصائيات",
                    "اختتم بالتوقعات المستقبلية"
                ]
            },
            scraped_at=datetime.now().isoformat(),
            extracted_date="اليوم",
            key_entities=entities,
            word_count=len(content.split()),
            estimated_reading_time="دقيقتان"
        )
        
        return article
    
    # API Endpoints
    @app.get("/")
    async def root():
        return {
            "message": "Basic News Agent API - Demo Mode",
            "version": "1.0.0",
            "note": "This is a demo version with sample articles",
            "endpoints": {
                "professional": "/news/professional",
                "single": "/news/professional/single",
                "web": "/web/professional.html"
            }
        }
    
    @app.post("/news/professional", response_model=ProfessionalArticleResponse)
    async def get_professional_articles(request: ProfessionalArticleRequest):
        """Get professional articles (demo version)"""
        try:
            # Check if already delivered
            key = f"{request.agent_id}:{request.query}"
            if key in delivered_articles:
                return ProfessionalArticleResponse(
                    query=request.query,
                    agent_id=request.agent_id,
                    professional_articles=[],
                    total_found=1,
                    total_new=0,
                    total_delivered=0,
                    processing_time=0.1,
                    extraction_enabled=request.extract_full_content,
                    delivery_time=datetime.now().isoformat(),
                    message="تم تقديم هذا المقال مسبقاً (منع التكرار)"
                )
            
            # Create demo articles
            articles = []
            for i in range(request.max_articles):
                article = create_demo_article(request.query, i + 1)
                articles.append(article)
            
            # Mark as delivered
            delivered_articles[key] = datetime.now().isoformat()
            
            return ProfessionalArticleResponse(
                query=request.query,
                agent_id=request.agent_id,
                professional_articles=articles,
                total_found=request.max_articles,
                total_new=request.max_articles,
                total_delivered=request.max_articles,
                processing_time=1.5,
                extraction_enabled=request.extract_full_content,
                delivery_time=datetime.now().isoformat(),
                message=f"تم تقديم {len(articles)} مقال تجريبي احترافي"
            )
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/news/professional/single")
    async def get_single_professional_article(
        query: str,
        agent_id: str = "default",
        extract_full_content: bool = True
    ):
        """Get single professional article"""
        request = ProfessionalArticleRequest(
            query=query,
            agent_id=agent_id,
            max_articles=1,
            extract_full_content=extract_full_content
        )
        return await get_professional_articles(request)
    
    @app.get("/news/professional/stats/{agent_id}")
    async def get_agent_delivery_stats(agent_id: str):
        """Get agent statistics"""
        agent_articles = [k for k in delivered_articles.keys() if k.startswith(f"{agent_id}:")]
        
        return {
            "agent_id": agent_id,
            "total_articles_delivered": len(agent_articles),
            "articles_last_24h": len(agent_articles),
            "categories_covered": ["demo"],
            "last_delivery": max(delivered_articles.values()) if delivered_articles else None
        }
    
    @app.delete("/news/professional/history/{agent_id}")
    async def clear_agent_history(agent_id: str):
        """Clear agent history"""
        keys_to_remove = [k for k in delivered_articles.keys() if k.startswith(f"{agent_id}:")]
        for key in keys_to_remove:
            del delivered_articles[key]
        
        return {
            "message": f"تم مسح تاريخ التسليم للوكيل {agent_id}",
            "agent_id": agent_id,
            "cleared_at": datetime.now().isoformat()
        }
    
    # Start server
    print("✅ Basic server configured with demo articles")
    print(f"\n🌐 Server starting on http://localhost:8001")
    print(f"📰 Professional Interface: http://localhost:8001/web/professional.html")
    print(f"📚 API Docs: http://localhost:8001/docs")
    print()
    print("🤖 Demo API Endpoints:")
    print("  • POST /news/professional - Get demo professional articles")
    print("  • GET /news/professional/single - Get single demo article")
    print("  • GET /news/professional/stats/{agent_id} - Agent statistics")
    print()
    print("💡 Example usage:")
    print("  curl 'http://localhost:8001/news/professional/single?query=آخر أخبار كرة القدم&agent_id=sports_bot'")
    print()
    print("⚠️ Note: This is a DEMO version with sample articles for testing")
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
    
except KeyboardInterrupt:
    print("\n⏹️ Server stopped by user")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
