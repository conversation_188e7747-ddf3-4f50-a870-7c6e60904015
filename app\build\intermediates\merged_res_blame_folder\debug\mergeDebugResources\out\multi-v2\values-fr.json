{"logs": [{"outputFile": "com.modetaris.gamingnews.app-mergeDebugResources-42:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\69c40aec708a7733e482ec325600ccb6\\transformed\\play-services-basement-18.2.0\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5626", "endColumns": "164", "endOffsets": "5786"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d64a33053620fe7b1a00c329a8dfc346\\transformed\\material-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1109,1175,1272,1355,1421,1523,1588,1663,1719,1798,1858,1912,2034,2093,2155,2209,2291,2426,2518,2593,2688,2769,2853,2997,3076,3157,3298,3391,3470,3525,3576,3642,3722,3803,3874,3954,4027,4105,4178,4250,4362,4455,4527,4619,4711,4785,4869,4961,5018,5102,5168,5251,5338,5400,5464,5527,5605,5707,5811,5908,6012,6071,6126,6215,6302,6379", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "278,358,439,522,631,726,824,954,1039,1104,1170,1267,1350,1416,1518,1583,1658,1714,1793,1853,1907,2029,2088,2150,2204,2286,2421,2513,2588,2683,2764,2848,2992,3071,3152,3293,3386,3465,3520,3571,3637,3717,3798,3869,3949,4022,4100,4173,4245,4357,4450,4522,4614,4706,4780,4864,4956,5013,5097,5163,5246,5333,5395,5459,5522,5600,5702,5806,5903,6007,6066,6121,6210,6297,6374,6455"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,146,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3155,3236,3319,3428,4250,4348,4478,7072,7137,7203,7626,7709,7775,7877,7942,8017,8073,8152,8212,8266,8388,8447,8509,8563,8645,8780,8872,8947,9042,9123,9207,9351,9430,9511,9652,9745,9824,9879,9930,9996,10076,10157,10228,10308,10381,10459,10532,10604,10716,10809,10881,10973,11065,11139,11223,11315,11372,11456,11522,11605,11692,11754,11818,11881,11959,12061,12165,12262,12366,12425,13651,14246,14333,14410", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,146,155,156,157", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "328,3150,3231,3314,3423,3518,4343,4473,4558,7132,7198,7295,7704,7770,7872,7937,8012,8068,8147,8207,8261,8383,8442,8504,8558,8640,8775,8867,8942,9037,9118,9202,9346,9425,9506,9647,9740,9819,9874,9925,9991,10071,10152,10223,10303,10376,10454,10527,10599,10711,10804,10876,10968,11060,11134,11218,11310,11367,11451,11517,11600,11687,11749,11813,11876,11954,12056,12160,12257,12361,12420,12475,13735,14328,14405,14486"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b28f7da774e38c14e96bef101d6db4b2\\transformed\\core-1.16.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3523,3621,3723,3822,3924,4028,4132,14491", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3616,3718,3817,3919,4023,4127,4245,14587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6268fa03c7ebf543ba887f8bf449bb01\\transformed\\play-services-ads-22.6.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,287,344,411,485,594,675,815,865,1015,1073,1205,1318,1366,1461,1497,1532,1588,1669,1709", "endColumns": "41,45,56,66,73,108,80,139,49,149,57,131,112,47,94,35,34,55,80,39,55", "endOffsets": "240,286,343,410,484,593,674,814,864,1014,1072,1204,1317,1365,1460,1496,1531,1587,1668,1708,1764"}, "to": {"startLines": "133,134,135,136,137,138,139,140,141,142,143,144,145,147,148,149,150,151,152,153,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12480,12526,12576,12637,12708,12786,12899,12984,13128,13182,13336,13398,13534,13740,13792,13891,13931,13970,14030,14115,14592", "endColumns": "45,49,60,70,77,112,84,143,53,153,61,135,116,51,98,39,38,59,84,43,59", "endOffsets": "12521,12571,12632,12703,12781,12894,12979,13123,13177,13331,13393,13529,13646,13787,13886,13926,13965,14025,14110,14154,14647"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3c8ddd354c9ab96e361fff9bc529194f\\transformed\\browser-1.4.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "66,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6965,7300,7402,7521", "endColumns": "106,101,118,104", "endOffsets": "7067,7397,7516,7621"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c7be0c72e4429312e422d4fb96d3bf68\\transformed\\play-services-base-18.0.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4563,4669,4849,4979,5088,5259,5392,5513,5791,5969,6081,6266,6402,6562,6741,6814,6881", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "4664,4844,4974,5083,5254,5387,5508,5621,5964,6076,6261,6397,6557,6736,6809,6876,6960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e165e972ac01707ea55a93b91866c0a0\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,14159", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,14241"}}]}]}