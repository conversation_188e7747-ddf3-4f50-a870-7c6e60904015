// DeepSearch Agent Web Interface

const API_BASE_URL = 'http://localhost:8000';

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Add enter key support for search
    document.getElementById('searchQuery').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    // Check API health on load
    checkHealth();
});

// Perform search
async function performSearch() {
    const query = document.getElementById('searchQuery').value.trim();
    if (!query) {
        showError('يرجى إدخال استعلام البحث');
        return;
    }
    
    const maxResults = parseInt(document.getElementById('maxResults').value);
    const searchType = document.getElementById('searchType').value;
    const summarize = document.getElementById('summarize').checked;
    
    showLoading();
    hideError();
    hideResults();
    
    try {
        let result;
        
        if (searchType === 'rag') {
            result = await performRAGSearch(query, maxResults);
            showRAGResults(result);
        } else {
            result = await performRegularSearch(query, maxResults, summarize);
            showSearchResults(result);
        }
        
    } catch (error) {
        console.error('Search error:', error);
        showError('حدث خطأ أثناء البحث: ' + error.message);
    } finally {
        hideLoading();
    }
}

// Regular search
async function performRegularSearch(query, maxResults, summarize) {
    const response = await fetch(`${API_BASE_URL}/search`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            query: query,
            max_results: maxResults,
            summarize: summarize,
            use_cache: true,
            enhance_query: true
        })
    });
    
    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
}

// RAG search
async function performRAGSearch(query, maxResults) {
    const response = await fetch(`${API_BASE_URL}/rag`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            query: query,
            max_context_articles: maxResults,
            use_recent_articles: true
        })
    });
    
    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
}

// Show search results
function showSearchResults(result) {
    showResults();
    
    // Show articles
    const articlesContainer = document.getElementById('articlesContainer');
    articlesContainer.innerHTML = '';
    
    if (result.articles && result.articles.length > 0) {
        result.articles.forEach((article, index) => {
            const articleCard = createArticleCard(article, index + 1);
            articlesContainer.appendChild(articleCard);
        });
    } else {
        articlesContainer.innerHTML = '<div class="alert alert-info">لم يتم العثور على نتائج</div>';
    }
    
    // Show stats
    const statsContainer = document.getElementById('statsContainer');
    statsContainer.innerHTML = createStatsHTML(result);
    
    // Hide RAG tab
    document.getElementById('rag-tab').style.display = 'none';
}

// Show RAG results
function showRAGResults(result) {
    showResults();
    
    // Show RAG answer
    const ragContainer = document.getElementById('ragContainer');
    ragContainer.innerHTML = createRAGHTML(result);
    
    // Show RAG tab and activate it
    document.getElementById('rag-tab').style.display = 'block';
    document.getElementById('rag-tab').click();
    
    // Hide articles tab content
    document.getElementById('articlesContainer').innerHTML = 
        '<div class="alert alert-info">استخدم تبويب "إجابة RAG" لرؤية النتيجة</div>';
    
    // Show basic stats
    const statsContainer = document.getElementById('statsContainer');
    statsContainer.innerHTML = createRAGStatsHTML(result);
}

// Create article card
function createArticleCard(article, index) {
    const card = document.createElement('div');
    card.className = 'result-card';
    
    const sentiment = article.sentiment || {};
    const sentimentClass = getSentimentClass(sentiment.sentiment);
    const sentimentIcon = getSentimentIcon(sentiment.sentiment);
    
    card.innerHTML = `
        <div class="d-flex justify-content-between align-items-start mb-2">
            <h5 class="card-title mb-0">
                <span class="badge bg-primary me-2">${index}</span>
                ${article.title || 'بدون عنوان'}
            </h5>
            <div>
                ${article.source_engine ? `<span class="provider-badge">${article.source_engine}</span>` : ''}
                ${sentiment.sentiment ? `<span class="${sentimentClass}"><i class="${sentimentIcon}"></i></span>` : ''}
            </div>
        </div>
        
        <p class="text-muted mb-2">
            <i class="fas fa-link"></i>
            <a href="${article.url}" target="_blank" class="text-decoration-none">
                ${article.url}
            </a>
        </p>
        
        ${article.summary ? `
            <div class="mb-3">
                <h6><i class="fas fa-file-alt"></i> الملخص:</h6>
                <p>${article.summary}</p>
            </div>
        ` : ''}
        
        ${article.ai_keywords && article.ai_keywords.length > 0 ? `
            <div class="mb-2">
                <h6><i class="fas fa-tags"></i> الكلمات المفتاحية:</h6>
                ${article.ai_keywords.slice(0, 5).map(keyword => 
                    `<span class="badge bg-secondary me-1">${keyword}</span>`
                ).join('')}
            </div>
        ` : ''}
        
        ${article.similarity_score ? `
            <div class="progress mb-2" style="height: 5px;">
                <div class="progress-bar" role="progressbar" 
                     style="width: ${(article.similarity_score * 100).toFixed(1)}%">
                </div>
            </div>
            <small class="text-muted">درجة التشابه: ${(article.similarity_score * 100).toFixed(1)}%</small>
        ` : ''}
    `;
    
    return card;
}

// Create stats HTML
function createStatsHTML(result) {
    return `
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card">
                    <h3>${result.total_results}</h3>
                    <p>إجمالي النتائج</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h3>${result.processing_time ? result.processing_time.toFixed(2) : 0}s</h3>
                    <p>وقت المعالجة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h3>${result.engines_used ? result.engines_used.length : 0}</h3>
                    <p>محركات البحث</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h3>${result.cached ? 'نعم' : 'لا'}</h3>
                    <p>من التخزين المؤقت</p>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h5>تفاصيل البحث:</h5>
            <ul class="list-group">
                <li class="list-group-item d-flex justify-content-between">
                    <span>الاستعلام الأصلي:</span>
                    <strong>${result.query}</strong>
                </li>
                ${result.enhanced_query && result.enhanced_query !== result.query ? `
                    <li class="list-group-item d-flex justify-content-between">
                        <span>الاستعلام المحسن:</span>
                        <strong>${result.enhanced_query}</strong>
                    </li>
                ` : ''}
                <li class="list-group-item d-flex justify-content-between">
                    <span>محركات البحث المستخدمة:</span>
                    <strong>${result.engines_used ? result.engines_used.join(', ') : 'غير محدد'}</strong>
                </li>
            </ul>
        </div>
    `;
}

// Create RAG HTML
function createRAGHTML(result) {
    return `
        <div class="result-card">
            <h4><i class="fas fa-robot"></i> إجابة الذكاء الاصطناعي</h4>
            <div class="alert alert-primary">
                <h5>السؤال: ${result.query}</h5>
            </div>
            
            <div class="mb-4">
                <h6>الإجابة:</h6>
                <p class="lead">${result.answer}</p>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: ${(result.confidence * 100).toFixed(1)}%">
                            ${(result.confidence * 100).toFixed(1)}%
                        </div>
                    </div>
                    <small class="text-muted">درجة الثقة</small>
                </div>
                <div class="col-md-6">
                    <p class="mb-0"><strong>المقالات المستخدمة:</strong> ${result.context_used || 0}</p>
                    <p class="mb-0"><strong>إجمالي المقالات:</strong> ${result.total_context_articles || 0}</p>
                </div>
            </div>
            
            ${result.sources && result.sources.length > 0 ? `
                <h6>المصادر:</h6>
                <div class="list-group">
                    ${result.sources.map((source, index) => `
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">${index + 1}. ${source.title}</h6>
                                    <p class="mb-1">
                                        <a href="${source.url}" target="_blank" class="text-decoration-none">
                                            ${source.url}
                                        </a>
                                    </p>
                                </div>
                                <span class="badge bg-primary rounded-pill">
                                    ${(source.similarity_score * 100).toFixed(1)}%
                                </span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            ` : ''}
        </div>
    `;
}

// Create RAG stats HTML
function createRAGStatsHTML(result) {
    return `
        <div class="row">
            <div class="col-md-4">
                <div class="stats-card">
                    <h3>${(result.confidence * 100).toFixed(1)}%</h3>
                    <p>درجة الثقة</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <h3>${result.processing_time ? result.processing_time.toFixed(2) : 0}s</h3>
                    <p>وقت المعالجة</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <h3>${result.sources ? result.sources.length : 0}</h3>
                    <p>عدد المصادر</p>
                </div>
            </div>
        </div>
    `;
}

// Check API health
async function checkHealth() {
    try {
        const response = await fetch(`${API_BASE_URL}/health`);
        const health = await response.json();
        
        console.log('Health check:', health);
        
        // You can add visual indicators here
        if (health.overall_status === 'healthy') {
            console.log('✅ API is healthy');
        } else {
            console.log('⚠️ API has issues');
        }
        
    } catch (error) {
        console.error('❌ API is not accessible:', error);
        showError('لا يمكن الوصول إلى API. تأكد من تشغيل الخادم على المنفذ 8000');
    }
}

// Utility functions
function showLoading() {
    document.getElementById('loading').style.display = 'block';
}

function hideLoading() {
    document.getElementById('loading').style.display = 'none';
}

function showResults() {
    document.getElementById('results').style.display = 'block';
}

function hideResults() {
    document.getElementById('results').style.display = 'none';
}

function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('error').style.display = 'block';
}

function hideError() {
    document.getElementById('error').style.display = 'none';
}

function getSentimentClass(sentiment) {
    if (!sentiment) return '';
    
    if (sentiment.includes('إيجابية') || sentiment.includes('positive')) {
        return 'sentiment-positive';
    } else if (sentiment.includes('سلبية') || sentiment.includes('negative')) {
        return 'sentiment-negative';
    } else {
        return 'sentiment-neutral';
    }
}

function getSentimentIcon(sentiment) {
    if (!sentiment) return '';
    
    if (sentiment.includes('إيجابية') || sentiment.includes('positive')) {
        return 'fas fa-smile';
    } else if (sentiment.includes('سلبية') || sentiment.includes('negative')) {
        return 'fas fa-frown';
    } else {
        return 'fas fa-meh';
    }
}
