<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSearch News Agent - محرك الأخبار الذكي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }
        
        .news-badge {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .category-buttons {
            margin-bottom: 20px;
        }
        
        .btn-category {
            margin: 5px;
            border-radius: 20px;
            padding: 8px 16px;
            border: 2px solid #dc3545;
            background: white;
            color: #dc3545;
            transition: all 0.3s ease;
        }
        
        .btn-category:hover, .btn-category.active {
            background: #dc3545;
            color: white;
        }
        
        .news-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid #dc3545;
        }
        
        .news-meta {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .news-score {
            background: #dc3545;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
        }
        
        .breaking-news {
            border-left-color: #ff0000;
            background: linear-gradient(45deg, #fff5f5, #ffffff);
        }
        
        .breaking-badge {
            background: #ff0000;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .entity-tag {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 11px;
            margin: 2px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="text-center mb-4">
                <div class="news-badge">
                    <i class="fas fa-newspaper"></i> محرك الأخبار الذكي للوكلاء البرمجيين
                </div>
                <h1 class="display-5 text-danger">
                    <i class="fas fa-rss"></i>
                    DeepSearch News Agent
                </h1>
                <p class="lead text-muted">آخر الأخبار مع ملخصات ذكية ومصادر موثوقة</p>
            </div>

            <!-- Quick Category Buttons -->
            <div class="category-buttons text-center">
                <button class="btn btn-category" onclick="searchByCategory('football')">
                    <i class="fas fa-futbol"></i> كرة القدم
                </button>
                <button class="btn btn-category" onclick="searchByCategory('crypto')">
                    <i class="fab fa-bitcoin"></i> العملات الرقمية
                </button>
                <button class="btn btn-category" onclick="searchByCategory('technology')">
                    <i class="fas fa-microchip"></i> التكنولوجيا
                </button>
                <button class="btn btn-category" onclick="searchByCategory('war')">
                    <i class="fas fa-globe"></i> الحروب والصراعات
                </button>
                <button class="btn btn-category" onclick="searchTrending()">
                    <i class="fas fa-fire"></i> الأخبار الرائجة
                </button>
            </div>

            <!-- Search Box -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <input type="text" id="newsQuery" class="form-control" 
                                   placeholder="ابحث عن آخر الأخبار... (مثال: آخر أخبار كرة القدم اليوم)">
                        </div>
                        <div class="col-md-4">
                            <button onclick="searchNews()" class="btn btn-danger w-100">
                                <i class="fas fa-search"></i> بحث الأخبار
                            </button>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <select id="maxResults" class="form-select">
                                <option value="5">5 أخبار</option>
                                <option value="10" selected>10 أخبار</option>
                                <option value="20">20 خبر</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select id="hoursBack" class="form-select">
                                <option value="6">آخر 6 ساعات</option>
                                <option value="12">آخر 12 ساعة</option>
                                <option value="24" selected>آخر 24 ساعة</option>
                                <option value="48">آخر 48 ساعة</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select id="category" class="form-select">
                                <option value="">جميع الفئات</option>
                                <option value="sports">الرياضة</option>
                                <option value="technology">التكنولوجيا</option>
                                <option value="crypto">العملات الرقمية</option>
                                <option value="politics">السياسة</option>
                                <option value="economy">الاقتصاد</option>
                                <option value="health">الصحة</option>
                                <option value="war">الحروب</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading -->
            <div id="loading" class="text-center" style="display: none;">
                <div class="spinner-border text-danger" role="status"></div>
                <p class="mt-2">جاري البحث عن آخر الأخبار...</p>
            </div>

            <!-- Results -->
            <div id="results" style="display: none;">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-newspaper"></i> آخر الأخبار</h5>
                    <div id="searchInfo" class="text-muted"></div>
                </div>
                <div id="newsContainer"></div>
                
                <!-- Stats -->
                <div id="statsContainer" class="mt-4"></div>
            </div>

            <!-- Error -->
            <div id="error" class="alert alert-danger" style="display: none;">
                <h6><i class="fas fa-exclamation-triangle"></i> خطأ</h6>
                <p id="errorMessage"></p>
            </div>

            <!-- API Usage Guide -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6><i class="fas fa-code"></i> دليل استخدام API للوكلاء البرمجيين</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>نقاط النهاية المتخصصة:</h6>
                            <ul class="list-unstyled">
                                <li><code>POST /news/search</code> - بحث عام في الأخبار</li>
                                <li><code>POST /news/football</code> - أخبار كرة القدم</li>
                                <li><code>POST /news/crypto</code> - أخبار العملات الرقمية</li>
                                <li><code>POST /news/technology</code> - أخبار التكنولوجيا</li>
                                <li><code>POST /news/war</code> - أخبار الحروب</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>مثال على الاستخدام:</h6>
                            <pre class="bg-light p-2 rounded"><code>curl -X POST "http://localhost:8000/news/football" \
  -H "Content-Type: application/json" \
  -d '{"max_results": 10}'</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="news.js"></script>
</body>
</html>
