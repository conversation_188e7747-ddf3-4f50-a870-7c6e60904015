# DeepSearch Agent - Optimized Configuration for Low Token Usage
# This configuration is optimized to minimize API token consumption

# Database Settings
database:
  url: "sqlite:///data/deepsearch.db"
  vector_db_path: "data/vector_db"
  backup_enabled: false  # Disabled to save resources
  backup_interval: 3600

# Search Settings
search:
  max_results: 5  # Reduced from 20 to save processing
  default_language: "ar"
  timeout: 20  # Reduced timeout
  engines: ["duckduckgo"]  # Only free engine

# Web Scraping Settings
scraping:
  user_agent: "DeepSearch-Agent/1.0"
  request_delay: 2  # Increased delay to be respectful
  concurrent_requests: 2  # Reduced concurrency
  download_timeout: 20  # Reduced timeout
  retry_attempts: 2  # Reduced retries
  respect_robots_txt: true
  max_content_length: 3000  # Reduced content length

# AI Processing Settings - OPTIMIZED FOR LOW USAGE
ai:
  provider: "mistral"  # Using Mistral as primary
  model: "mistral-large-latest"
  fallback_provider: "gemini"  # Gemini as fallback
  fallback_model: "gemini-2.0-flash-exp"
  
  # Content limits to save tokens
  max_content_length: 2000  # Heavily reduced
  summary_max_length: 150   # Very short summaries
  
  # Model parameters
  temperature: 0.3  # Lower for more focused responses
  max_tokens: 500   # Very limited tokens
  
  # Optimization settings
  auto_switch: true
  rate_limit_enabled: true
  requests_per_minute: 5    # Very conservative rate limit
  batch_processing: true
  skip_ai_on_short_content: true
  min_content_length: 300   # Higher threshold
  
  # Feature toggles to save tokens
  enable_summarization: true
  enable_keyword_extraction: false  # DISABLED
  enable_sentiment_analysis: false  # DISABLED
  enable_query_enhancement: false   # DISABLED
  
  # Embedding settings
  embedding_model: "sentence-transformers/all-MiniLM-L6-v2"  # Smaller model

# API Settings
api:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  debug: false  # Disabled for production
  cors_enabled: true
  rate_limit: 50  # Lower API rate limit

# Logging Settings
logging:
  level: "WARNING"  # Reduced logging
  file: "logs/deepsearch.log"
  max_size: "5MB"   # Smaller log files
  backup_count: 3   # Fewer backups
  format: "%(asctime)s - %(levelname)s - %(message)s"

# Cache Settings
cache:
  enabled: true
  ttl: 7200  # Longer cache time
  max_size: 500  # Smaller cache
