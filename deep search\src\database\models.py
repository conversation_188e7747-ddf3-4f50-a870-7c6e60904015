"""
Database models for DeepSearch
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Float, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime

Base = declarative_base()


class SearchQuery(Base):
    """Model for storing search queries"""
    __tablename__ = 'search_queries'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    query = Column(String(500), nullable=False)
    enhanced_query = Column(String(500))
    language = Column(String(10), default='ar')
    timestamp = Column(DateTime, default=func.now())
    results_count = Column(Integer, default=0)
    engines_used = Column(JSON)  # List of engines used
    processing_time = Column(Float)  # Time taken in seconds
    
    def __repr__(self):
        return f"<SearchQuery(id={self.id}, query='{self.query[:50]}...')>"


class Article(Base):
    """Model for storing scraped articles"""
    __tablename__ = 'articles'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    url = Column(String(1000), unique=True, nullable=False)
    title = Column(String(500))
    content = Column(Text)
    summary = Column(Text)
    description = Column(Text)
    author = Column(String(200))
    language = Column(String(10), default='ar')
    keywords = Column(JSON)  # List of keywords
    ai_keywords = Column(JSON)  # AI-extracted keywords
    
    # Metadata
    source_engine = Column(String(50))  # brave, duckduckgo, etc.
    scraped_at = Column(DateTime, default=func.now())
    is_summarized = Column(Boolean, default=False)
    
    # Content analysis
    sentiment = Column(JSON)  # Sentiment analysis results
    content_length = Column(Integer)
    
    # Search relevance
    similarity_score = Column(Float)  # For semantic search results
    
    def __repr__(self):
        return f"<Article(id={self.id}, title='{self.title[:50]}...')>"


class Embedding(Base):
    """Model for storing text embeddings"""
    __tablename__ = 'embeddings'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    article_id = Column(Integer, nullable=False)  # Reference to Article.id
    embedding_vector = Column(JSON, nullable=False)  # Embedding as JSON array
    embedding_model = Column(String(200))  # Model used for embedding
    text_hash = Column(String(64))  # Hash of the text for deduplication
    created_at = Column(DateTime, default=func.now())
    
    def __repr__(self):
        return f"<Embedding(id={self.id}, article_id={self.article_id})>"


class SearchResult(Base):
    """Model for storing search results and their associations"""
    __tablename__ = 'search_results'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    query_id = Column(Integer, nullable=False)  # Reference to SearchQuery.id
    article_id = Column(Integer, nullable=False)  # Reference to Article.id
    rank = Column(Integer)  # Position in search results
    relevance_score = Column(Float)  # Relevance score
    created_at = Column(DateTime, default=func.now())
    
    def __repr__(self):
        return f"<SearchResult(query_id={self.query_id}, article_id={self.article_id}, rank={self.rank})>"


class Cache(Base):
    """Model for caching search results and other data"""
    __tablename__ = 'cache'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    cache_key = Column(String(200), unique=True, nullable=False)
    cache_value = Column(JSON, nullable=False)
    expires_at = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    
    def __repr__(self):
        return f"<Cache(key='{self.cache_key}')>"


class SystemLog(Base):
    """Model for storing system logs and events"""
    __tablename__ = 'system_logs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    level = Column(String(20), nullable=False)  # INFO, WARNING, ERROR, etc.
    message = Column(Text, nullable=False)
    component = Column(String(100))  # scraper, ai, api, etc.
    details = Column(JSON)  # Additional details as JSON
    timestamp = Column(DateTime, default=func.now())
    
    def __repr__(self):
        return f"<SystemLog(level={self.level}, component={self.component})>"


class UserSession(Base):
    """Model for tracking user sessions and API usage"""
    __tablename__ = 'user_sessions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(100), unique=True, nullable=False)
    user_agent = Column(String(500))
    ip_address = Column(String(45))  # IPv6 compatible
    requests_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=func.now())
    last_activity = Column(DateTime, default=func.now())
    
    def __repr__(self):
        return f"<UserSession(session_id='{self.session_id}')>"
