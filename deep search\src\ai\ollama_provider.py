"""
Ollama AI provider implementation
"""

import os
import aiohttp
import asyncio
from typing import List, Dict, Any, Optional
from loguru import logger
from .base_ai_provider import BaseAIProvider


class OllamaProvider(BaseAIProvider):
    """AI provider using Ollama for local AI models"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.base_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _ensure_session(self):
        """Ensure session is available"""
        if not self.session:
            self.session = aiohttp.ClientSession()
    
    async def generate_text(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Generate text using Ollama
        
        Args:
            prompt: User prompt
            system_prompt: System prompt (optional)
            **kwargs: Additional parameters
            
        Returns:
            Generated text
        """
        await self._ensure_session()
        
        url = f"{self.base_url}/api/generate"
        
        payload = {
            "model": self.model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": kwargs.get('temperature', self.temperature),
                "num_predict": kwargs.get('max_tokens', self.max_tokens)
            }
        }
        
        if system_prompt:
            payload["system"] = system_prompt
        
        try:
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    generated_text = data.get('response', '').strip()
                    logger.info(f"Generated text with Ollama model: {self.model}")
                    return generated_text
                else:
                    error_text = await response.text()
                    logger.error(f"Ollama API error {response.status}: {error_text}")
                    return ""
                    
        except Exception as e:
            logger.error(f"Ollama generation failed: {str(e)}")
            return ""
    
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings using Ollama
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        await self._ensure_session()
        
        url = f"{self.base_url}/api/embeddings"
        embeddings = []
        
        for text in texts:
            payload = {
                "model": self.model,
                "prompt": text
            }
            
            try:
                async with self.session.post(url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        embedding = data.get('embedding', [])
                        embeddings.append(embedding)
                    else:
                        logger.error(f"Embedding failed for text: {text[:50]}...")
                        embeddings.append([])
                        
            except Exception as e:
                logger.error(f"Embedding generation failed: {str(e)}")
                embeddings.append([])
        
        logger.info(f"Generated {len(embeddings)} embeddings")
        return embeddings
    
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> str:
        """
        Chat completion using Ollama
        
        Args:
            messages: List of chat messages
            **kwargs: Additional parameters
            
        Returns:
            Chat response
        """
        await self._ensure_session()
        
        url = f"{self.base_url}/api/chat"
        
        payload = {
            "model": self.model,
            "messages": messages,
            "stream": False,
            "options": {
                "temperature": kwargs.get('temperature', self.temperature),
                "num_predict": kwargs.get('max_tokens', self.max_tokens)
            }
        }
        
        try:
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    message = data.get('message', {})
                    content = message.get('content', '').strip()
                    logger.info(f"Chat completion with Ollama model: {self.model}")
                    return content
                else:
                    error_text = await response.text()
                    logger.error(f"Ollama chat API error {response.status}: {error_text}")
                    return ""
                    
        except Exception as e:
            logger.error(f"Ollama chat completion failed: {str(e)}")
            return ""
    
    async def health_check(self) -> bool:
        """
        Check if Ollama service is available
        
        Returns:
            True if service is healthy
        """
        await self._ensure_session()
        
        try:
            url = f"{self.base_url}/api/tags"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    models = data.get('models', [])
                    logger.info(f"Ollama is healthy. Available models: {len(models)}")
                    return True
                else:
                    logger.error(f"Ollama health check failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"Ollama health check failed: {str(e)}")
            return False
    
    async def list_models(self) -> List[str]:
        """
        List available models in Ollama
        
        Returns:
            List of model names
        """
        await self._ensure_session()
        
        try:
            url = f"{self.base_url}/api/tags"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    models = data.get('models', [])
                    model_names = [model.get('name', '') for model in models]
                    logger.info(f"Available Ollama models: {model_names}")
                    return model_names
                else:
                    logger.error(f"Failed to list models: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Failed to list models: {str(e)}")
            return []
