#!/usr/bin/env python3
"""
Quick start script for DeepSearch Agent with AI provider switching
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config_loader import load_config
from src.utils.logger import setup_logging
from src.core.search_engine import DeepSearchEngine
from loguru import logger


async def test_ai_providers():
    """Test different AI providers"""
    print("🧠 Testing AI Providers")
    print("=" * 50)
    
    config = load_config()
    setup_logging(config)
    
    # Test different providers
    providers_to_test = ['gemini', 'mistral', 'ollama']
    
    for provider in providers_to_test:
        print(f"\n🔄 Testing {provider.upper()} provider...")
        
        # Update config for this provider
        test_config = config.copy()
        test_config['ai']['provider'] = provider
        
        try:
            async with DeepSearchEngine(test_config) as engine:
                # Test basic functionality
                if engine.ai_manager and engine.ai_manager.provider:
                    # Test query enhancement
                    enhanced = await engine.ai_manager.enhance_search_query("AI news")
                    print(f"  ✅ Query enhancement: 'AI news' → '{enhanced}'")
                    
                    # Test health check
                    if hasattr(engine.ai_manager.provider, 'health_check'):
                        health = await engine.ai_manager.provider.health_check()
                        print(f"  ✅ Health check: {'Healthy' if health else 'Unhealthy'}")
                    
                    print(f"  ✅ {provider.upper()} provider is working!")
                else:
                    print(f"  ❌ {provider.upper()} provider failed to initialize")
                    
        except Exception as e:
            print(f"  ❌ {provider.upper()} provider error: {str(e)}")


async def test_search_with_switching():
    """Test search with AI provider switching"""
    print("\n🔍 Testing Search with AI Provider Switching")
    print("=" * 50)
    
    config = load_config()
    
    # Configure with Gemini primary and Ollama fallback
    config['ai']['provider'] = 'gemini'
    config['ai']['fallback_provider'] = 'ollama'
    config['ai']['auto_switch'] = True
    
    try:
        async with DeepSearchEngine(config) as engine:
            # Test search
            result = await engine.search(
                query="Python programming tutorials",
                max_results=3,
                engines=["duckduckgo"]
            )
            
            print(f"Search Results:")
            print(f"  📊 Total results: {result['total_results']}")
            print(f"  ⏱️ Processing time: {result['processing_time']:.2f}s")
            print(f"  🔧 Engines used: {', '.join(result['engines_used'])}")
            print(f"  🧠 Query enhanced: {result['enhanced_query'] != result['query']}")
            
            if result['articles']:
                print(f"\n📰 First article:")
                article = result['articles'][0]
                print(f"  Title: {article['title'][:100]}...")
                print(f"  URL: {article['url']}")
                print(f"  Summarized: {article.get('is_summarized', False)}")
                
                if article.get('ai_keywords'):
                    keywords = ', '.join(article['ai_keywords'][:5])
                    print(f"  Keywords: {keywords}")
            
            # Test RAG search
            print(f"\n🤖 Testing RAG search...")
            rag_result = await engine.rag_search("What is Python programming?")
            
            print(f"RAG Results:")
            print(f"  🎯 Confidence: {rag_result['confidence']:.2f}")
            print(f"  📚 Sources used: {len(rag_result.get('sources', []))}")
            print(f"  💬 Answer: {rag_result['answer'][:200]}...")
            
    except Exception as e:
        print(f"❌ Search test failed: {str(e)}")


async def test_web_interface():
    """Instructions for testing web interface"""
    print("\n🌐 Web Interface Testing")
    print("=" * 50)
    
    print("To test the web interface:")
    print("1. Start the API server:")
    print("   python main.py --api")
    print()
    print("2. Open your browser and visit:")
    print("   🔍 Main Interface: http://localhost:8000/web/index.html")
    print("   ⚙️ Admin Panel: http://localhost:8000/web/admin.html")
    print()
    print("3. Test features:")
    print("   • Search for anything (e.g., 'latest AI news')")
    print("   • Try RAG search with questions")
    print("   • Switch AI providers in admin panel")
    print("   • Monitor system health")


async def main():
    """Main test function"""
    print("🚀 DeepSearch Agent - Quick Start & Testing")
    print("=" * 60)
    
    # Check environment
    print("🔧 Environment Check:")
    
    # Check API keys
    api_keys = {
        'GEMINI_API_KEY': os.getenv('GEMINI_API_KEY'),
        'MISTRAL_API_KEY': os.getenv('MISTRAL_API_KEY'),
        'BRAVE_API_KEY': os.getenv('BRAVE_API_KEY')
    }
    
    for key, value in api_keys.items():
        status = "✅ Configured" if value else "❌ Missing"
        print(f"  {key}: {status}")
    
    if not any(api_keys.values()):
        print("\n⚠️ Warning: No API keys configured!")
        print("Please set up API keys in .env file for full functionality.")
        print("You can still test with DuckDuckGo search and local features.")
    
    print()
    
    # Run tests
    try:
        await test_ai_providers()
        await test_search_with_switching()
        await test_web_interface()
        
        print("\n✅ Quick start completed successfully!")
        print("\n📋 Next steps:")
        print("1. Configure API keys in .env file")
        print("2. Start the API server: python main.py --api")
        print("3. Open web interface: http://localhost:8000/web/index.html")
        print("4. Try different search queries and AI providers")
        
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Testing failed: {str(e)}")
        logger.error(f"Quick start error: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
