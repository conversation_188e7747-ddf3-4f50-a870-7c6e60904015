"""
Article Tracker - Prevents duplicate articles and tracks delivered content
"""

import hashlib
import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Set
from loguru import logger


class ArticleTracker:
    """Tracks delivered articles to prevent duplicates"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.tracker_file = "data/delivered_articles.json"
        self.max_history_days = config.get('news', {}).get('max_history_days', 7)
        self.delivered_articles: Dict[str, Dict] = {}
        self._load_tracker()
    
    def _load_tracker(self):
        """Load delivered articles from file"""
        try:
            if os.path.exists(self.tracker_file):
                with open(self.tracker_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.delivered_articles = data.get('delivered_articles', {})
                    logger.info(f"Loaded {len(self.delivered_articles)} tracked articles")
            else:
                self.delivered_articles = {}
                logger.info("No previous article tracking found")
        except Exception as e:
            logger.error(f"Failed to load article tracker: {str(e)}")
            self.delivered_articles = {}
    
    def _save_tracker(self):
        """Save delivered articles to file"""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.tracker_file), exist_ok=True)
            
            # Clean old entries before saving
            self._cleanup_old_entries()
            
            data = {
                'delivered_articles': self.delivered_articles,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.tracker_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.debug(f"Saved {len(self.delivered_articles)} tracked articles")
        except Exception as e:
            logger.error(f"Failed to save article tracker: {str(e)}")
    
    def _cleanup_old_entries(self):
        """Remove old entries beyond max_history_days"""
        cutoff_date = datetime.now() - timedelta(days=self.max_history_days)
        
        old_articles = []
        for article_hash, info in self.delivered_articles.items():
            try:
                delivered_date = datetime.fromisoformat(info['delivered_at'])
                if delivered_date < cutoff_date:
                    old_articles.append(article_hash)
            except Exception:
                # Remove invalid entries
                old_articles.append(article_hash)
        
        for article_hash in old_articles:
            del self.delivered_articles[article_hash]
        
        if old_articles:
            logger.info(f"Cleaned up {len(old_articles)} old article entries")
    
    def _generate_article_hash(self, article: Dict[str, Any]) -> str:
        """Generate unique hash for article"""
        # Use URL as primary identifier
        url = article.get('url', '')
        if url:
            return hashlib.md5(url.encode('utf-8')).hexdigest()
        
        # Fallback to title + content hash
        title = article.get('title', '')
        content = article.get('content', '')[:500]  # First 500 chars
        combined = f"{title}|{content}"
        return hashlib.md5(combined.encode('utf-8')).hexdigest()
    
    def is_article_delivered(self, article: Dict[str, Any]) -> bool:
        """Check if article was already delivered"""
        article_hash = self._generate_article_hash(article)
        return article_hash in self.delivered_articles
    
    def mark_article_delivered(self, article: Dict[str, Any], agent_id: str = "default"):
        """Mark article as delivered"""
        article_hash = self._generate_article_hash(article)
        
        self.delivered_articles[article_hash] = {
            'title': article.get('title', ''),
            'url': article.get('url', ''),
            'delivered_at': datetime.now().isoformat(),
            'agent_id': agent_id,
            'category': article.get('category', 'general')
        }
        
        self._save_tracker()
        logger.debug(f"Marked article as delivered: {article.get('title', 'Unknown')[:50]}...")
    
    def mark_articles_delivered(self, articles: List[Dict[str, Any]], agent_id: str = "default"):
        """Mark multiple articles as delivered"""
        for article in articles:
            article_hash = self._generate_article_hash(article)
            self.delivered_articles[article_hash] = {
                'title': article.get('title', ''),
                'url': article.get('url', ''),
                'delivered_at': datetime.now().isoformat(),
                'agent_id': agent_id,
                'category': article.get('category', 'general')
            }
        
        self._save_tracker()
        logger.info(f"Marked {len(articles)} articles as delivered for agent {agent_id}")
    
    def filter_new_articles(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter out already delivered articles"""
        new_articles = []
        
        for article in articles:
            if not self.is_article_delivered(article):
                new_articles.append(article)
            else:
                logger.debug(f"Skipping already delivered article: {article.get('title', 'Unknown')[:50]}...")
        
        logger.info(f"Filtered {len(articles)} articles -> {len(new_articles)} new articles")
        return new_articles
    
    def get_delivery_stats(self) -> Dict[str, Any]:
        """Get statistics about delivered articles"""
        total_delivered = len(self.delivered_articles)
        
        # Count by category
        categories = {}
        agents = {}
        recent_count = 0
        
        recent_cutoff = datetime.now() - timedelta(hours=24)
        
        for info in self.delivered_articles.values():
            # Count by category
            category = info.get('category', 'general')
            categories[category] = categories.get(category, 0) + 1
            
            # Count by agent
            agent_id = info.get('agent_id', 'default')
            agents[agent_id] = agents.get(agent_id, 0) + 1
            
            # Count recent deliveries
            try:
                delivered_date = datetime.fromisoformat(info['delivered_at'])
                if delivered_date > recent_cutoff:
                    recent_count += 1
            except Exception:
                pass
        
        return {
            'total_delivered': total_delivered,
            'recent_24h': recent_count,
            'by_category': categories,
            'by_agent': agents,
            'oldest_entry': min([info.get('delivered_at', '') for info in self.delivered_articles.values()]) if self.delivered_articles else None,
            'newest_entry': max([info.get('delivered_at', '') for info in self.delivered_articles.values()]) if self.delivered_articles else None
        }
    
    def clear_history(self, agent_id: str = None):
        """Clear delivery history (optionally for specific agent)"""
        if agent_id:
            # Clear only for specific agent
            to_remove = []
            for article_hash, info in self.delivered_articles.items():
                if info.get('agent_id') == agent_id:
                    to_remove.append(article_hash)
            
            for article_hash in to_remove:
                del self.delivered_articles[article_hash]
            
            logger.info(f"Cleared {len(to_remove)} articles for agent {agent_id}")
        else:
            # Clear all
            count = len(self.delivered_articles)
            self.delivered_articles = {}
            logger.info(f"Cleared all {count} tracked articles")
        
        self._save_tracker()
    
    def get_similar_articles(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Get previously delivered articles similar to query"""
        query_lower = query.lower()
        similar_articles = []
        
        for info in self.delivered_articles.values():
            title = info.get('title', '').lower()
            if any(word in title for word in query_lower.split()):
                similar_articles.append(info)
        
        # Sort by delivery date (newest first)
        similar_articles.sort(
            key=lambda x: x.get('delivered_at', ''), 
            reverse=True
        )
        
        return similar_articles[:limit]
