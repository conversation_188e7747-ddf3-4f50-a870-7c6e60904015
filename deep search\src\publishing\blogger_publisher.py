"""
Blogger API Publisher for automatic article publishing
"""

import json
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
from loguru import logger
import httpx
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError


class BloggerPublisher:
    """Publisher for Blogger API with service account authentication"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.publishing_config = config.get('publishing', {})
        self.blogger_config = self.publishing_config.get('blogger', {})
        
        # Authentication
        self.service_account_file = None
        self.credentials = None
        self.service = None
        self.blog_id = None
        self.is_configured = False
        
        # Publishing settings
        self.auto_publish = self.blogger_config.get('auto_publish', False)
        self.default_labels = self.blogger_config.get('default_labels', [])
        
    def configure_service_account(self, service_account_json: str, blog_id: str) -> Dict[str, Any]:
        """
        Configure Blogger API with service account credentials
        
        Args:
            service_account_json: Service account JSON content
            blog_id: Blogger blog ID
            
        Returns:
            Configuration result
        """
        try:
            # Parse service account JSON
            try:
                service_account_info = json.loads(service_account_json)
            except json.JSONDecodeError as e:
                return {
                    "success": False,
                    "error": f"Invalid JSON format: {str(e)}"
                }
            
            # Validate required fields
            required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email']
            missing_fields = [field for field in required_fields if field not in service_account_info]
            
            if missing_fields:
                return {
                    "success": False,
                    "error": f"Missing required fields: {', '.join(missing_fields)}"
                }
            
            # Create credentials
            scopes = ['https://www.googleapis.com/auth/blogger']
            self.credentials = service_account.Credentials.from_service_account_info(
                service_account_info, scopes=scopes
            )
            
            # Build service
            self.service = build('blogger', 'v3', credentials=self.credentials)
            self.blog_id = blog_id
            
            # Test the configuration
            test_result = self._test_blogger_access()
            if not test_result['success']:
                return test_result
            
            self.is_configured = True
            logger.info(f"Blogger API configured successfully for blog: {blog_id}")
            
            return {
                "success": True,
                "message": "Blogger API configured successfully",
                "blog_id": blog_id,
                "service_account_email": service_account_info.get('client_email')
            }
            
        except Exception as e:
            logger.error(f"Failed to configure Blogger API: {str(e)}")
            return {
                "success": False,
                "error": f"Configuration failed: {str(e)}"
            }
    
    def _test_blogger_access(self) -> Dict[str, Any]:
        """Test access to Blogger API"""
        try:
            # Try to get blog information
            blog = self.service.blogs().get(blogId=self.blog_id).execute()
            
            return {
                "success": True,
                "blog_name": blog.get('name'),
                "blog_url": blog.get('url'),
                "posts_count": blog.get('posts', {}).get('totalItems', 0)
            }
            
        except HttpError as e:
            error_details = e.error_details[0] if e.error_details else {}
            error_message = error_details.get('message', str(e))
            
            return {
                "success": False,
                "error": f"Blogger API access failed: {error_message}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}"
            }
    
    def is_ready(self) -> bool:
        """Check if publisher is ready for use"""
        return self.is_configured and self.service is not None and self.blog_id is not None
    
    async def publish_article(
        self,
        title: str,
        content: str,
        labels: List[str] = None,
        is_draft: bool = False,
        meta_description: str = None,
        seo_keywords: List[str] = None
    ) -> Dict[str, Any]:
        """
        Publish article to Blogger
        
        Args:
            title: Article title
            content: Article content (HTML)
            labels: Article labels/tags
            is_draft: Whether to publish as draft
            meta_description: Meta description for SEO
            seo_keywords: SEO keywords
            
        Returns:
            Publishing result
        """
        if not self.is_ready():
            return {
                "success": False,
                "error": "Blogger publisher is not configured"
            }
        
        try:
            logger.info(f"Publishing article to Blogger: {title}")
            
            # Prepare labels
            article_labels = labels or []
            article_labels.extend(self.default_labels)
            
            # Add SEO keywords as labels
            if seo_keywords:
                article_labels.extend(seo_keywords[:5])  # Limit to 5 keywords
            
            # Remove duplicates and empty labels
            article_labels = list(set([label.strip() for label in article_labels if label.strip()]))
            
            # Prepare HTML content
            html_content = self._prepare_html_content(content, meta_description, seo_keywords)
            
            # Prepare post data
            post_data = {
                'title': title,
                'content': html_content,
                'labels': article_labels
            }
            
            # Publish or save as draft
            if is_draft:
                result = self.service.posts().insert(
                    blogId=self.blog_id,
                    body=post_data,
                    isDraft=True
                ).execute()
            else:
                result = self.service.posts().insert(
                    blogId=self.blog_id,
                    body=post_data
                ).execute()
            
            logger.info(f"Article published successfully: {result.get('url')}")
            
            return {
                "success": True,
                "post_id": result.get('id'),
                "post_url": result.get('url'),
                "published_date": result.get('published'),
                "status": "draft" if is_draft else "published",
                "labels": article_labels
            }
            
        except HttpError as e:
            error_details = e.error_details[0] if e.error_details else {}
            error_message = error_details.get('message', str(e))
            
            logger.error(f"Failed to publish article: {error_message}")
            return {
                "success": False,
                "error": f"Publishing failed: {error_message}"
            }
        except Exception as e:
            logger.error(f"Unexpected error during publishing: {str(e)}")
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}"
            }
    
    def _prepare_html_content(
        self, 
        content: str, 
        meta_description: str = None,
        seo_keywords: List[str] = None
    ) -> str:
        """Prepare HTML content with SEO optimization"""
        try:
            # Convert markdown-style content to HTML if needed
            html_content = content
            
            # Add basic HTML structure if not present
            if not html_content.startswith('<'):
                # Convert line breaks to paragraphs
                paragraphs = [p.strip() for p in html_content.split('\n\n') if p.strip()]
                html_paragraphs = [f'<p>{p.replace(chr(10), '<br>')}</p>' for p in paragraphs]
                html_content = '\n'.join(html_paragraphs)
            
            # Add SEO meta tags in HTML comments (for reference)
            if meta_description:
                html_content = f'<!-- Meta Description: {meta_description} -->\n{html_content}'
            
            if seo_keywords:
                keywords_str = ', '.join(seo_keywords)
                html_content = f'<!-- Keywords: {keywords_str} -->\n{html_content}'
            
            return html_content
            
        except Exception as e:
            logger.error(f"Failed to prepare HTML content: {str(e)}")
            return content
    
    def generate_html_copy(
        self,
        title: str,
        content: str,
        meta_description: str = None,
        seo_keywords: List[str] = None
    ) -> str:
        """
        Generate HTML copy for manual pasting into Blogger
        
        Args:
            title: Article title
            content: Article content
            meta_description: Meta description
            seo_keywords: SEO keywords
            
        Returns:
            Complete HTML ready for copying
        """
        try:
            # Prepare HTML content
            html_content = self._prepare_html_content(content, meta_description, seo_keywords)
            
            # Create complete HTML structure
            html_template = f"""<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    {f'<meta name="description" content="{meta_description}">' if meta_description else ''}
    {f'<meta name="keywords" content="{", ".join(seo_keywords)}">' if seo_keywords else ''}
</head>
<body>
    <article>
        <h1>{title}</h1>
        {html_content}
    </article>
</body>
</html>"""
            
            return html_template
            
        except Exception as e:
            logger.error(f"Failed to generate HTML copy: {str(e)}")
            return f"<h1>{title}</h1>\n{content}"
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the publisher"""
        status = {
            "configured": self.is_configured,
            "ready": self.is_ready(),
            "blog_id": self.blog_id,
            "auto_publish": self.auto_publish,
            "default_labels": self.default_labels
        }
        
        if self.is_ready():
            # Get blog info
            try:
                blog_info = self._test_blogger_access()
                if blog_info['success']:
                    status.update({
                        "blog_name": blog_info.get('blog_name'),
                        "blog_url": blog_info.get('blog_url'),
                        "posts_count": blog_info.get('posts_count')
                    })
            except Exception:
                pass
        
        return status
