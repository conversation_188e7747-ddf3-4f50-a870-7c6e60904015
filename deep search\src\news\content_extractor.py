"""
Content Extractor - Extracts full article content for professional analysis
"""

import asyncio
import aiohttp
from datetime import datetime
from typing import Dict, Any, Optional
from loguru import logger
from bs4 import BeautifulSoup
import re


class ContentExtractor:
    """Extracts full content from news articles"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.scraping_config = config.get('scraping', {})
        self.user_agent = self.scraping_config.get('user_agent', 'DeepSearch-NewsAgent/2.0')
        self.timeout = self.scraping_config.get('download_timeout', 30)
        self.max_content_length = config.get('news', {}).get('max_full_content_length', 50000)
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout),
            headers={'User-Agent': self.user_agent}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def extract_full_content(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """Extract full content from article URL"""
        url = article.get('url', '')
        if not url:
            logger.warning("No URL provided for content extraction")
            return article
        
        try:
            logger.info(f"Extracting full content from: {url}")
            
            # Download the page
            async with self.session.get(url) as response:
                if response.status != 200:
                    logger.warning(f"Failed to fetch article: HTTP {response.status}")
                    return article
                
                html_content = await response.text()
            
            # Parse and extract content
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract full article content
            full_content = self._extract_article_content(soup)
            
            # Extract additional metadata
            metadata = self._extract_enhanced_metadata(soup, url)
            
            # Update article with full content
            enhanced_article = article.copy()
            enhanced_article.update({
                'full_content': full_content,
                'content_length': len(full_content),
                'extraction_time': datetime.now().isoformat(),
                'has_full_content': True,
                **metadata
            })
            
            logger.info(f"Extracted {len(full_content)} characters of content")
            return enhanced_article
            
        except Exception as e:
            logger.error(f"Failed to extract content from {url}: {str(e)}")
            return article
    
    def _extract_article_content(self, soup: BeautifulSoup) -> str:
        """Extract main article content from HTML"""
        # Remove unwanted elements
        for element in soup(['script', 'style', 'nav', 'header', 'footer', 
                           'aside', 'advertisement', 'ads', 'sidebar']):
            element.decompose()
        
        # Try different content selectors (common article containers)
        content_selectors = [
            'article',
            '.article-content',
            '.post-content',
            '.entry-content',
            '.content',
            '.story-body',
            '.article-body',
            '.post-body',
            'main',
            '.main-content'
        ]
        
        content_text = ""
        
        for selector in content_selectors:
            elements = soup.select(selector)
            if elements:
                # Get the largest content block
                largest_element = max(elements, key=lambda x: len(x.get_text()))
                content_text = self._clean_text(largest_element.get_text())
                if len(content_text) > 500:  # Minimum content length
                    break
        
        # Fallback: extract from paragraphs
        if len(content_text) < 500:
            paragraphs = soup.find_all('p')
            content_text = '\n\n'.join([self._clean_text(p.get_text()) for p in paragraphs])
        
        # Limit content length
        if len(content_text) > self.max_content_length:
            content_text = content_text[:self.max_content_length] + "..."
        
        return content_text
    
    def _extract_enhanced_metadata(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """Extract enhanced metadata from article"""
        metadata = {}
        
        # Extract publication date
        date_selectors = [
            'time[datetime]',
            '.publish-date',
            '.publication-date',
            '.date',
            '[data-date]'
        ]
        
        for selector in date_selectors:
            element = soup.select_one(selector)
            if element:
                date_text = element.get('datetime') or element.get('data-date') or element.get_text()
                metadata['publication_date'] = self._parse_date(date_text)
                break
        
        # Extract author
        author_selectors = [
            '.author',
            '.byline',
            '.writer',
            '[rel="author"]',
            '.post-author'
        ]
        
        for selector in author_selectors:
            element = soup.select_one(selector)
            if element:
                metadata['author'] = self._clean_text(element.get_text())
                break
        
        # Extract tags/categories
        tag_selectors = [
            '.tags a',
            '.categories a',
            '.post-tags a',
            '.article-tags a'
        ]
        
        tags = []
        for selector in tag_selectors:
            elements = soup.select(selector)
            if elements:
                tags.extend([self._clean_text(tag.get_text()) for tag in elements])
                break
        
        if tags:
            metadata['article_tags'] = tags[:10]  # Limit to 10 tags
        
        # Extract reading time estimate
        content_length = len(soup.get_text())
        words_count = len(soup.get_text().split())
        reading_time = max(1, words_count // 200)  # Assume 200 words per minute
        
        metadata.update({
            'word_count': words_count,
            'estimated_reading_time': f"{reading_time} دقيقة",
            'content_quality_score': self._calculate_content_quality(soup)
        })
        
        return metadata
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        if not text:
            return ""
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep Arabic and basic punctuation
        text = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF.,!?;:()\-"]', '', text)
        
        return text.strip()
    
    def _parse_date(self, date_text: str) -> Optional[str]:
        """Parse date from various formats"""
        if not date_text:
            return None
        
        try:
            # Try ISO format first
            if 'T' in date_text:
                return datetime.fromisoformat(date_text.replace('Z', '+00:00')).isoformat()
            
            # Try other common formats
            try:
                import dateutil.parser
                parsed_date = dateutil.parser.parse(date_text)
                return parsed_date.isoformat()
            except ImportError:
                # Fallback if dateutil not available
                return date_text
            
        except Exception:
            return date_text  # Return as-is if parsing fails
    
    def _calculate_content_quality(self, soup: BeautifulSoup) -> float:
        """Calculate content quality score (0-10)"""
        score = 5.0  # Base score
        
        text = soup.get_text()
        word_count = len(text.split())
        
        # Word count factor
        if word_count > 500:
            score += 1.0
        if word_count > 1000:
            score += 1.0
        if word_count > 2000:
            score += 1.0
        
        # Structure factors
        if soup.find_all('h1', 'h2', 'h3'):  # Has headings
            score += 0.5
        
        if soup.find_all('p'):  # Has paragraphs
            score += 0.5
        
        if soup.find_all('img'):  # Has images
            score += 0.5
        
        # Reduce score for poor indicators
        if word_count < 200:
            score -= 2.0
        
        if len(soup.find_all('a')) > word_count // 10:  # Too many links
            score -= 1.0
        
        return max(0.0, min(10.0, score))
    
    async def extract_multiple_contents(self, articles: list) -> list:
        """Extract full content for multiple articles"""
        enhanced_articles = []
        
        for article in articles:
            try:
                enhanced_article = await self.extract_full_content(article)
                enhanced_articles.append(enhanced_article)
                
                # Add delay between requests to be respectful
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Failed to extract content for article: {str(e)}")
                enhanced_articles.append(article)  # Add original article
        
        return enhanced_articles
