1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.modetaris.gamingnews"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <!-- الصلاحيات المطلوبة -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.WAKE_LOCK" />
15-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:9:5-68
15-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:9:22-65
16
17    <!-- صلاحيات AdMob -->
18    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
18-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:12:5-79
18-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:12:22-76
19    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
19-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:27:5-82
19-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:27:22-79
20    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
20-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:28:5-88
20-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:28:22-85
21    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
21-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:29:5-83
21-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:29:22-80
22    <queries>
22-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:35:5-51:15
23
24        <!-- For browser content -->
25        <intent>
25-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:38:9-44:18
26            <action android:name="android.intent.action.VIEW" />
26-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:39:13-65
26-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:39:21-62
27
28            <category android:name="android.intent.category.BROWSABLE" />
28-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:41:13-74
28-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:41:23-71
29
30            <data android:scheme="https" />
30-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
30-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:43:19-41
31        </intent>
32        <!-- End of browser content -->
33        <!-- For CustomTabsService -->
34        <intent>
34-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:47:9-49:18
35            <action android:name="android.support.customtabs.action.CustomTabsService" />
35-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:48:13-90
35-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:48:21-87
36        </intent>
37        <!-- End of CustomTabsService -->
38    </queries>
39
40    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
40-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
40-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
41
42    <permission
42-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b28f7da774e38c14e96bef101d6db4b2\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
43        android:name="com.modetaris.gamingnews.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
43-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b28f7da774e38c14e96bef101d6db4b2\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
44        android:protectionLevel="signature" />
44-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b28f7da774e38c14e96bef101d6db4b2\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
45
46    <uses-permission android:name="com.modetaris.gamingnews.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
46-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b28f7da774e38c14e96bef101d6db4b2\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
46-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b28f7da774e38c14e96bef101d6db4b2\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
47
48    <application
48-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:14:5-53:19
49        android:allowBackup="true"
49-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:15:9-35
50        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
50-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b28f7da774e38c14e96bef101d6db4b2\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
51        android:dataExtractionRules="@xml/data_extraction_rules"
51-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:16:9-65
52        android:debuggable="true"
53        android:extractNativeLibs="true"
54        android:fullBackupContent="@xml/backup_rules"
54-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:17:9-54
55        android:icon="@mipmap/ic_launcher"
55-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:18:9-43
56        android:label="@string/app_name"
56-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:19:9-41
57        android:networkSecurityConfig="@xml/network_security_config"
57-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:24:9-69
58        android:roundIcon="@mipmap/ic_launcher_round"
58-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:20:9-54
59        android:supportsRtl="true"
59-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:21:9-35
60        android:theme="@style/Theme.Modetaris"
60-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:22:9-47
61        android:usesCleartextTraffic="true" >
61-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:23:9-44
62
63        <!-- معرف تطبيق AdMob الحقيقي -->
64        <meta-data
64-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:28:9-30:70
65            android:name="com.google.android.gms.ads.APPLICATION_ID"
65-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:29:13-69
66            android:value="ca-app-pub-4259271882937197~2882200625" />
66-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:30:13-67
67
68        <!-- النشاط الرئيسي بواجهة الويب -->
69        <activity
69-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:33:9-43:20
70            android:name="com.modetaris.gamingnews.MainActivity"
70-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:34:13-65
71            android:exported="true"
71-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:35:13-36
72            android:launchMode="singleTop"
72-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:38:13-43
73            android:screenOrientation="portrait"
73-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:36:13-49
74            android:theme="@style/Theme.Modetaris.NoActionBar" >
74-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:37:13-63
75            <intent-filter>
75-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:39:13-42:29
76                <action android:name="android.intent.action.MAIN" />
76-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:40:17-69
76-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:40:25-66
77
78                <category android:name="android.intent.category.LAUNCHER" />
78-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:41:17-77
78-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:41:27-74
79            </intent-filter>
80        </activity>
81
82        <!-- شاشة البداية -->
83        <activity
83-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:46:9-51:20
84            android:name="com.modetaris.gamingnews.SplashActivity"
84-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:47:13-67
85            android:exported="false"
85-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:48:13-37
86            android:screenOrientation="portrait"
86-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:50:13-49
87            android:theme="@style/Theme.Modetaris.Splash" >
87-->C:\Users\<USER>\deabsearch app\app\src\main\AndroidManifest.xml:49:13-58
88        </activity>
89
90        <provider
90-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c9b924042be905678c3ae3c25620591\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
91            android:name="androidx.startup.InitializationProvider"
91-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c9b924042be905678c3ae3c25620591\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
92            android:authorities="com.modetaris.gamingnews.androidx-startup"
92-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c9b924042be905678c3ae3c25620591\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
93            android:exported="false" >
93-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c9b924042be905678c3ae3c25620591\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
94            <meta-data
94-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c9b924042be905678c3ae3c25620591\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
95                android:name="androidx.emoji2.text.EmojiCompatInitializer"
95-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c9b924042be905678c3ae3c25620591\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
96                android:value="androidx.startup" />
96-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c9b924042be905678c3ae3c25620591\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
97            <meta-data
97-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
98                android:name="androidx.work.WorkManagerInitializer"
98-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
99                android:value="androidx.startup" />
99-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
100            <meta-data
100-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1d62220d4857f5d15385f3eaa8b2c68\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
101                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
101-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1d62220d4857f5d15385f3eaa8b2c68\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
102                android:value="androidx.startup" />
102-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1d62220d4857f5d15385f3eaa8b2c68\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
103            <meta-data
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
104                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
105                android:value="androidx.startup" />
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
106        </provider> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
107        <activity
107-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:56:9-61:43
108            android:name="com.google.android.gms.ads.AdActivity"
108-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:57:13-65
109            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
109-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:58:13-122
110            android:exported="false"
110-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:59:13-37
111            android:theme="@android:style/Theme.Translucent" />
111-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:60:13-61
112
113        <provider
113-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:63:9-68:43
114            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
114-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:64:13-76
115            android:authorities="com.modetaris.gamingnews.mobileadsinitprovider"
115-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:65:13-73
116            android:exported="false"
116-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:66:13-37
117            android:initOrder="100" />
117-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:67:13-36
118
119        <service
119-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:70:9-74:43
120            android:name="com.google.android.gms.ads.AdService"
120-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:71:13-64
121            android:enabled="true"
121-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:72:13-35
122            android:exported="false" />
122-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:73:13-37
123
124        <activity
124-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:76:9-80:43
125            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
125-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:77:13-82
126            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
126-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:78:13-122
127            android:exported="false" />
127-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:79:13-37
128        <activity
128-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:81:9-88:43
129            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
129-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:82:13-82
130            android:excludeFromRecents="true"
130-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:83:13-46
131            android:exported="false"
131-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:84:13-37
132            android:launchMode="singleTask"
132-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:85:13-44
133            android:taskAffinity=""
133-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:86:13-36
134            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
134-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:87:13-72
135
136        <property
136-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:90:9-92:62
137            android:name="android.adservices.AD_SERVICES_CONFIG"
137-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:91:13-65
138            android:resource="@xml/gma_ad_services_config" />
138-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:92:13-59
139
140        <activity
140-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7be0c72e4429312e422d4fb96d3bf68\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
141            android:name="com.google.android.gms.common.api.GoogleApiActivity"
141-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7be0c72e4429312e422d4fb96d3bf68\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:19-85
142            android:exported="false"
142-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7be0c72e4429312e422d4fb96d3bf68\transformed\play-services-base-18.0.0\AndroidManifest.xml:22:19-43
143            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
143-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7be0c72e4429312e422d4fb96d3bf68\transformed\play-services-base-18.0.0\AndroidManifest.xml:21:19-78
144
145        <meta-data
145-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69c40aec708a7733e482ec325600ccb6\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
146            android:name="com.google.android.gms.version"
146-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69c40aec708a7733e482ec325600ccb6\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
147            android:value="@integer/google_play_services_version" />
147-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69c40aec708a7733e482ec325600ccb6\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
148
149        <service
149-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
150            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
150-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
151            android:directBootAware="false"
151-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
152            android:enabled="@bool/enable_system_alarm_service_default"
152-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
153            android:exported="false" />
153-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
154        <service
154-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
155            android:name="androidx.work.impl.background.systemjob.SystemJobService"
155-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
156            android:directBootAware="false"
156-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
157            android:enabled="@bool/enable_system_job_service_default"
157-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
158            android:exported="true"
158-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
159            android:permission="android.permission.BIND_JOB_SERVICE" />
159-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
160        <service
160-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
161            android:name="androidx.work.impl.foreground.SystemForegroundService"
161-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
162            android:directBootAware="false"
162-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
163            android:enabled="@bool/enable_system_foreground_service_default"
163-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
164            android:exported="false" />
164-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
165
166        <receiver
166-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
167            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
167-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
168            android:directBootAware="false"
168-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
169            android:enabled="true"
169-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
170            android:exported="false" />
170-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
171        <receiver
171-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
172            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
172-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
173            android:directBootAware="false"
173-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
174            android:enabled="false"
174-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
175            android:exported="false" >
175-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
176            <intent-filter>
176-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
177                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
177-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
177-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
178                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
178-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
178-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
179            </intent-filter>
180        </receiver>
181        <receiver
181-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
182            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
182-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
183            android:directBootAware="false"
183-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
184            android:enabled="false"
184-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
185            android:exported="false" >
185-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
186            <intent-filter>
186-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
187                <action android:name="android.intent.action.BATTERY_OKAY" />
187-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
187-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
188                <action android:name="android.intent.action.BATTERY_LOW" />
188-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
188-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
189            </intent-filter>
190        </receiver>
191        <receiver
191-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
192            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
192-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
193            android:directBootAware="false"
193-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
194            android:enabled="false"
194-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
195            android:exported="false" >
195-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
196            <intent-filter>
196-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
197                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
197-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
197-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
198                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
198-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
198-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
199            </intent-filter>
200        </receiver>
201        <receiver
201-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
202            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
202-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
203            android:directBootAware="false"
203-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
204            android:enabled="false"
204-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
205            android:exported="false" >
205-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
206            <intent-filter>
206-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
207                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
207-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
207-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
208            </intent-filter>
209        </receiver>
210        <receiver
210-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
211            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
211-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
212            android:directBootAware="false"
212-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
213            android:enabled="false"
213-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
214            android:exported="false" >
214-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
215            <intent-filter>
215-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
216                <action android:name="android.intent.action.BOOT_COMPLETED" />
216-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
216-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
217                <action android:name="android.intent.action.TIME_SET" />
217-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
217-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
218                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
218-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
218-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
219            </intent-filter>
220        </receiver>
221        <receiver
221-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
222            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
222-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
223            android:directBootAware="false"
223-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
224            android:enabled="@bool/enable_system_alarm_service_default"
224-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
225            android:exported="false" >
225-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
226            <intent-filter>
226-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
227                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
227-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
227-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
228            </intent-filter>
229        </receiver>
230        <receiver
230-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
231            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
231-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
232            android:directBootAware="false"
232-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
233            android:enabled="true"
233-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
234            android:exported="true"
234-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
235            android:permission="android.permission.DUMP" >
235-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
236            <intent-filter>
236-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
237                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
237-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
237-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
238            </intent-filter>
239        </receiver>
240
241        <uses-library
241-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c2f6d124ce4a3faad48ea9ecc6d1b03\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
242            android:name="android.ext.adservices"
242-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c2f6d124ce4a3faad48ea9ecc6d1b03\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
243            android:required="false" />
243-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c2f6d124ce4a3faad48ea9ecc6d1b03\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
244
245        <receiver
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
246            android:name="androidx.profileinstaller.ProfileInstallReceiver"
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
247            android:directBootAware="false"
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
248            android:enabled="true"
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
249            android:exported="true"
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
250            android:permission="android.permission.DUMP" >
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
251            <intent-filter>
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
252                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
253            </intent-filter>
254            <intent-filter>
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
255                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
256            </intent-filter>
257            <intent-filter>
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
258                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
259            </intent-filter>
260            <intent-filter>
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
261                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
262            </intent-filter>
263        </receiver>
264
265        <service
265-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f042b56fa71e2f56ff8d2b686c9c2992\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
266            android:name="androidx.room.MultiInstanceInvalidationService"
266-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f042b56fa71e2f56ff8d2b686c9c2992\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
267            android:directBootAware="true"
267-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f042b56fa71e2f56ff8d2b686c9c2992\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
268            android:exported="false" />
268-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f042b56fa71e2f56ff8d2b686c9c2992\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
269    </application>
270
271</manifest>
