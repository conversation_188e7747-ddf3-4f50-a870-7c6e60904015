"""
Tests for web scrapers
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from src.scrapers.base_scraper import BaseScraper
from src.scrapers.duckduckgo_scraper import DuckDuckGoScraper
from src.scrapers.scraper_manager import ScraperManager


class TestBaseScraper:
    """Test base scraper functionality"""
    
    @pytest.fixture
    def config(self):
        return {
            'user_agent': 'Test-Agent/1.0',
            'request_delay': 0.1,
            'download_timeout': 10,
            'retry_attempts': 2
        }
    
    @pytest.fixture
    def scraper(self, config):
        class TestScraper(BaseScraper):
            async def scrape(self, query, max_results=10):
                return []
        return TestScraper(config)
    
    def test_scraper_initialization(self, scraper, config):
        """Test scraper initialization"""
        assert scraper.user_agent == config['user_agent']
        assert scraper.request_delay == config['request_delay']
        assert scraper.timeout == config['download_timeout']
        assert scraper.retry_attempts == config['retry_attempts']
    
    @pytest.mark.asyncio
    async def test_scraper_context_manager(self, scraper):
        """Test scraper async context manager"""
        async with scraper:
            assert scraper.session is not None
        # Session should be closed after context
        assert scraper.session.closed
    
    def test_clean_html(self, scraper):
        """Test HTML cleaning functionality"""
        html = """
        <html>
            <head><title>Test</title></head>
            <body>
                <script>alert('test');</script>
                <nav>Navigation</nav>
                <main>
                    <h1>Main Content</h1>
                    <p>This is a paragraph.</p>
                </main>
                <footer>Footer content</footer>
            </body>
        </html>
        """
        
        soup = scraper.clean_html(html)
        
        # Check that unwanted elements are removed
        assert soup.find('script') is None
        assert soup.find('nav') is None
        assert soup.find('footer') is None
        
        # Check that main content remains
        assert soup.find('h1') is not None
        assert soup.find('p') is not None
    
    def test_extract_text(self, scraper):
        """Test text extraction from HTML"""
        html = """
        <div>
            <h1>Title</h1>
            <p>First paragraph.</p>
            <p>Second paragraph.</p>
        </div>
        """
        
        soup = scraper.clean_html(html)
        text = scraper.extract_text(soup)
        
        assert "Title" in text
        assert "First paragraph." in text
        assert "Second paragraph." in text
    
    def test_extract_metadata(self, scraper):
        """Test metadata extraction"""
        html = """
        <html lang="ar">
            <head>
                <title>Test Article</title>
                <meta name="description" content="Test description">
                <meta name="keywords" content="test, article, sample">
                <meta name="author" content="Test Author">
            </head>
            <body>Content</body>
        </html>
        """
        
        soup = scraper.clean_html(html)
        metadata = scraper.extract_metadata(soup, "https://example.com")
        
        assert metadata['title'] == "Test Article"
        assert metadata['description'] == "Test description"
        assert metadata['author'] == "Test Author"
        assert metadata['language'] == "ar"
        assert "test" in metadata['keywords']
        assert "article" in metadata['keywords']


class TestDuckDuckGoScraper:
    """Test DuckDuckGo scraper"""
    
    @pytest.fixture
    def config(self):
        return {
            'user_agent': 'Test-Agent/1.0',
            'request_delay': 0.1,
            'concurrent_requests': 2,
            'max_content_length': 1000
        }
    
    @pytest.fixture
    def scraper(self, config):
        return DuckDuckGoScraper(config)
    
    @pytest.mark.asyncio
    async def test_search_duckduckgo(self, scraper):
        """Test DuckDuckGo search functionality"""
        # Mock the DDGS search
        mock_results = [
            {
                'href': 'https://example.com/1',
                'title': 'Test Article 1',
                'body': 'Test content 1'
            },
            {
                'href': 'https://example.com/2',
                'title': 'Test Article 2',
                'body': 'Test content 2'
            }
        ]
        
        with patch.object(scraper.ddgs, 'text', return_value=mock_results):
            results = await scraper.search_duckduckgo("test query", max_results=2)
            
            assert len(results) == 2
            assert results[0]['href'] == 'https://example.com/1'
            assert results[0]['title'] == 'Test Article 1'
    
    def test_is_valid_url(self, scraper):
        """Test URL validation"""
        # Valid URLs
        assert scraper._is_valid_url("https://example.com/article")
        assert scraper._is_valid_url("http://news.site.com/story")
        
        # Invalid URLs (should be skipped)
        assert not scraper._is_valid_url("https://example.com/file.pdf")
        assert not scraper._is_valid_url("https://youtube.com/watch?v=123")
        assert not scraper._is_valid_url("https://facebook.com/post")


class TestScraperManager:
    """Test scraper manager"""
    
    @pytest.fixture
    def config(self):
        return {
            'scraping': {
                'user_agent': 'Test-Agent/1.0',
                'request_delay': 0.1,
                'concurrent_requests': 2
            }
        }
    
    @pytest.fixture
    def manager(self, config):
        return ScraperManager(config)
    
    def test_manager_initialization(self, manager):
        """Test manager initialization"""
        assert 'duckduckgo' in manager.scrapers
        # Brave scraper might not be available without API key
        
    def test_get_available_engines(self, manager):
        """Test getting available engines"""
        engines = manager.get_available_engines()
        assert isinstance(engines, list)
        assert len(engines) > 0
    
    @pytest.mark.asyncio
    async def test_health_check(self, manager):
        """Test health check functionality"""
        health_status = await manager.health_check()
        
        assert isinstance(health_status, dict)
        # Should have at least DuckDuckGo
        assert len(health_status) > 0
        
        for engine, status in health_status.items():
            assert isinstance(status, bool)


@pytest.mark.integration
class TestScraperIntegration:
    """Integration tests for scrapers"""
    
    @pytest.fixture
    def config(self):
        return {
            'scraping': {
                'user_agent': 'DeepSearch-Test/1.0',
                'request_delay': 1,
                'concurrent_requests': 1,
                'max_content_length': 5000
            }
        }
    
    @pytest.mark.asyncio
    async def test_real_duckduckgo_search(self, config):
        """Test real DuckDuckGo search (requires internet)"""
        scraper = DuckDuckGoScraper(config['scraping'])
        
        async with scraper:
            results = await scraper.scrape("Python programming", max_results=3)
            
            assert len(results) <= 3
            
            for result in results:
                assert 'url' in result
                assert 'title' in result
                assert 'content' in result
                assert result['url'].startswith('http')
                assert len(result['title']) > 0
    
    @pytest.mark.asyncio
    async def test_scraper_manager_integration(self, config):
        """Test scraper manager with real search"""
        manager = ScraperManager(config)
        
        results = await manager.search_and_scrape(
            "artificial intelligence", 
            max_results=2,
            engines=['duckduckgo']
        )
        
        assert isinstance(results, list)
        # Should get some results
        if results:  # Only check if we got results (network dependent)
            for result in results:
                assert 'url' in result
                assert 'title' in result
                assert 'source_engine' in result
