com.modetaris.gamingnews.app-emoji2-1.3.0-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c9b924042be905678c3ae3c25620591\transformed\emoji2-1.3.0\res
com.modetaris.gamingnews.app-lifecycle-livedata-2.6.2-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\178e28ba8dee01db03f971ae9fd444ac\transformed\lifecycle-livedata-2.6.2\res
com.modetaris.gamingnews.app-recyclerview-1.1.0-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e13116ddf9e56a509e8e46fc77a00ea\transformed\recyclerview-1.1.0\res
com.modetaris.gamingnews.app-security-crypto-1.1.0-alpha06-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1fcf19671a19174186d676b2580c8209\transformed\security-crypto-1.1.0-alpha06\res
com.modetaris.gamingnews.app-annotation-experimental-1.4.1-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25035b2c3f50e912041f0dfccb23c3cc\transformed\annotation-experimental-1.4.1\res
com.modetaris.gamingnews.app-constraintlayout-2.1.4-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d183ce94fffd3b026fdfcef9217c768\transformed\constraintlayout-2.1.4\res
com.modetaris.gamingnews.app-drawerlayout-1.1.1-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33b978e982dc0322ef14ff281f37724f\transformed\drawerlayout-1.1.1\res
com.modetaris.gamingnews.app-ads-adservices-java-1.0.0-beta05-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3777e7a336aeea8aaeacf26dd40d8128\transformed\ads-adservices-java-1.0.0-beta05\res
com.modetaris.gamingnews.app-browser-1.4.0-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\********************************\transformed\browser-1.4.0\res
com.modetaris.gamingnews.app-lifecycle-service-2.6.2-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f01d8bae1a1bbcbd490c73d2aaf0d2d\transformed\lifecycle-service-2.6.2\res
com.modetaris.gamingnews.app-core-runtime-2.2.0-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4534fe0abfa56bdb1a092fec1452c1df\transformed\core-runtime-2.2.0\res
com.modetaris.gamingnews.app-cardview-1.0.0-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48fdc81238eeebfb06dadbdc09fc9758\transformed\cardview-1.0.0\res
com.modetaris.gamingnews.app-appcompat-resources-1.7.0-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49a525a6f8d52fb93606538a20b1f418\transformed\appcompat-resources-1.7.0\res
com.modetaris.gamingnews.app-viewpager2-1.0.0-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\536133a7dd35f95f97165c659ad191e1\transformed\viewpager2-1.0.0\res
com.modetaris.gamingnews.app-startup-runtime-1.1.1-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5565800e4563258ee75e9205372f7e37\transformed\startup-runtime-1.1.1\res
com.modetaris.gamingnews.app-work-runtime-2.7.0-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b2ef3940c4f22c7cfd16650a3252297\transformed\work-runtime-2.7.0\res
com.modetaris.gamingnews.app-play-services-ads-22.6.0-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6268fa03c7ebf543ba887f8bf449bb01\transformed\play-services-ads-22.6.0\res
com.modetaris.gamingnews.app-play-services-basement-18.2.0-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69c40aec708a7733e482ec325600ccb6\transformed\play-services-basement-18.2.0\res
com.modetaris.gamingnews.app-core-ktx-1.16.0-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b5bb5d68d6551e141b85438454bc845\transformed\core-ktx-1.16.0\res
com.modetaris.gamingnews.app-activity-1.8.0-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e37b6f65ba8d2c3e70c173b4597e613\transformed\activity-1.8.0\res
com.modetaris.gamingnews.app-coordinatorlayout-1.1.0-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\782d595f4ce2429d5c1a2b43fe9b6ded\transformed\coordinatorlayout-1.1.0\res
com.modetaris.gamingnews.app-play-services-ads-lite-22.6.0-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a5878de4ef4605111eb579e002a829e\transformed\play-services-ads-lite-22.6.0\res
com.modetaris.gamingnews.app-lifecycle-livedata-core-2.6.2-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89edbdc966875946f294186ee38108dd\transformed\lifecycle-livedata-core-2.6.2\res
com.modetaris.gamingnews.app-ads-adservices-1.0.0-beta05-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c2f6d124ce4a3faad48ea9ecc6d1b03\transformed\ads-adservices-1.0.0-beta05\res
com.modetaris.gamingnews.app-core-viewtree-1.0.0-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\901841fb7ec94f199cafba950807a3b5\transformed\core-viewtree-1.0.0\res
com.modetaris.gamingnews.app-savedstate-1.2.1-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b140edd68e6532e09087fa724db868a5\transformed\savedstate-1.2.1\res
com.modetaris.gamingnews.app-lifecycle-process-2.6.2-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1d62220d4857f5d15385f3eaa8b2c68\transformed\lifecycle-process-2.6.2\res
com.modetaris.gamingnews.app-core-1.16.0-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b28f7da774e38c14e96bef101d6db4b2\transformed\core-1.16.0\res
com.modetaris.gamingnews.app-lifecycle-runtime-2.6.2-28 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4583b16ef5c3c4b3c7ee0cecc1f45f2\transformed\lifecycle-runtime-2.6.2\res
com.modetaris.gamingnews.app-play-services-base-18.0.0-29 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7be0c72e4429312e422d4fb96d3bf68\transformed\play-services-base-18.0.0\res
com.modetaris.gamingnews.app-webkit-1.8.0-30 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d459649882bc2aa4b0d431f900f2acae\transformed\webkit-1.8.0\res
com.modetaris.gamingnews.app-lifecycle-viewmodel-savedstate-2.6.2-31 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d58889d105e9014e1afbbaeb31be7a78\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.modetaris.gamingnews.app-material-1.12.0-32 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d64a33053620fe7b1a00c329a8dfc346\transformed\material-1.12.0\res
com.modetaris.gamingnews.app-profileinstaller-1.3.1-33 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deb8cf8215b05e9a00c1512e349a609f\transformed\profileinstaller-1.3.1\res
com.modetaris.gamingnews.app-appcompat-1.7.0-34 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e165e972ac01707ea55a93b91866c0a0\transformed\appcompat-1.7.0\res
com.modetaris.gamingnews.app-tracing-1.2.0-35 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0b7caf61cd81fb319c24532d36789c2\transformed\tracing-1.2.0\res
com.modetaris.gamingnews.app-lifecycle-viewmodel-2.6.2-36 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2303f41652d4f029aa0740df39d34a2\transformed\lifecycle-viewmodel-2.6.2\res
com.modetaris.gamingnews.app-transition-1.5.0-37 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f61c125702b0be53c7486b4d25578384\transformed\transition-1.5.0\res
com.modetaris.gamingnews.app-emoji2-views-helper-1.3.0-38 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f996fa39b9c2fd7a4317e14435d7706c\transformed\emoji2-views-helper-1.3.0\res
com.modetaris.gamingnews.app-fragment-1.5.4-39 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffd582b3347ee91a53c4a95f4eb6e814\transformed\fragment-1.5.4\res
com.modetaris.gamingnews.app-pngs-40 C:\Users\<USER>\deabsearch app\app\build\generated\res\pngs\debug
com.modetaris.gamingnews.app-resValues-41 C:\Users\<USER>\deabsearch app\app\build\generated\res\resValues\debug
com.modetaris.gamingnews.app-packageDebugResources-42 C:\Users\<USER>\deabsearch app\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.modetaris.gamingnews.app-packageDebugResources-43 C:\Users\<USER>\deabsearch app\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.modetaris.gamingnews.app-debug-44 C:\Users\<USER>\deabsearch app\app\build\intermediates\merged_res\debug\mergeDebugResources
com.modetaris.gamingnews.app-debug-45 C:\Users\<USER>\deabsearch app\app\src\debug\res
com.modetaris.gamingnews.app-main-46 C:\Users\<USER>\deabsearch app\app\src\main\res
