"""
Advanced SEO Optimizer for article content and keywords
"""

import re
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
from collections import Counter
import httpx
from urllib.parse import quote


class SEOOptimizer:
    """Advanced SEO optimizer with keyword research and content optimization"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.seo_config = config.get('seo', {})
        
        # SEO settings
        self.target_keyword_density = self.seo_config.get('target_keyword_density', 0.02)  # 2%
        self.max_keywords_per_article = self.seo_config.get('max_keywords_per_article', 10)
        self.min_keyword_length = self.seo_config.get('min_keyword_length', 3)
        
        # Arabic stop words for better keyword extraction
        self.arabic_stop_words = {
            'في', 'من', 'إلى', 'على', 'عن', 'مع', 'بعد', 'قبل', 'أثناء', 'خلال',
            'هذا', 'هذه', 'ذلك', 'تلك', 'التي', 'الذي', 'اللذان', 'اللتان', 'الذين', 'اللواتي',
            'هو', 'هي', 'هم', 'هن', 'أنت', 'أنتم', 'أنتن', 'أنا', 'نحن',
            'كان', 'كانت', 'كانوا', 'كن', 'يكون', 'تكون', 'يكونوا', 'يكن',
            'قد', 'لقد', 'قال', 'قالت', 'قالوا', 'قلن', 'يقول', 'تقول',
            'كل', 'بعض', 'جميع', 'معظم', 'أكثر', 'أقل', 'أول', 'آخر',
            'أيضا', 'أيضاً', 'كذلك', 'لكن', 'لكن', 'غير', 'سوى', 'إلا',
            'أم', 'أو', 'إما', 'لا', 'ما', 'لم', 'لن', 'ليس', 'ليست'
        }
        
        # High-value Arabic keywords for different topics
        self.topic_keywords = {
            'تقنية': ['تكنولوجيا', 'ذكاء اصطناعي', 'برمجة', 'تطبيقات', 'مواقع', 'أمن سيبراني'],
            'صحة': ['طب', 'علاج', 'أدوية', 'تغذية', 'رياضة', 'وقاية'],
            'تعليم': ['دراسة', 'جامعة', 'مدرسة', 'تعلم', 'مهارات', 'شهادة'],
            'أعمال': ['شركة', 'استثمار', 'تجارة', 'مال', 'اقتصاد', 'ربح'],
            'سفر': ['سياحة', 'رحلة', 'فندق', 'طيران', 'مطار', 'حجز']
        }
    
    async def extract_seo_keywords(
        self, 
        content: str, 
        title: str = "", 
        topic: str = "",
        target_keywords: List[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Extract and optimize SEO keywords from content
        
        Args:
            content: Article content
            title: Article title
            topic: Main topic
            target_keywords: Specific keywords to target
            
        Returns:
            List of optimized keywords with metadata
        """
        try:
            logger.info("Extracting SEO keywords from content")
            
            # Combine all text for analysis
            full_text = f"{title} {content}".strip()
            
            # Extract candidate keywords
            candidates = self._extract_keyword_candidates(full_text)
            
            # Score keywords based on various factors
            scored_keywords = self._score_keywords(candidates, full_text, topic)
            
            # Add target keywords if provided
            if target_keywords:
                for keyword in target_keywords:
                    if keyword not in [k['keyword'] for k in scored_keywords]:
                        score = self._calculate_keyword_score(keyword, full_text, topic)
                        scored_keywords.append({
                            'keyword': keyword,
                            'score': score,
                            'frequency': full_text.lower().count(keyword.lower()),
                            'type': 'target'
                        })
            
            # Sort by score and limit results
            scored_keywords.sort(key=lambda x: x['score'], reverse=True)
            top_keywords = scored_keywords[:self.max_keywords_per_article]
            
            # Add SEO metadata
            for keyword_data in top_keywords:
                keyword_data.update(self._get_keyword_seo_data(keyword_data['keyword'], full_text))
            
            logger.info(f"Extracted {len(top_keywords)} SEO keywords")
            return top_keywords
            
        except Exception as e:
            logger.error(f"Failed to extract SEO keywords: {str(e)}")
            return []
    
    def _extract_keyword_candidates(self, text: str) -> List[str]:
        """Extract potential keywords from text"""
        try:
            # Clean and normalize text
            cleaned_text = re.sub(r'[^\w\s]', ' ', text)
            words = cleaned_text.split()
            
            candidates = set()
            
            # Single words
            for word in words:
                word = word.strip().lower()
                if (len(word) >= self.min_keyword_length and 
                    word not in self.arabic_stop_words and
                    not word.isdigit()):
                    candidates.add(word)
            
            # Two-word phrases
            for i in range(len(words) - 1):
                phrase = f"{words[i]} {words[i+1]}".strip().lower()
                if (len(phrase) >= self.min_keyword_length * 2 and
                    not any(stop_word in phrase.split() for stop_word in self.arabic_stop_words)):
                    candidates.add(phrase)
            
            # Three-word phrases (for long-tail keywords)
            for i in range(len(words) - 2):
                phrase = f"{words[i]} {words[i+1]} {words[i+2]}".strip().lower()
                if (len(phrase) >= self.min_keyword_length * 3 and
                    not any(stop_word in phrase.split() for stop_word in self.arabic_stop_words)):
                    candidates.add(phrase)
            
            return list(candidates)
            
        except Exception as e:
            logger.error(f"Failed to extract keyword candidates: {str(e)}")
            return []
    
    def _score_keywords(self, candidates: List[str], text: str, topic: str) -> List[Dict[str, Any]]:
        """Score keywords based on various SEO factors"""
        scored_keywords = []
        text_lower = text.lower()
        
        for candidate in candidates:
            score = self._calculate_keyword_score(candidate, text, topic)
            frequency = text_lower.count(candidate.lower())
            
            # Determine keyword type
            keyword_type = 'extracted'
            if any(topic_word in candidate for topic_word in self.topic_keywords.get(topic, [])):
                keyword_type = 'topic_related'
            
            scored_keywords.append({
                'keyword': candidate,
                'score': score,
                'frequency': frequency,
                'type': keyword_type
            })
        
        return scored_keywords
    
    def _calculate_keyword_score(self, keyword: str, text: str, topic: str) -> float:
        """Calculate SEO score for a keyword"""
        try:
            score = 0.0
            text_lower = text.lower()
            keyword_lower = keyword.lower()
            
            # Frequency score (30% weight)
            frequency = text_lower.count(keyword_lower)
            total_words = len(text.split())
            frequency_score = min(frequency / total_words, 0.05) * 6  # Cap at 5% density
            score += frequency_score * 0.3
            
            # Length score (20% weight) - prefer medium-length keywords
            length_score = 1.0
            if len(keyword.split()) == 2:
                length_score = 1.2  # Boost for two-word phrases
            elif len(keyword.split()) == 3:
                length_score = 1.1  # Slight boost for three-word phrases
            elif len(keyword.split()) > 3:
                length_score = 0.8  # Penalize very long phrases
            
            score += length_score * 0.2
            
            # Topic relevance score (25% weight)
            topic_score = 0.0
            if topic and topic in self.topic_keywords:
                topic_keywords = self.topic_keywords[topic]
                if any(topic_word in keyword_lower for topic_word in topic_keywords):
                    topic_score = 1.0
            
            score += topic_score * 0.25
            
            # Position score (15% weight) - keywords appearing early are better
            first_occurrence = text_lower.find(keyword_lower)
            if first_occurrence != -1:
                position_score = max(0, 1 - (first_occurrence / len(text)))
                score += position_score * 0.15
            
            # Uniqueness score (10% weight) - prefer less common words
            word_count = Counter(text_lower.split())
            avg_word_frequency = sum(word_count[word] for word in keyword_lower.split()) / len(keyword_lower.split())
            uniqueness_score = max(0, 1 - (avg_word_frequency / total_words))
            score += uniqueness_score * 0.1
            
            return score
            
        except Exception as e:
            logger.error(f"Failed to calculate keyword score: {str(e)}")
            return 0.0
    
    def _get_keyword_seo_data(self, keyword: str, text: str) -> Dict[str, Any]:
        """Get additional SEO data for a keyword"""
        try:
            text_lower = text.lower()
            keyword_lower = keyword.lower()
            
            # Calculate keyword density
            frequency = text_lower.count(keyword_lower)
            total_words = len(text.split())
            density = (frequency / total_words) * 100 if total_words > 0 else 0
            
            # Find positions of keyword occurrences
            positions = []
            start = 0
            while True:
                pos = text_lower.find(keyword_lower, start)
                if pos == -1:
                    break
                positions.append(pos)
                start = pos + 1
            
            # Calculate distribution score
            if len(positions) > 1:
                text_length = len(text)
                distribution_score = 1.0 - (max(positions) - min(positions)) / text_length
            else:
                distribution_score = 0.5
            
            return {
                'density': round(density, 2),
                'positions': positions,
                'distribution_score': round(distribution_score, 2),
                'is_optimal_density': 1.0 <= density <= 3.0,
                'search_volume_estimate': self._estimate_search_volume(keyword),
                'competition_level': self._estimate_competition(keyword)
            }
            
        except Exception as e:
            logger.error(f"Failed to get keyword SEO data: {str(e)}")
            return {}
    
    def _estimate_search_volume(self, keyword: str) -> str:
        """Estimate search volume for a keyword (simplified)"""
        try:
            # Simple heuristic based on keyword characteristics
            word_count = len(keyword.split())
            
            if word_count == 1:
                if len(keyword) <= 4:
                    return "high"
                elif len(keyword) <= 8:
                    return "medium"
                else:
                    return "low"
            elif word_count == 2:
                return "medium"
            else:
                return "low"
                
        except Exception:
            return "unknown"
    
    def _estimate_competition(self, keyword: str) -> str:
        """Estimate competition level for a keyword (simplified)"""
        try:
            # Simple heuristic based on keyword characteristics
            word_count = len(keyword.split())
            
            # Single words usually have higher competition
            if word_count == 1:
                return "high"
            elif word_count == 2:
                return "medium"
            else:
                return "low"
                
        except Exception:
            return "unknown"
    
    async def optimize_content_for_seo(
        self, 
        content: str, 
        target_keywords: List[str],
        title: str = ""
    ) -> Dict[str, Any]:
        """
        Optimize content for better SEO performance
        
        Args:
            content: Original content
            target_keywords: Keywords to optimize for
            title: Article title
            
        Returns:
            Optimization suggestions and metrics
        """
        try:
            logger.info("Optimizing content for SEO")
            
            suggestions = []
            metrics = {}
            
            # Analyze current keyword usage
            for keyword in target_keywords:
                keyword_analysis = self._analyze_keyword_usage(content, keyword)
                metrics[keyword] = keyword_analysis
                
                # Generate suggestions based on analysis
                if keyword_analysis['density'] < 1.0:
                    suggestions.append({
                        'type': 'keyword_density',
                        'keyword': keyword,
                        'message': f"زيادة كثافة الكلمة المفتاحية '{keyword}' (حالياً {keyword_analysis['density']:.1f}%)",
                        'priority': 'high'
                    })
                elif keyword_analysis['density'] > 3.0:
                    suggestions.append({
                        'type': 'keyword_density',
                        'keyword': keyword,
                        'message': f"تقليل كثافة الكلمة المفتاحية '{keyword}' (حالياً {keyword_analysis['density']:.1f}%)",
                        'priority': 'medium'
                    })
            
            # Check title optimization
            title_suggestions = self._analyze_title_seo(title, target_keywords)
            suggestions.extend(title_suggestions)
            
            # Check content structure
            structure_suggestions = self._analyze_content_structure(content)
            suggestions.extend(structure_suggestions)
            
            return {
                'suggestions': suggestions,
                'metrics': metrics,
                'overall_score': self._calculate_overall_seo_score(metrics, content, title)
            }
            
        except Exception as e:
            logger.error(f"Failed to optimize content for SEO: {str(e)}")
            return {'suggestions': [], 'metrics': {}, 'overall_score': 0}
    
    def _analyze_keyword_usage(self, content: str, keyword: str) -> Dict[str, Any]:
        """Analyze how a keyword is used in the content"""
        content_lower = content.lower()
        keyword_lower = keyword.lower()
        
        frequency = content_lower.count(keyword_lower)
        total_words = len(content.split())
        density = (frequency / total_words) * 100 if total_words > 0 else 0
        
        # Check if keyword appears in first paragraph
        first_paragraph = content.split('\n\n')[0] if '\n\n' in content else content[:200]
        in_first_paragraph = keyword_lower in first_paragraph.lower()
        
        return {
            'frequency': frequency,
            'density': density,
            'in_first_paragraph': in_first_paragraph,
            'total_words': total_words
        }
    
    def _analyze_title_seo(self, title: str, target_keywords: List[str]) -> List[Dict[str, Any]]:
        """Analyze title for SEO optimization"""
        suggestions = []
        
        if not title:
            return suggestions
        
        # Check title length
        if len(title) < 30:
            suggestions.append({
                'type': 'title_length',
                'message': 'العنوان قصير جداً، يُفضل أن يكون بين 50-60 حرف',
                'priority': 'medium'
            })
        elif len(title) > 70:
            suggestions.append({
                'type': 'title_length',
                'message': 'العنوان طويل جداً، يُفضل أن يكون بين 50-60 حرف',
                'priority': 'medium'
            })
        
        # Check if target keywords are in title
        title_lower = title.lower()
        for keyword in target_keywords:
            if keyword.lower() not in title_lower:
                suggestions.append({
                    'type': 'title_keyword',
                    'keyword': keyword,
                    'message': f"إضافة الكلمة المفتاحية '{keyword}' في العنوان",
                    'priority': 'high'
                })
        
        return suggestions
    
    def _analyze_content_structure(self, content: str) -> List[Dict[str, Any]]:
        """Analyze content structure for SEO"""
        suggestions = []
        
        # Check for headings
        heading_count = len(re.findall(r'^#+\s', content, re.MULTILINE))
        if heading_count < 2:
            suggestions.append({
                'type': 'content_structure',
                'message': 'إضافة المزيد من العناوين الفرعية لتحسين هيكل المحتوى',
                'priority': 'medium'
            })
        
        # Check content length
        word_count = len(content.split())
        if word_count < 300:
            suggestions.append({
                'type': 'content_length',
                'message': 'المحتوى قصير، يُفضل أن يكون أكثر من 300 كلمة',
                'priority': 'high'
            })
        
        return suggestions
    
    def _calculate_overall_seo_score(self, metrics: Dict, content: str, title: str) -> float:
        """Calculate overall SEO score"""
        try:
            score = 0.0
            max_score = 100.0
            
            # Keyword optimization score (40%)
            if metrics:
                keyword_scores = []
                for keyword_data in metrics.values():
                    density = keyword_data['density']
                    if 1.0 <= density <= 3.0:
                        keyword_scores.append(1.0)
                    elif density < 1.0:
                        keyword_scores.append(density / 1.0)
                    else:
                        keyword_scores.append(max(0, 2.0 - (density - 3.0) / 2.0))
                
                if keyword_scores:
                    score += (sum(keyword_scores) / len(keyword_scores)) * 40
            
            # Content length score (20%)
            word_count = len(content.split())
            if word_count >= 500:
                score += 20
            elif word_count >= 300:
                score += 15
            else:
                score += (word_count / 300) * 15
            
            # Title optimization score (20%)
            if title:
                if 50 <= len(title) <= 70:
                    score += 20
                elif 30 <= len(title) <= 80:
                    score += 15
                else:
                    score += 10
            
            # Content structure score (20%)
            heading_count = len(re.findall(r'^#+\s', content, re.MULTILINE))
            if heading_count >= 3:
                score += 20
            elif heading_count >= 2:
                score += 15
            elif heading_count >= 1:
                score += 10
            
            return min(score, max_score)
            
        except Exception as e:
            logger.error(f"Failed to calculate SEO score: {str(e)}")
            return 0.0
