"""
Google Gemini AI provider implementation
"""

import os
import asyncio
from typing import List, Dict, Any, Optional
from loguru import logger
import google.generativeai as genai
from .base_ai_provider import BaseAIProvider


class GeminiProvider(BaseAIProvider):
    """AI provider using Google Gemini API"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = os.getenv('GEMINI_API_KEY')
        self.model_name = config.get('model', 'gemini-2.0-flash-exp')
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Gemini client"""
        if not self.api_key:
            logger.warning("Gemini API key not found. Set GEMINI_API_KEY environment variable.")
            return
        
        try:
            genai.configure(api_key=self.api_key)
            self.client = genai.GenerativeModel(self.model_name)
            logger.info(f"Gemini client initialized with model: {self.model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {str(e)}")
            self.client = None
    
    async def generate_text(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Generate text using Gemini
        
        Args:
            prompt: User prompt
            system_prompt: System prompt (optional)
            **kwargs: Additional parameters
            
        Returns:
            Generated text
        """
        if not self.client:
            logger.error("Gemini client not available")
            return ""
        
        try:
            # Combine system prompt and user prompt
            full_prompt = prompt
            if system_prompt:
                full_prompt = f"{system_prompt}\n\n{prompt}"
            
            # Configure generation parameters
            generation_config = genai.types.GenerationConfig(
                temperature=kwargs.get('temperature', self.temperature),
                max_output_tokens=kwargs.get('max_tokens', self.max_tokens),
                top_p=0.8,
                top_k=40
            )
            
            # Run generation in thread pool since it's synchronous
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.client.generate_content(
                    full_prompt,
                    generation_config=generation_config
                )
            )
            
            if response and response.text:
                logger.info(f"Generated text with Gemini model: {self.model_name}")
                return response.text.strip()
            else:
                logger.warning("Empty response from Gemini")
                return ""
                
        except Exception as e:
            logger.error(f"Gemini generation failed: {str(e)}")
            return ""
    
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings using Gemini
        Note: Gemini doesn't have a direct embedding API, so we'll use a fallback
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        logger.warning("Gemini doesn't support embeddings directly. Using fallback embedding model.")
        
        try:
            # Use sentence-transformers as fallback for embeddings
            from sentence_transformers import SentenceTransformer
            
            model_name = self.config.get('embedding_model', 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2')
            model = SentenceTransformer(model_name)
            
            # Run embedding generation in thread pool
            loop = asyncio.get_event_loop()
            embeddings = await loop.run_in_executor(
                None, 
                model.encode, 
                texts
            )
            
            logger.info(f"Generated embeddings for {len(texts)} texts using fallback model")
            return embeddings.tolist()
            
        except Exception as e:
            logger.error(f"Embedding generation failed: {str(e)}")
            return []
    
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> str:
        """
        Chat completion using Gemini
        
        Args:
            messages: List of chat messages
            **kwargs: Additional parameters
            
        Returns:
            Chat response
        """
        if not self.client:
            logger.error("Gemini client not available")
            return ""
        
        try:
            # Convert messages to Gemini format
            conversation_text = ""
            for message in messages:
                role = message.get('role', 'user')
                content = message.get('content', '')
                
                if role == 'system':
                    conversation_text += f"System: {content}\n\n"
                elif role == 'user':
                    conversation_text += f"User: {content}\n\n"
                elif role == 'assistant':
                    conversation_text += f"Assistant: {content}\n\n"
            
            # Add final prompt
            conversation_text += "Assistant:"
            
            # Configure generation parameters
            generation_config = genai.types.GenerationConfig(
                temperature=kwargs.get('temperature', self.temperature),
                max_output_tokens=kwargs.get('max_tokens', self.max_tokens),
                top_p=0.8,
                top_k=40
            )
            
            # Run generation in thread pool
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.client.generate_content(
                    conversation_text,
                    generation_config=generation_config
                )
            )
            
            if response and response.text:
                logger.info(f"Chat completion with Gemini model: {self.model_name}")
                return response.text.strip()
            else:
                logger.warning("Empty response from Gemini chat")
                return ""
                
        except Exception as e:
            logger.error(f"Gemini chat completion failed: {str(e)}")
            return ""
    
    async def health_check(self) -> bool:
        """
        Check if Gemini service is available
        
        Returns:
            True if service is healthy
        """
        if not self.client:
            return False
        
        try:
            # Simple test generation
            test_response = await self.generate_text("Hello", max_tokens=10)
            
            if test_response:
                logger.info("Gemini health check passed")
                return True
            else:
                logger.error("Gemini health check failed: empty response")
                return False
                
        except Exception as e:
            logger.error(f"Gemini health check failed: {str(e)}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the Gemini model
        
        Returns:
            Model information dictionary
        """
        return {
            "provider": "gemini",
            "model": self.model_name,
            "api_key_configured": bool(self.api_key),
            "client_initialized": bool(self.client),
            "supports_embeddings": False,  # Gemini doesn't have direct embedding support
            "supports_chat": True,
            "supports_text_generation": True
        }
