from fastapi import FastAPI
import uvicorn

app = FastAPI()

@app.get("/")
def read_root():
    return {"message": "Demo Professional News API Working!"}

@app.get("/news/professional/single")
def get_demo_article(query: str = "test", agent_id: str = "demo"):
    return {
        "query": query,
        "agent_id": agent_id,
        "professional_articles": [{
            "title": f"مقال تجريبي حول {query}",
            "url": "https://demo.com/article",
            "content": f"هذا مقال تجريبي مفصل حول {query}",
            "full_content": f"المحتوى الكامل للمقال حول {query}...",
            "professional_summary": f"ملخص احترافي للمقال حول {query}",
            "has_full_content": True,
            "news_score": 8.5
        }],
        "total_delivered": 1,
        "message": "مقال تجريبي تم تقديمه بنجاح"
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
