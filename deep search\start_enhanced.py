#!/usr/bin/env python3
"""
Quick start script for DeepSearch Agent Pro
Enhanced version with all new features
"""

import asyncio
import sys
import os
import webbrowser
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config_loader import load_config
from src.utils.logger import setup_logging
from loguru import logger


def print_banner():
    """Print startup banner"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    DeepSearch Agent Pro 🚀                   ║
║                                                              ║
║  منصة متقدمة للبحث العميق وإنتاج المقالات بالذكاء الاصطناعي  ║
║                                                              ║
║  المميزات الجديدة:                                          ║
║  🔍 البحث العميق المحسن                                     ║
║  🤖 إنتاج المقالات بـ Gemini AI                            ║
║  📊 تحسين SEO متقدم                                         ║
║  📝 نشر مباشر على Blogger                                  ║
║  💻 واجهة مستخدم محسنة                                      ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_requirements():
    """Check if all required packages are installed"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'google.generativeai',
        'google.oauth2',
        'googleapiclient',
        'loguru',
        'httpx'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"Missing required packages: {', '.join(missing_packages)}")
        logger.info("Please install requirements: pip install -r requirements.txt")
        return False
    
    return True


def check_config():
    """Check configuration files"""
    config_file = Path("config/config.yaml")
    
    if not config_file.exists():
        logger.warning("Configuration file not found. Using default settings.")
        return True
    
    try:
        config = load_config()
        logger.info("Configuration loaded successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to load configuration: {str(e)}")
        return False


async def start_server():
    """Start the enhanced server"""
    try:
        import uvicorn
        from src.api.main import app
        
        # Load configuration
        config = load_config()
        api_config = config.get('api', {})
        
        host = api_config.get('host', '0.0.0.0')
        port = api_config.get('port', 8000)
        
        logger.info(f"Starting DeepSearch Agent Pro server...")
        logger.info(f"Server will be available at: http://localhost:{port}")
        logger.info(f"Enhanced interface: http://localhost:{port}/web/enhanced.html")
        logger.info(f"API documentation: http://localhost:{port}/docs")
        
        # Open browser automatically
        try:
            webbrowser.open(f"http://localhost:{port}/web/enhanced.html")
        except Exception:
            pass
        
        # Start server
        uvicorn.run(
            app,
            host=host,
            port=port,
            workers=1,
            reload=False,
            log_level="info"
        )
        
    except Exception as e:
        logger.error(f"Failed to start server: {str(e)}")
        sys.exit(1)


def show_usage_instructions():
    """Show usage instructions"""
    instructions = """
🚀 DeepSearch Agent Pro - دليل الاستخدام السريع

1️⃣ إعداد Gemini AI:
   - انتقل لتبويب "الإعدادات"
   - أدخل Gemini API Key
   - اختر النموذج المطلوب
   - اضغط "حفظ إعدادات Gemini"

2️⃣ إعداد Blogger (اختياري):
   - أنشئ Service Account في Google Cloud Console
   - حمل ملف JSON للـ Service Account
   - أضف Service Account كمحرر في مدونة Blogger
   - في الواجهة، الصق محتوى JSON وأدخل Blog ID

3️⃣ البحث العميق:
   - انتقل لتبويب "البحث العميق"
   - أدخل الموضوع المطلوب
   - اختر نوع المقال واللغة
   - اضغط "بحث عميق"

4️⃣ إنتاج المقالات:
   - انتقل لتبويب "إنتاج المقالات"
   - أدخل موضوع المقال
   - حدد الإعدادات المطلوبة
   - اضغط "إنتاج المقال"

5️⃣ النشر والتصدير:
   - بعد إنتاج المقال، انتقل لتبويب "النشر والتصدير"
   - اختر النشر على Blogger أو إنشاء نسخة HTML
   - حدد الإعدادات واضغط النشر

📚 للمزيد من المعلومات، راجع ملف README.md
    """
    print(instructions)


def main():
    """Main function"""
    print_banner()
    
    # Setup logging
    setup_logging({})
    
    logger.info("Starting DeepSearch Agent Pro...")
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check configuration
    if not check_config():
        sys.exit(1)
    
    # Show usage instructions
    show_usage_instructions()
    
    # Start server
    try:
        asyncio.run(start_server())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
