#!/usr/bin/env python3
"""
Comprehensive test script for DeepSearch Agent Pro
Tests all new features and integrations
"""

import asyncio
import sys
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config_loader import load_config
from src.utils.logger import setup_logging
from src.core.search_engine import DeepSearchEngine
from src.ai.article_generator import ArticleGenerator
from src.publishing.blogger_publisher import BloggerPublisher
from src.seo.seo_optimizer import SEOOptimizer
from loguru import logger


class EnhancedSystemTester:
    """Comprehensive tester for the enhanced system"""
    
    def __init__(self):
        self.config = load_config()
        self.test_results = {}
        
    async def test_deep_search(self):
        """Test enhanced deep search functionality"""
        logger.info("🔍 Testing Deep Search System...")
        
        try:
            async with DeepSearchEngine(self.config) as engine:
                # Test deep single article search
                result = await engine.deep_single_article_search(
                    query="الذكاء الاصطناعي في التعليم",
                    target_language="ar",
                    article_style="professional"
                )
                
                success = result.get('success', False)
                self.test_results['deep_search'] = {
                    'status': 'PASS' if success else 'FAIL',
                    'details': {
                        'query_processed': bool(result.get('query')),
                        'article_selected': bool(result.get('selected_article')),
                        'analysis_performed': bool(result.get('selected_article', {}).get('deep_analysis')),
                        'seo_keywords_extracted': bool(result.get('selected_article', {}).get('seo_keywords')),
                        'processing_time': result.get('processing_time', 0)
                    }
                }
                
                if success:
                    logger.success("✅ Deep Search: PASSED")
                else:
                    logger.error(f"❌ Deep Search: FAILED - {result.get('error')}")
                    
        except Exception as e:
            logger.error(f"❌ Deep Search: ERROR - {str(e)}")
            self.test_results['deep_search'] = {'status': 'ERROR', 'error': str(e)}
    
    async def test_article_generation(self):
        """Test Gemini article generation"""
        logger.info("🤖 Testing Article Generation System...")
        
        try:
            article_generator = ArticleGenerator(self.config)
            
            # Test configuration check
            config_status = article_generator.get_status()
            
            if not config_status['configured']:
                logger.warning("⚠️ Gemini not configured - skipping article generation test")
                self.test_results['article_generation'] = {
                    'status': 'SKIP',
                    'reason': 'Gemini API not configured'
                }
                return
            
            # Test article generation
            test_research_data = {
                'selected_article': {
                    'original_article': {
                        'title': 'اختبار النظام',
                        'content': 'هذا اختبار شامل لنظام إنتاج المقالات المحسن'
                    },
                    'deep_analysis': {
                        'comprehensive_summary': 'ملخص شامل للاختبار'
                    },
                    'seo_keywords': ['اختبار', 'نظام', 'مقالات']
                }
            }
            
            result = await article_generator.generate_article(
                topic="اختبار النظام المحسن",
                research_data=test_research_data,
                article_type="professional",
                target_language="ar",
                min_words=200,
                max_words=400
            )
            
            success = result.get('success', False)
            self.test_results['article_generation'] = {
                'status': 'PASS' if success else 'FAIL',
                'details': {
                    'article_generated': bool(result.get('article')),
                    'title_created': bool(result.get('article', {}).get('title')),
                    'content_created': bool(result.get('article', {}).get('content')),
                    'seo_analysis_included': bool(result.get('article', {}).get('seo_analysis')),
                    'egyptian_dialect_applied': result.get('article', {}).get('egyptian_dialect_applied', False),
                    'word_count': result.get('article', {}).get('word_count', 0),
                    'processing_time': result.get('generation_metadata', {}).get('processing_time', 0)
                }
            }
            
            if success:
                logger.success("✅ Article Generation: PASSED")
            else:
                logger.error(f"❌ Article Generation: FAILED - {result.get('error')}")
                
        except Exception as e:
            logger.error(f"❌ Article Generation: ERROR - {str(e)}")
            self.test_results['article_generation'] = {'status': 'ERROR', 'error': str(e)}
    
    async def test_seo_optimization(self):
        """Test SEO optimization system"""
        logger.info("📊 Testing SEO Optimization System...")
        
        try:
            seo_optimizer = SEOOptimizer(self.config)
            
            test_content = """
            هذا مقال اختبار عن الذكاء الاصطناعي في التعليم.
            يتناول المقال أهمية الذكاء الاصطناعي في تطوير التعليم.
            كما يشرح كيفية استخدام التكنولوجيا في الفصول الدراسية.
            """
            
            test_title = "الذكاء الاصطناعي في التعليم"
            test_keywords = ["ذكاء اصطناعي", "تعليم", "تكنولوجيا"]
            
            # Test keyword extraction
            extracted_keywords = await seo_optimizer.extract_seo_keywords(
                content=test_content,
                title=test_title,
                topic="تعليم"
            )
            
            # Test content optimization
            optimization_result = await seo_optimizer.optimize_content_for_seo(
                content=test_content,
                target_keywords=test_keywords,
                title=test_title
            )
            
            self.test_results['seo_optimization'] = {
                'status': 'PASS',
                'details': {
                    'keywords_extracted': len(extracted_keywords),
                    'optimization_suggestions': len(optimization_result.get('suggestions', [])),
                    'seo_score': optimization_result.get('overall_score', 0),
                    'metrics_calculated': bool(optimization_result.get('metrics'))
                }
            }
            
            logger.success("✅ SEO Optimization: PASSED")
            
        except Exception as e:
            logger.error(f"❌ SEO Optimization: ERROR - {str(e)}")
            self.test_results['seo_optimization'] = {'status': 'ERROR', 'error': str(e)}
    
    def test_blogger_publisher(self):
        """Test Blogger publisher (configuration only)"""
        logger.info("📝 Testing Blogger Publisher System...")
        
        try:
            blogger_publisher = BloggerPublisher(self.config)
            
            # Test status check
            status = blogger_publisher.get_status()
            
            # Test HTML generation
            test_html = blogger_publisher.generate_html_copy(
                title="مقال اختبار",
                content="هذا محتوى اختبار للنظام",
                meta_description="وصف تجريبي",
                seo_keywords=["اختبار", "نظام"]
            )
            
            self.test_results['blogger_publisher'] = {
                'status': 'PASS',
                'details': {
                    'status_check': bool(status),
                    'html_generation': bool(test_html),
                    'configured': status.get('configured', False),
                    'ready': status.get('ready', False)
                }
            }
            
            logger.success("✅ Blogger Publisher: PASSED")
            
        except Exception as e:
            logger.error(f"❌ Blogger Publisher: ERROR - {str(e)}")
            self.test_results['blogger_publisher'] = {'status': 'ERROR', 'error': str(e)}
    
    async def run_all_tests(self):
        """Run all system tests"""
        logger.info("🚀 Starting Comprehensive System Test...")
        
        # Test individual components
        await self.test_deep_search()
        await self.test_article_generation()
        await self.test_seo_optimization()
        self.test_blogger_publisher()
        
        # Generate test report
        self.generate_test_report()
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        logger.info("📋 Generating Test Report...")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASS')
        failed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'FAIL')
        error_tests = sum(1 for result in self.test_results.values() if result['status'] == 'ERROR')
        skipped_tests = sum(1 for result in self.test_results.values() if result['status'] == 'SKIP')
        
        report = f"""
╔══════════════════════════════════════════════════════════════╗
║                    Test Report Summary                        ║
╠══════════════════════════════════════════════════════════════╣
║ Total Tests:    {total_tests:2d}                                        ║
║ Passed:         {passed_tests:2d}                                        ║
║ Failed:         {failed_tests:2d}                                        ║
║ Errors:         {error_tests:2d}                                        ║
║ Skipped:        {skipped_tests:2d}                                        ║
╠══════════════════════════════════════════════════════════════╣
║                    Detailed Results                          ║
╚══════════════════════════════════════════════════════════════╝
        """
        
        print(report)
        
        for test_name, result in self.test_results.items():
            status_icon = {
                'PASS': '✅',
                'FAIL': '❌',
                'ERROR': '💥',
                'SKIP': '⏭️'
            }.get(result['status'], '❓')
            
            print(f"{status_icon} {test_name.replace('_', ' ').title()}: {result['status']}")
            
            if result['status'] in ['FAIL', 'ERROR'] and 'error' in result:
                print(f"   Error: {result['error']}")
            
            if 'details' in result:
                for key, value in result['details'].items():
                    print(f"   {key}: {value}")
            
            print()
        
        # Save detailed report to file
        report_file = Path("test_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Detailed test report saved to: {report_file}")
        
        # Overall result
        if failed_tests == 0 and error_tests == 0:
            logger.success("🎉 All tests passed successfully!")
        else:
            logger.warning(f"⚠️ {failed_tests + error_tests} tests failed or had errors")


async def main():
    """Main test function"""
    # Setup logging
    setup_logging({})
    
    print("🧪 DeepSearch Agent Pro - Comprehensive System Test")
    print("=" * 60)
    
    tester = EnhancedSystemTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
