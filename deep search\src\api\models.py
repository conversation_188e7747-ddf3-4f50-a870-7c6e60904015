"""
Pydantic models for API requests and responses
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime


class SearchRequest(BaseModel):
    """Request model for search endpoint"""
    query: str = Field(..., description="Search query", min_length=1, max_length=500)
    max_results: Optional[int] = Field(20, description="Maximum number of results", ge=1, le=100)
    engines: Optional[List[str]] = Field(None, description="List of search engines to use")
    summarize: Optional[bool] = Field(True, description="Whether to summarize articles")
    use_cache: Optional[bool] = Field(True, description="Whether to use cache")
    enhance_query: Optional[bool] = Field(True, description="Whether to enhance query with AI")
    language: Optional[str] = Field("ar", description="Search language")


class RAGRequest(BaseModel):
    """Request model for RAG search endpoint"""
    query: str = Field(..., description="Search query", min_length=1, max_length=500)
    max_context_articles: Optional[int] = Field(10, description="Maximum articles for context", ge=1, le=50)
    use_recent_articles: Optional[bool] = Field(True, description="Include recent articles in context")


class ArticleResponse(BaseModel):
    """Response model for article data"""
    id: Optional[int] = Field(None, description="Article ID")
    url: str = Field(..., description="Article URL")
    title: str = Field("", description="Article title")
    content: str = Field("", description="Article content")
    summary: Optional[str] = Field(None, description="Article summary")
    description: str = Field("", description="Article description")
    author: str = Field("", description="Article author")
    language: str = Field("ar", description="Article language")
    keywords: List[str] = Field([], description="Article keywords")
    ai_keywords: List[str] = Field([], description="AI-extracted keywords")
    source_engine: str = Field("", description="Search engine used")
    scraped_at: Optional[str] = Field(None, description="Scraping timestamp")
    is_summarized: bool = Field(False, description="Whether article is summarized")
    sentiment: Optional[Dict[str, Any]] = Field(None, description="Sentiment analysis")
    similarity_score: float = Field(0.0, description="Similarity score for search")


class SearchResponse(BaseModel):
    """Response model for search endpoint"""
    query: str = Field(..., description="Original search query")
    enhanced_query: str = Field("", description="AI-enhanced query")
    articles: List[ArticleResponse] = Field([], description="List of articles")
    total_results: int = Field(0, description="Total number of results")
    processing_time: float = Field(0.0, description="Processing time in seconds")
    engines_used: List[str] = Field([], description="Search engines used")
    cached: bool = Field(False, description="Whether result was cached")
    query_id: Optional[int] = Field(None, description="Query ID in database")
    error: Optional[str] = Field(None, description="Error message if any")


class SourceResponse(BaseModel):
    """Response model for RAG sources"""
    title: str = Field("", description="Source title")
    url: str = Field("", description="Source URL")
    similarity_score: float = Field(0.0, description="Similarity score")


class RAGResponse(BaseModel):
    """Response model for RAG search endpoint"""
    query: str = Field(..., description="Search query")
    answer: str = Field("", description="Generated answer")
    sources: List[SourceResponse] = Field([], description="Source articles")
    confidence: float = Field(0.0, description="Confidence score")
    context_used: Optional[int] = Field(None, description="Number of articles used for context")
    total_context_articles: int = Field(0, description="Total articles available for context")
    processing_time: float = Field(0.0, description="Processing time in seconds")
    error: Optional[str] = Field(None, description="Error message if any")


class TrendingTopicResponse(BaseModel):
    """Response model for trending topics"""
    topic: str = Field(..., description="Trending topic")
    frequency: int = Field(0, description="Topic frequency")
    articles_count: int = Field(0, description="Number of articles")


class TrendingTopicsResponse(BaseModel):
    """Response model for trending topics endpoint"""
    topics: List[TrendingTopicResponse] = Field([], description="List of trending topics")
    total_topics: int = Field(0, description="Total number of topics")
    generated_at: str = Field(..., description="Generation timestamp")


class HealthCheckResponse(BaseModel):
    """Response model for health check endpoint"""
    timestamp: float = Field(..., description="Health check timestamp")
    overall_status: str = Field(..., description="Overall system status")
    scrapers: Dict[str, Any] = Field({}, description="Scraper health status")
    ai: Dict[str, Any] = Field({}, description="AI components health status")
    database: Dict[str, Any] = Field({}, description="Database health status")
    error: Optional[str] = Field(None, description="Error message if any")


class DatabaseStatsResponse(BaseModel):
    """Response model for database statistics"""
    total_articles: int = Field(0, description="Total number of articles")
    total_queries: int = Field(0, description="Total number of queries")
    total_embeddings: int = Field(0, description="Total number of embeddings")
    total_cache_entries: int = Field(0, description="Total cache entries")
    recent_articles_24h: int = Field(0, description="Articles added in last 24 hours")


class ErrorResponse(BaseModel):
    """Response model for errors"""
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Error details")
    timestamp: str = Field(..., description="Error timestamp")


class SuccessResponse(BaseModel):
    """Generic success response"""
    success: bool = Field(True, description="Success status")
    message: str = Field("", description="Success message")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional data")
