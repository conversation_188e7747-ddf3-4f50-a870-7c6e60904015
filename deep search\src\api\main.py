"""
FastAPI main application
"""

import os
import yaml
from datetime import datetime
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from loguru import logger
from typing import Dict, Any

from .models import (
    SearchRequest, SearchResponse, RAGRequest, RAGResponse,
    TrendingTopicsResponse, HealthCheckResponse, DatabaseStatsResponse,
    ErrorResponse, SuccessResponse
)
from .news_routes import router as news_router
from ..core.search_engine import DeepSearchEngine
from ..utils.config_loader import load_config


# Global search engine instance
search_engine: DeepSearchEngine = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global search_engine
    
    # Startup
    logger.info("Starting DeepSearch API...")
    
    # Load configuration
    config = load_config()
    
    # Initialize search engine
    search_engine = DeepSearchEngine(config)
    
    logger.info("DeepSearch API started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down DeepSearch API...")
    if search_engine:
        # Cleanup if needed
        pass
    logger.info("DeepSearch API shutdown complete")


# Create FastAPI app
app = FastAPI(
    title="DeepSearch News Agent API",
    description="Specialized news search engine for AI agents - Get latest news with summaries and sources",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Include news routes
app.include_router(news_router)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for web interface
import os
web_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "web")
if os.path.exists(web_dir):
    app.mount("/web", StaticFiles(directory=web_dir), name="web")


async def get_search_engine() -> DeepSearchEngine:
    """Dependency to get search engine instance"""
    if search_engine is None:
        raise HTTPException(status_code=503, detail="Search engine not initialized")
    return search_engine


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal server error",
            detail=str(exc),
            timestamp=datetime.now().isoformat()
        ).dict()
    )


@app.get("/", response_model=SuccessResponse)
async def root():
    """Root endpoint"""
    return SuccessResponse(
        success=True,
        message="DeepSearch Agent API is running",
        data={
            "version": "1.0.0",
            "docs": "/docs",
            "health": "/health"
        }
    )


@app.post("/search", response_model=SearchResponse)
async def search(
    request: SearchRequest,
    engine: DeepSearchEngine = Depends(get_search_engine)
):
    """
    Perform comprehensive search
    
    This endpoint searches the web, scrapes articles, processes them with AI,
    and returns comprehensive results with summaries and analysis.
    """
    try:
        async with engine:
            result = await engine.search(
                query=request.query,
                max_results=request.max_results,
                engines=request.engines,
                summarize=request.summarize,
                use_cache=request.use_cache,
                enhance_query=request.enhance_query
            )
        
        return SearchResponse(**result)
        
    except Exception as e:
        logger.error(f"Search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/rag", response_model=RAGResponse)
async def rag_search(
    request: RAGRequest,
    engine: DeepSearchEngine = Depends(get_search_engine)
):
    """
    Perform RAG (Retrieval-Augmented Generation) search
    
    This endpoint performs semantic search on available articles and generates
    an AI-powered answer based on the most relevant content.
    """
    try:
        async with engine:
            result = await engine.rag_search(
                query=request.query,
                max_context_articles=request.max_context_articles,
                use_recent_articles=request.use_recent_articles
            )
        
        return RAGResponse(**result)
        
    except Exception as e:
        logger.error(f"RAG search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/trending", response_model=TrendingTopicsResponse)
async def get_trending_topics(
    limit: int = 10,
    engine: DeepSearchEngine = Depends(get_search_engine)
):
    """
    Get trending topics based on recent articles
    
    Analyzes recent articles to identify trending topics and keywords.
    """
    try:
        async with engine:
            topics = await engine.get_trending_topics(limit=limit)
        
        return TrendingTopicsResponse(
            topics=topics,
            total_topics=len(topics),
            generated_at=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Failed to get trending topics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health", response_model=HealthCheckResponse)
async def health_check(engine: DeepSearchEngine = Depends(get_search_engine)):
    """
    Perform health check on all system components
    
    Checks the status of scrapers, AI components, and database.
    """
    try:
        async with engine:
            health_status = await engine.health_check()
        
        return HealthCheckResponse(**health_status)
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/stats", response_model=DatabaseStatsResponse)
async def get_database_stats(engine: DeepSearchEngine = Depends(get_search_engine)):
    """
    Get database statistics
    
    Returns statistics about stored articles, queries, and other data.
    """
    try:
        stats = await engine.database_manager.get_database_stats()
        return DatabaseStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"Failed to get database stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/cache/clear", response_model=SuccessResponse)
async def clear_cache(engine: DeepSearchEngine = Depends(get_search_engine)):
    """
    Clear expired cache entries
    
    Removes expired cache entries from the database.
    """
    try:
        await engine.database_manager.cleanup_expired_cache()
        return SuccessResponse(
            success=True,
            message="Cache cleared successfully"
        )
        
    except Exception as e:
        logger.error(f"Failed to clear cache: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/engines", response_model=SuccessResponse)
async def get_available_engines(engine: DeepSearchEngine = Depends(get_search_engine)):
    """
    Get list of available search engines

    Returns the list of search engines that can be used for scraping.
    """
    try:
        engines = engine.scraper_manager.get_available_engines()
        return SuccessResponse(
            success=True,
            message="Available engines retrieved",
            data={"engines": engines}
        )

    except Exception as e:
        logger.error(f"Failed to get engines: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/ai/providers", response_model=SuccessResponse)
async def get_ai_providers(engine: DeepSearchEngine = Depends(get_search_engine)):
    """
    Get information about AI providers

    Returns information about available AI providers and their status.
    """
    try:
        async with engine:
            ai_manager = engine.ai_manager

            provider_info = {
                "primary_provider": None,
                "fallback_provider": None,
                "auto_switch": ai_manager.auto_switch
            }

            if ai_manager.provider:
                if hasattr(ai_manager.provider, 'get_model_info'):
                    provider_info["primary_provider"] = ai_manager.provider.get_model_info()
                else:
                    provider_info["primary_provider"] = {"provider": "unknown", "status": "available"}

            if ai_manager.fallback_provider:
                if hasattr(ai_manager.fallback_provider, 'get_model_info'):
                    provider_info["fallback_provider"] = ai_manager.fallback_provider.get_model_info()
                else:
                    provider_info["fallback_provider"] = {"provider": "unknown", "status": "available"}

            return SuccessResponse(
                success=True,
                message="AI providers information retrieved",
                data=provider_info
            )

    except Exception as e:
        logger.error(f"Failed to get AI providers: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/ai/switch", response_model=SuccessResponse)
async def switch_ai_provider(
    provider: str,
    engine: DeepSearchEngine = Depends(get_search_engine)
):
    """
    Switch AI provider

    Switches the primary AI provider to the specified one.
    """
    try:
        async with engine:
            ai_manager = engine.ai_manager

            # Create new provider
            new_provider = ai_manager._create_provider(provider)
            if not new_provider:
                raise HTTPException(status_code=400, detail=f"Unsupported provider: {provider}")

            # Switch providers
            old_provider = ai_manager.provider
            ai_manager.provider = new_provider

            # Test new provider
            if hasattr(new_provider, 'health_check'):
                health = await new_provider.health_check()
                if not health:
                    # Revert if health check fails
                    ai_manager.provider = old_provider
                    raise HTTPException(status_code=503, detail=f"Provider {provider} is not healthy")

            return SuccessResponse(
                success=True,
                message=f"Successfully switched to {provider} provider",
                data={"new_provider": provider}
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to switch AI provider: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    
    # Load configuration
    config = load_config()
    api_config = config.get('api', {})
    
    # Run the application
    uvicorn.run(
        "src.api.main:app",
        host=api_config.get('host', '0.0.0.0'),
        port=api_config.get('port', 8000),
        workers=api_config.get('workers', 1),
        reload=api_config.get('debug', False)
    )
