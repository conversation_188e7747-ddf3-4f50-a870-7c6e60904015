"""
Configuration loader utility
"""

import os
import yaml
from typing import Dict, Any
from pathlib import Path
from loguru import logger


def load_config(config_path: str = None) -> Dict[str, Any]:
    """
    Load configuration from YAML file and environment variables
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        Configuration dictionary
    """
    # Default configuration
    default_config = {
        "database": {
            "url": "sqlite:///data/deepsearch.db",
            "vector_db_path": "data/vector_db",
            "backup_enabled": True,
            "backup_interval": 3600
        },
        "search": {
            "max_results": 20,
            "default_language": "ar",
            "timeout": 30,
            "engines": ["brave", "duckduckgo"]
        },
        "scraping": {
            "user_agent": "DeepSearch-Agent/1.0",
            "request_delay": 1,
            "concurrent_requests": 5,
            "download_timeout": 30,
            "retry_attempts": 3,
            "respect_robots_txt": True
        },
        "ai": {
            "provider": "ollama",
            "model": "mistral:latest",
            "max_content_length": 10000,
            "summary_max_length": 500,
            "embedding_model": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
            "temperature": 0.7,
            "max_tokens": 2000
        },
        "api": {
            "host": "0.0.0.0",
            "port": 8000,
            "workers": 1,
            "debug": True,
            "cors_enabled": True,
            "rate_limit": 100
        },
        "logging": {
            "level": "INFO",
            "file": "logs/deepsearch.log",
            "max_size": "10MB",
            "backup_count": 5,
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        },
        "cache": {
            "enabled": True,
            "ttl": 3600,
            "max_size": 1000
        }
    }
    
    # Determine config file path
    if config_path is None:
        # Try different locations
        possible_paths = [
            "config/config.yaml",
            "config.yaml",
            os.path.expanduser("~/.deepsearch/config.yaml"),
            "/etc/deepsearch/config.yaml"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                config_path = path
                break
    
    # Load from YAML file if exists
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                file_config = yaml.safe_load(f)
                if file_config:
                    # Merge with default config
                    config = merge_configs(default_config, file_config)
                    logger.info(f"Configuration loaded from: {config_path}")
                else:
                    config = default_config
                    logger.warning(f"Empty configuration file: {config_path}")
        except Exception as e:
            logger.error(f"Failed to load configuration from {config_path}: {str(e)}")
            config = default_config
    else:
        config = default_config
        logger.info("Using default configuration")
    
    # Override with environment variables
    config = apply_env_overrides(config)
    
    # Ensure required directories exist
    ensure_directories(config)
    
    return config


def merge_configs(default: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
    """
    Recursively merge configuration dictionaries
    
    Args:
        default: Default configuration
        override: Override configuration
        
    Returns:
        Merged configuration
    """
    result = default.copy()
    
    for key, value in override.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = merge_configs(result[key], value)
        else:
            result[key] = value
    
    return result


def apply_env_overrides(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Apply environment variable overrides to configuration
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Configuration with environment overrides
    """
    # Database overrides
    if os.getenv('DATABASE_URL'):
        config['database']['url'] = os.getenv('DATABASE_URL')
    
    if os.getenv('VECTOR_DB_PATH'):
        config['database']['vector_db_path'] = os.getenv('VECTOR_DB_PATH')
    
    # API overrides
    if os.getenv('API_HOST'):
        config['api']['host'] = os.getenv('API_HOST')
    
    if os.getenv('API_PORT'):
        try:
            config['api']['port'] = int(os.getenv('API_PORT'))
        except ValueError:
            logger.warning("Invalid API_PORT environment variable")
    
    if os.getenv('API_WORKERS'):
        try:
            config['api']['workers'] = int(os.getenv('API_WORKERS'))
        except ValueError:
            logger.warning("Invalid API_WORKERS environment variable")
    
    if os.getenv('DEBUG'):
        config['api']['debug'] = os.getenv('DEBUG').lower() in ('true', '1', 'yes')
    
    # AI overrides
    if os.getenv('OLLAMA_BASE_URL'):
        config['ai']['ollama_base_url'] = os.getenv('OLLAMA_BASE_URL')
    
    if os.getenv('OLLAMA_MODEL'):
        config['ai']['model'] = os.getenv('OLLAMA_MODEL')
    
    if os.getenv('EMBEDDING_MODEL'):
        config['ai']['embedding_model'] = os.getenv('EMBEDDING_MODEL')
    
    # Search overrides
    if os.getenv('MAX_SEARCH_RESULTS'):
        try:
            config['search']['max_results'] = int(os.getenv('MAX_SEARCH_RESULTS'))
        except ValueError:
            logger.warning("Invalid MAX_SEARCH_RESULTS environment variable")
    
    if os.getenv('DEFAULT_LANGUAGE'):
        config['search']['default_language'] = os.getenv('DEFAULT_LANGUAGE')
    
    # Scraping overrides
    if os.getenv('USER_AGENT'):
        config['scraping']['user_agent'] = os.getenv('USER_AGENT')
    
    if os.getenv('REQUEST_DELAY'):
        try:
            config['scraping']['request_delay'] = float(os.getenv('REQUEST_DELAY'))
        except ValueError:
            logger.warning("Invalid REQUEST_DELAY environment variable")
    
    if os.getenv('CONCURRENT_REQUESTS'):
        try:
            config['scraping']['concurrent_requests'] = int(os.getenv('CONCURRENT_REQUESTS'))
        except ValueError:
            logger.warning("Invalid CONCURRENT_REQUESTS environment variable")
    
    # Logging overrides
    if os.getenv('LOG_LEVEL'):
        config['logging']['level'] = os.getenv('LOG_LEVEL')
    
    if os.getenv('LOG_FILE'):
        config['logging']['file'] = os.getenv('LOG_FILE')
    
    return config


def ensure_directories(config: Dict[str, Any]):
    """
    Ensure required directories exist
    
    Args:
        config: Configuration dictionary
    """
    directories_to_create = []
    
    # Database directory
    db_url = config.get('database', {}).get('url', '')
    if 'sqlite' in db_url:
        db_path = db_url.replace('sqlite:///', '')
        db_dir = os.path.dirname(db_path)
        if db_dir:
            directories_to_create.append(db_dir)
    
    # Vector database directory
    vector_db_path = config.get('database', {}).get('vector_db_path', '')
    if vector_db_path:
        directories_to_create.append(os.path.dirname(vector_db_path))
    
    # Log directory
    log_file = config.get('logging', {}).get('file', '')
    if log_file:
        log_dir = os.path.dirname(log_file)
        if log_dir:
            directories_to_create.append(log_dir)
    
    # Create directories
    for directory in directories_to_create:
        if directory:
            try:
                os.makedirs(directory, exist_ok=True)
                logger.debug(f"Ensured directory exists: {directory}")
            except Exception as e:
                logger.error(f"Failed to create directory {directory}: {str(e)}")


def get_env_file_path() -> str:
    """
    Get the path to the .env file
    
    Returns:
        Path to .env file
    """
    possible_paths = [
        ".env",
        os.path.expanduser("~/.deepsearch/.env"),
        "/etc/deepsearch/.env"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return ".env"  # Default


def load_env_file():
    """Load environment variables from .env file"""
    try:
        from dotenv import load_dotenv
        env_path = get_env_file_path()
        if os.path.exists(env_path):
            load_dotenv(env_path)
            logger.info(f"Environment variables loaded from: {env_path}")
        else:
            logger.info("No .env file found, using system environment variables")
    except ImportError:
        logger.warning("python-dotenv not installed, skipping .env file loading")
    except Exception as e:
        logger.error(f"Failed to load .env file: {str(e)}")


# Load environment variables when module is imported
load_env_file()
