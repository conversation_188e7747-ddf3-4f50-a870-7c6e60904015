"""
Enhanced search APIs including Brave Search and Tavily
"""

import asyncio
import httpx
from typing import List, Dict, Any, Optional
from loguru import logger
import json
from datetime import datetime


class BraveSearchAPI:
    """Enhanced Brave Search API client"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key
        self.base_url = "https://api.search.brave.com/res/v1"
        self.is_configured = bool(api_key)
        
    def configure(self, api_key: str) -> Dict[str, Any]:
        """Configure Brave Search API"""
        try:
            self.api_key = api_key
            self.is_configured = True
            
            logger.info("Brave Search API configured successfully")
            return {
                "success": True,
                "message": "Brave Search API configured successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to configure Brave Search API: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def search(
        self, 
        query: str, 
        count: int = 10,
        country: str = "ALL",
        search_lang: str = "ar",
        ui_lang: str = "ar-SA"
    ) -> Dict[str, Any]:
        """
        Search using Brave Search API
        
        Args:
            query: Search query
            count: Number of results
            country: Country code for localized results
            search_lang: Search language
            ui_lang: UI language
            
        Returns:
            Search results
        """
        if not self.is_configured:
            return {
                "success": False,
                "error": "Brave Search API not configured"
            }
        
        try:
            headers = {
                "Accept": "application/json",
                "Accept-Encoding": "gzip",
                "X-Subscription-Token": self.api_key
            }
            
            params = {
                "q": query,
                "count": count,
                "country": country,
                "search_lang": search_lang,
                "ui_lang": ui_lang,
                "result_filter": "web",
                "freshness": "all"
            }
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{self.base_url}/web/search",
                    headers=headers,
                    params=params
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Format results
                    formatted_results = []
                    web_results = data.get("web", {}).get("results", [])
                    
                    for result in web_results:
                        formatted_results.append({
                            "title": result.get("title", ""),
                            "url": result.get("url", ""),
                            "description": result.get("description", ""),
                            "content": result.get("description", ""),
                            "published_date": result.get("age", ""),
                            "source": "brave_search",
                            "language": search_lang,
                            "meta": {
                                "profile": result.get("profile", {}),
                                "family_friendly": result.get("family_friendly", True)
                            }
                        })
                    
                    logger.info(f"Brave Search returned {len(formatted_results)} results")
                    
                    return {
                        "success": True,
                        "results": formatted_results,
                        "total_results": len(formatted_results),
                        "query": query,
                        "source": "brave_search"
                    }
                else:
                    error_msg = f"Brave Search API error: {response.status_code}"
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "error": error_msg
                    }
                    
        except Exception as e:
            logger.error(f"Brave Search failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Get Brave Search API status"""
        return {
            "configured": self.is_configured,
            "api_key_set": bool(self.api_key),
            "service": "brave_search"
        }


class TavilySearchAPI:
    """Tavily Search API client for AI agents"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key
        self.base_url = "https://api.tavily.com"
        self.is_configured = bool(api_key)
        
    def configure(self, api_key: str) -> Dict[str, Any]:
        """Configure Tavily Search API"""
        try:
            self.api_key = api_key
            self.is_configured = True
            
            logger.info("Tavily Search API configured successfully")
            return {
                "success": True,
                "message": "Tavily Search API configured successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to configure Tavily Search API: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def search(
        self, 
        query: str, 
        max_results: int = 10,
        search_depth: str = "advanced",
        include_domains: List[str] = None,
        exclude_domains: List[str] = None
    ) -> Dict[str, Any]:
        """
        Search using Tavily API
        
        Args:
            query: Search query
            max_results: Maximum number of results
            search_depth: Search depth (basic, advanced)
            include_domains: Domains to include
            exclude_domains: Domains to exclude
            
        Returns:
            Search results with content
        """
        if not self.is_configured:
            return {
                "success": False,
                "error": "Tavily Search API not configured"
            }
        
        try:
            payload = {
                "api_key": self.api_key,
                "query": query,
                "max_results": max_results,
                "search_depth": search_depth,
                "include_answer": True,
                "include_raw_content": True,
                "include_images": False
            }
            
            if include_domains:
                payload["include_domains"] = include_domains
            
            if exclude_domains:
                payload["exclude_domains"] = exclude_domains
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.base_url}/search",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Format results
                    formatted_results = []
                    results = data.get("results", [])
                    
                    for result in results:
                        formatted_results.append({
                            "title": result.get("title", ""),
                            "url": result.get("url", ""),
                            "description": result.get("content", ""),
                            "content": result.get("raw_content", result.get("content", "")),
                            "published_date": result.get("published_date", ""),
                            "source": "tavily_search",
                            "score": result.get("score", 0),
                            "meta": {
                                "raw_content_length": len(result.get("raw_content", "")),
                                "content_length": len(result.get("content", ""))
                            }
                        })
                    
                    logger.info(f"Tavily Search returned {len(formatted_results)} results")
                    
                    return {
                        "success": True,
                        "results": formatted_results,
                        "total_results": len(formatted_results),
                        "query": query,
                        "answer": data.get("answer", ""),
                        "source": "tavily_search"
                    }
                else:
                    error_msg = f"Tavily Search API error: {response.status_code}"
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "error": error_msg
                    }
                    
        except Exception as e:
            logger.error(f"Tavily Search failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Get Tavily Search API status"""
        return {
            "configured": self.is_configured,
            "api_key_set": bool(self.api_key),
            "service": "tavily_search"
        }


class EnhancedSearchManager:
    """Manager for all enhanced search APIs"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.search_config = config.get('enhanced_search', {})
        
        # Initialize APIs
        self.brave_api = BraveSearchAPI()
        self.tavily_api = TavilySearchAPI()
        
        # Load API keys from config if available
        brave_key = self.search_config.get('brave_api_key')
        if brave_key:
            self.brave_api.configure(brave_key)
        
        tavily_key = self.search_config.get('tavily_api_key')
        if tavily_key:
            self.tavily_api.configure(tavily_key)
    
    def configure_brave(self, api_key: str) -> Dict[str, Any]:
        """Configure Brave Search API"""
        return self.brave_api.configure(api_key)
    
    def configure_tavily(self, api_key: str) -> Dict[str, Any]:
        """Configure Tavily Search API"""
        return self.tavily_api.configure(api_key)
    
    async def search_all_enhanced(
        self, 
        query: str, 
        max_results: int = 10
    ) -> Dict[str, Any]:
        """Search using all available enhanced APIs"""
        try:
            results = {
                "query": query,
                "timestamp": datetime.now().isoformat(),
                "sources": {},
                "combined_results": [],
                "total_results": 0
            }
            
            # Search with available APIs
            tasks = []
            
            if self.brave_api.is_configured:
                tasks.append(("brave", self.brave_api.search(query, max_results)))
            
            if self.tavily_api.is_configured:
                tasks.append(("tavily", self.tavily_api.search(query, max_results)))
            
            if not tasks:
                return {
                    "success": False,
                    "error": "No enhanced search APIs configured"
                }
            
            # Execute searches in parallel
            search_results = await asyncio.gather(
                *[task[1] for task in tasks], 
                return_exceptions=True
            )
            
            # Process results
            for i, (source_name, result) in enumerate(zip([task[0] for task in tasks], search_results)):
                if isinstance(result, Exception):
                    logger.error(f"{source_name} search failed: {str(result)}")
                    results["sources"][source_name] = {
                        "success": False,
                        "error": str(result)
                    }
                elif isinstance(result, dict) and result.get("success"):
                    results["sources"][source_name] = result
                    results["combined_results"].extend(result.get("results", []))
                else:
                    results["sources"][source_name] = result
            
            # Remove duplicates and sort by relevance
            unique_results = self._deduplicate_results(results["combined_results"])
            results["combined_results"] = unique_results[:max_results]
            results["total_results"] = len(results["combined_results"])
            results["success"] = True
            
            logger.info(f"Enhanced search returned {results['total_results']} unique results")
            return results
            
        except Exception as e:
            logger.error(f"Enhanced search failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate results based on URL"""
        seen_urls = set()
        unique_results = []
        
        for result in results:
            url = result.get("url", "")
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)
        
        return unique_results
    
    def get_status(self) -> Dict[str, Any]:
        """Get status of all enhanced search APIs"""
        return {
            "brave_search": self.brave_api.get_status(),
            "tavily_search": self.tavily_api.get_status(),
            "total_configured": sum([
                self.brave_api.is_configured,
                self.tavily_api.is_configured
            ])
        }
