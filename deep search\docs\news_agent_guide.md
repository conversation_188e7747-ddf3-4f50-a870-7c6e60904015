# دليل استخدام DeepSearch News Agent للوكلاء البرمجيين

## 📰 نظرة عامة

DeepSearch News Agent هو محرك أخبار ذكي متخصص مصمم خصيصاً للوكلاء البرمجيين. يوفر آخر الأخبار مع ملخصات ذكية ومصادر موثوقة.

## 🎯 الميزات الرئيسية

### ✅ بحث إخباري متخصص
- البحث عن آخر الأخبار (آخر 24 ساعة)
- تصنيف الأخبار حسب الفئات
- تقييم مصداقية المصادر
- استخراج التواريخ والكيانات

### ✅ ملخصات ذكية
- ملخصات إخبارية مركزة
- استخراج المعلومات الرئيسية (من، ماذا، متى، أين)
- تحليل نوع الخبر (عاجل، تحليل، مقابلة)

### ✅ مصادر موثوقة
- المقال الكامل مع المصدر
- تقييم جودة المصدر
- روابط مباشرة للمقالات الأصلية

## 🔗 نقاط النهاية (API Endpoints)

### 1. البحث العام في الأخبار
```http
POST /news/search
Content-Type: application/json

{
  "query": "آخر أخبار كرة القدم",
  "max_results": 10,
  "category": "sports",
  "hours_back": 24
}
```

### 2. أخبار كرة القدم المتخصصة
```http
POST /news/football
Content-Type: application/json

{
  "max_results": 10
}
```

### 3. أخبار العملات الرقمية
```http
POST /news/crypto
Content-Type: application/json

{
  "max_results": 10
}
```

### 4. أخبار التكنولوجيا
```http
POST /news/technology
Content-Type: application/json

{
  "max_results": 10
}
```

### 5. أخبار الحروب والصراعات
```http
POST /news/war
Content-Type: application/json

{
  "max_results": 10
}
```

### 6. الأخبار الرائجة
```http
GET /news/trending?limit=10
```

### 7. فئات الأخبار المتاحة
```http
GET /news/categories
```

## 📋 نموذج الاستجابة

```json
{
  "query": "آخر أخبار كرة القدم",
  "enhanced_query": "آخر أخبار كرة القدم اليوم 2024 عاجل",
  "news_articles": [
    {
      "title": "عنوان الخبر",
      "url": "https://example.com/news/1",
      "news_summary": "ملخص الخبر الذكي",
      "content": "المحتوى الكامل للخبر...",
      "source_engine": "duckduckgo",
      "scraped_at": "2024-01-01T12:00:00",
      "news_score": 4.5,
      "extracted_date": "اليوم",
      "key_entities": ["فريق برشلونة", "ليونيل ميسي"],
      "news_type": "breaking",
      "has_news_summary": true
    }
  ],
  "total_results": 10,
  "category": "sports",
  "search_time": "2024-01-01T12:00:00",
  "processing_time": 2.5,
  "sources_used": ["duckduckgo"]
}
```

## 🤖 أمثلة للوكلاء البرمجيين

### Python Example
```python
import requests
import json

# إعداد API
API_BASE = "http://localhost:8000"

class NewsAgent:
    def __init__(self):
        self.base_url = API_BASE
    
    def get_football_news(self, max_results=10):
        """الحصول على آخر أخبار كرة القدم"""
        response = requests.post(
            f"{self.base_url}/news/football",
            json={"max_results": max_results}
        )
        return response.json()
    
    def get_crypto_news(self, max_results=10):
        """الحصول على آخر أخبار العملات الرقمية"""
        response = requests.post(
            f"{self.base_url}/news/crypto",
            json={"max_results": max_results}
        )
        return response.json()
    
    def search_news(self, query, category=None, max_results=10):
        """البحث في الأخبار"""
        payload = {
            "query": query,
            "max_results": max_results
        }
        if category:
            payload["category"] = category
            
        response = requests.post(
            f"{self.base_url}/news/search",
            json=payload
        )
        return response.json()
    
    def get_news_summary(self, news_data):
        """استخراج ملخص من البيانات"""
        summaries = []
        for article in news_data.get('news_articles', []):
            summaries.append({
                'title': article['title'],
                'summary': article['news_summary'],
                'url': article['url'],
                'date': article.get('extracted_date', 'غير محدد')
            })
        return summaries

# الاستخدام
agent = NewsAgent()

# أخبار كرة القدم
football_news = agent.get_football_news(5)
print(f"وجدت {football_news['total_results']} خبر كرة قدم")

# أخبار البيتكوين
crypto_news = agent.get_crypto_news(5)
print(f"وجدت {crypto_news['total_results']} خبر عملات رقمية")

# بحث مخصص
tech_news = agent.search_news("آخر أخبار الذكاء الاصطناعي", "technology")
summaries = agent.get_news_summary(tech_news)
```

### JavaScript Example
```javascript
class NewsAgent {
    constructor(baseUrl = 'http://localhost:8000') {
        this.baseUrl = baseUrl;
    }
    
    async getFootballNews(maxResults = 10) {
        const response = await fetch(`${this.baseUrl}/news/football`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ max_results: maxResults })
        });
        return await response.json();
    }
    
    async getCryptoNews(maxResults = 10) {
        const response = await fetch(`${this.baseUrl}/news/crypto`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ max_results: maxResults })
        });
        return await response.json();
    }
    
    async searchNews(query, category = null, maxResults = 10) {
        const payload = { query, max_results: maxResults };
        if (category) payload.category = category;
        
        const response = await fetch(`${this.baseUrl}/news/search`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });
        return await response.json();
    }
}

// الاستخدام
const agent = new NewsAgent();

// أخبار كرة القدم
agent.getFootballNews(5).then(news => {
    console.log(`وجدت ${news.total_results} خبر كرة قدم`);
    news.news_articles.forEach(article => {
        console.log(`- ${article.title}`);
        console.log(`  الملخص: ${article.news_summary}`);
    });
});
```

## 🎯 حالات الاستخدام

### 1. وكيل أخبار رياضية
```python
# الحصول على آخر أخبار كرة القدم
football_news = agent.get_football_news(10)

# تحليل النتائج
for article in football_news['news_articles']:
    if article['news_type'] == 'breaking':
        print(f"خبر عاجل: {article['title']}")
    
    # استخراج الفرق المذكورة
    teams = [entity for entity in article['key_entities'] 
             if 'فريق' in entity]
```

### 2. وكيل أخبار مالية
```python
# أخبار العملات الرقمية
crypto_news = agent.get_crypto_news(10)

# البحث عن أخبار بيتكوين محددة
bitcoin_news = agent.search_news("بيتكوين سعر", "crypto")

# تحليل الاتجاهات
for article in bitcoin_news['news_articles']:
    if any(word in article['content'].lower() 
           for word in ['ارتفاع', 'صعود', 'زيادة']):
        print(f"اتجاه إيجابي: {article['title']}")
```

### 3. وكيل أخبار تقنية
```python
# أخبار التكنولوجيا
tech_news = agent.get_technology_news(10)

# البحث عن أخبار الذكاء الاصطناعي
ai_news = agent.search_news("ذكاء اصطناعي", "technology")

# تصفية الأخبار المهمة
important_news = [
    article for article in ai_news['news_articles']
    if article['news_score'] > 3.0
]
```

## ⚙️ التكوين

### إعدادات الأخبار في config.yaml
```yaml
news:
  max_age_hours: 24  # عمر الأخبار بالساعات
  preferred_sources: 
    - "bbc.com"
    - "cnn.com"
    - "aljazeera.net"
  enable_date_extraction: true
  enable_entity_extraction: true
  news_score_threshold: 1.0
```

## 🚀 التشغيل

```bash
# تشغيل الخادم
python main.py --api

# الوصول للواجهة الإخبارية
http://localhost:8000/web/news.html

# وثائق API
http://localhost:8000/docs
```

## 📊 مراقبة الأداء

```python
# فحص صحة النظام
response = requests.get(f"{API_BASE}/health")
health = response.json()

if health['overall_status'] == 'healthy':
    print("النظام يعمل بشكل طبيعي")
else:
    print("هناك مشاكل في النظام")
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:
1. **لا توجد أخبار**: تحقق من الاتصال بالإنترنت
2. **بطء في الاستجابة**: قلل عدد النتائج المطلوبة
3. **أخطاء API**: تحقق من مفاتيح API في .env

### نصائح للأداء الأمثل:
- استخدم فئات محددة للبحث
- حدد عدد النتائج حسب الحاجة
- استفد من التخزين المؤقت
- راقب معدل الطلبات
