"""
Advanced keyword extraction using multiple state-of-the-art algorithms
"""

import asyncio
import re
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
import numpy as np
from collections import Counter


class AdvancedKeywordExtractor:
    """Advanced keyword extractor using multiple algorithms"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.keyword_config = config.get('keyword_extraction', {})
        
        # Initialize extractors
        self.keybert_extractor = None
        self.yake_extractor = None
        self.textrank_extractor = None
        self.spacy_nlp = None
        
        # Configuration
        self.max_keywords = self.keyword_config.get('max_keywords', 15)
        self.min_keyword_length = self.keyword_config.get('min_keyword_length', 3)
        self.use_ensemble = self.keyword_config.get('use_ensemble', True)
        
        # Language support
        self.supported_languages = {
            'ar': 'arabic',
            'en': 'english',
            'fr': 'french',
            'es': 'spanish',
            'de': 'german'
        }
        
        self._initialize_extractors()
    
    def _initialize_extractors(self):
        """Initialize all keyword extraction algorithms"""
        try:
            # Initialize KeyBERT
            try:
                from keybert import KeyBERT
                self.keybert_extractor = KeyBERT()
                logger.info("KeyBERT initialized successfully")
            except ImportError:
                logger.warning("KeyBERT not available - install with: pip install keybert")
            
            # Initialize YAKE
            try:
                import yake
                self.yake_extractor = yake
                logger.info("YAKE initialized successfully")
            except ImportError:
                logger.warning("YAKE not available - install with: pip install yake")
            
            # Initialize TextRank
            try:
                import pytextrank
                import spacy
                
                # Try to load Arabic model first, fallback to English
                try:
                    self.spacy_nlp = spacy.load("ar_core_news_sm")
                except OSError:
                    try:
                        self.spacy_nlp = spacy.load("en_core_web_sm")
                    except OSError:
                        logger.warning("No spaCy models found. Install with: python -m spacy download en_core_web_sm")
                        self.spacy_nlp = None
                
                if self.spacy_nlp:
                    self.spacy_nlp.add_pipe("textrank")
                    logger.info("TextRank initialized successfully")
                    
            except ImportError:
                logger.warning("TextRank/spaCy not available - install with: pip install pytextrank spacy")
            
        except Exception as e:
            logger.error(f"Failed to initialize keyword extractors: {str(e)}")
    
    async def extract_keywords_keybert(
        self, 
        text: str, 
        language: str = 'ar',
        top_k: int = 10
    ) -> List[Dict[str, Any]]:
        """Extract keywords using KeyBERT"""
        if not self.keybert_extractor:
            return []
        
        try:
            # Run in thread pool since KeyBERT is synchronous
            loop = asyncio.get_event_loop()
            
            # Extract keywords with different n-gram ranges
            keywords_1gram = await loop.run_in_executor(
                None,
                lambda: self.keybert_extractor.extract_keywords(
                    text, 
                    keyphrase_ngram_range=(1, 1), 
                    stop_words=self.supported_languages.get(language, 'english'),
                    top_k=top_k//2
                )
            )
            
            keywords_2gram = await loop.run_in_executor(
                None,
                lambda: self.keybert_extractor.extract_keywords(
                    text, 
                    keyphrase_ngram_range=(2, 2), 
                    stop_words=self.supported_languages.get(language, 'english'),
                    top_k=top_k//2
                )
            )
            
            # Combine and format results
            all_keywords = keywords_1gram + keywords_2gram
            
            formatted_keywords = []
            for keyword, score in all_keywords:
                if len(keyword) >= self.min_keyword_length:
                    formatted_keywords.append({
                        'keyword': keyword,
                        'score': float(score),
                        'algorithm': 'keybert',
                        'type': 'semantic'
                    })
            
            # Sort by score
            formatted_keywords.sort(key=lambda x: x['score'], reverse=True)
            
            logger.info(f"KeyBERT extracted {len(formatted_keywords)} keywords")
            return formatted_keywords[:top_k]
            
        except Exception as e:
            logger.error(f"KeyBERT extraction failed: {str(e)}")
            return []
    
    async def extract_keywords_yake(
        self, 
        text: str, 
        language: str = 'ar',
        top_k: int = 10
    ) -> List[Dict[str, Any]]:
        """Extract keywords using YAKE"""
        if not self.yake_extractor:
            return []
        
        try:
            # Map language codes
            yake_language = self.supported_languages.get(language, 'en')
            
            # Configure YAKE
            kw_extractor = self.yake_extractor.KeywordExtractor(
                lan=yake_language,
                n=3,  # n-gram size
                dedupLim=0.7,  # deduplication threshold
                top=top_k,
                features=None
            )
            
            # Run in thread pool
            loop = asyncio.get_event_loop()
            keywords = await loop.run_in_executor(
                None,
                kw_extractor.extract_keywords,
                text
            )
            
            formatted_keywords = []
            for score, keyword in keywords:
                if len(keyword) >= self.min_keyword_length:
                    formatted_keywords.append({
                        'keyword': keyword,
                        'score': 1.0 - float(score),  # YAKE returns lower scores for better keywords
                        'algorithm': 'yake',
                        'type': 'statistical'
                    })
            
            logger.info(f"YAKE extracted {len(formatted_keywords)} keywords")
            return formatted_keywords
            
        except Exception as e:
            logger.error(f"YAKE extraction failed: {str(e)}")
            return []
    
    async def extract_keywords_textrank(
        self, 
        text: str, 
        top_k: int = 10
    ) -> List[Dict[str, Any]]:
        """Extract keywords using TextRank"""
        if not self.spacy_nlp:
            return []
        
        try:
            # Run in thread pool
            loop = asyncio.get_event_loop()
            
            def _extract_textrank():
                doc = self.spacy_nlp(text)
                keywords = []
                
                for phrase in doc._.phrases[:top_k]:
                    keywords.append({
                        'keyword': phrase.text,
                        'score': float(phrase.rank),
                        'algorithm': 'textrank',
                        'type': 'graph_based'
                    })
                
                return keywords
            
            keywords = await loop.run_in_executor(None, _extract_textrank)
            
            # Filter by length
            filtered_keywords = [
                kw for kw in keywords 
                if len(kw['keyword']) >= self.min_keyword_length
            ]
            
            logger.info(f"TextRank extracted {len(filtered_keywords)} keywords")
            return filtered_keywords
            
        except Exception as e:
            logger.error(f"TextRank extraction failed: {str(e)}")
            return []
    
    async def extract_keywords_ensemble(
        self, 
        text: str, 
        language: str = 'ar',
        top_k: int = 15
    ) -> List[Dict[str, Any]]:
        """Extract keywords using ensemble of all available algorithms"""
        try:
            logger.info("Starting ensemble keyword extraction")
            
            # Run all extractors in parallel
            tasks = []
            
            if self.keybert_extractor:
                tasks.append(self.extract_keywords_keybert(text, language, top_k))
            
            if self.yake_extractor:
                tasks.append(self.extract_keywords_yake(text, language, top_k))
            
            if self.spacy_nlp:
                tasks.append(self.extract_keywords_textrank(text, top_k))
            
            if not tasks:
                logger.warning("No keyword extractors available")
                return []
            
            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Combine results
            all_keywords = []
            for result in results:
                if isinstance(result, list):
                    all_keywords.extend(result)
                elif isinstance(result, Exception):
                    logger.error(f"Extractor failed: {str(result)}")
            
            # Ensemble scoring
            ensemble_keywords = self._ensemble_scoring(all_keywords)
            
            # Sort by ensemble score and return top results
            ensemble_keywords.sort(key=lambda x: x['ensemble_score'], reverse=True)
            
            logger.info(f"Ensemble extracted {len(ensemble_keywords)} unique keywords")
            return ensemble_keywords[:top_k]
            
        except Exception as e:
            logger.error(f"Ensemble extraction failed: {str(e)}")
            return []
    
    def _ensemble_scoring(self, keywords: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Combine scores from multiple algorithms using ensemble method"""
        try:
            # Group keywords by text (case-insensitive)
            keyword_groups = {}
            
            for kw in keywords:
                key = kw['keyword'].lower().strip()
                if key not in keyword_groups:
                    keyword_groups[key] = []
                keyword_groups[key].append(kw)
            
            ensemble_keywords = []
            
            for keyword_text, group in keyword_groups.items():
                if len(keyword_text) < self.min_keyword_length:
                    continue
                
                # Calculate ensemble score
                scores = [kw['score'] for kw in group]
                algorithms = [kw['algorithm'] for kw in group]
                types = [kw['type'] for kw in group]
                
                # Weighted average with bonus for multiple algorithms
                ensemble_score = np.mean(scores)
                algorithm_bonus = len(set(algorithms)) * 0.1  # Bonus for agreement
                final_score = min(1.0, ensemble_score + algorithm_bonus)
                
                ensemble_keywords.append({
                    'keyword': group[0]['keyword'],  # Use original case
                    'score': float(final_score),
                    'ensemble_score': float(final_score),
                    'algorithm_count': len(set(algorithms)),
                    'algorithms': list(set(algorithms)),
                    'types': list(set(types)),
                    'individual_scores': scores
                })
            
            return ensemble_keywords
            
        except Exception as e:
            logger.error(f"Ensemble scoring failed: {str(e)}")
            return []
    
    async def extract_keywords(
        self, 
        text: str, 
        language: str = 'ar',
        algorithm: str = 'ensemble',
        top_k: int = None
    ) -> List[Dict[str, Any]]:
        """
        Main keyword extraction method
        
        Args:
            text: Text to extract keywords from
            language: Language code (ar, en, fr, es, de)
            algorithm: Algorithm to use (ensemble, keybert, yake, textrank)
            top_k: Number of keywords to return
            
        Returns:
            List of keyword dictionaries with scores and metadata
        """
        if top_k is None:
            top_k = self.max_keywords
        
        try:
            if algorithm == 'ensemble' and self.use_ensemble:
                return await self.extract_keywords_ensemble(text, language, top_k)
            elif algorithm == 'keybert':
                return await self.extract_keywords_keybert(text, language, top_k)
            elif algorithm == 'yake':
                return await self.extract_keywords_yake(text, language, top_k)
            elif algorithm == 'textrank':
                return await self.extract_keywords_textrank(text, top_k)
            else:
                logger.warning(f"Unknown algorithm: {algorithm}, using ensemble")
                return await self.extract_keywords_ensemble(text, language, top_k)
                
        except Exception as e:
            logger.error(f"Keyword extraction failed: {str(e)}")
            return []
    
    def get_available_algorithms(self) -> List[str]:
        """Get list of available keyword extraction algorithms"""
        algorithms = []
        
        if self.keybert_extractor:
            algorithms.append('keybert')
        
        if self.yake_extractor:
            algorithms.append('yake')
        
        if self.spacy_nlp:
            algorithms.append('textrank')
        
        if len(algorithms) > 1:
            algorithms.append('ensemble')
        
        return algorithms
    
    def get_status(self) -> Dict[str, Any]:
        """Get status of keyword extraction system"""
        return {
            'available_algorithms': self.get_available_algorithms(),
            'keybert_available': bool(self.keybert_extractor),
            'yake_available': bool(self.yake_extractor),
            'textrank_available': bool(self.spacy_nlp),
            'ensemble_enabled': self.use_ensemble,
            'supported_languages': list(self.supported_languages.keys()),
            'max_keywords': self.max_keywords
        }
