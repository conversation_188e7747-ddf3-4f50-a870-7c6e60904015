"""
AI Manager to coordinate all AI operations
"""

import asyncio
import time
from typing import List, Dict, Any, Optional
from loguru import logger
from .ollama_provider import OllamaProvider
from .gemini_provider import GeminiProvider
from .mistral_provider import MistralProvider
# from .embedding_manager import EmbeddingManager  # Disabled for now


class AIManager:
    """Manages all AI operations including text generation, embeddings, and RAG"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.ai_config = config.get('ai', {})

        # Initialize AI providers
        self.provider = None
        self.fallback_provider = None
        self.auto_switch = self.ai_config.get('auto_switch', True)
        self._initialize_providers()

        # Initialize embedding manager (disabled for now)
        self.embedding_manager = None  # EmbeddingManager(self.ai_config)

        # Rate limiting and optimization settings
        self.rate_limit_enabled = self.ai_config.get('rate_limit_enabled', True)
        self.requests_per_minute = self.ai_config.get('requests_per_minute', 10)
        self.batch_processing = self.ai_config.get('batch_processing', True)
        self.skip_ai_on_short_content = self.ai_config.get('skip_ai_on_short_content', True)
        self.min_content_length = 200  # Minimum content length for AI processing

        # Rate limiting tracking
        self._request_times = []
        self._last_cleanup = time.time()
    
    def _initialize_providers(self):
        """Initialize the AI providers based on configuration"""
        provider_type = self.ai_config.get('provider', 'gemini')
        fallback_type = self.ai_config.get('fallback_provider', 'ollama')

        # Initialize primary provider
        self.provider = self._create_provider(provider_type)
        if self.provider:
            logger.info(f"Initialized primary AI provider: {provider_type}")

        # Initialize fallback provider if different from primary
        if fallback_type != provider_type:
            self.fallback_provider = self._create_provider(fallback_type)
            if self.fallback_provider:
                logger.info(f"Initialized fallback AI provider: {fallback_type}")

    def _create_provider(self, provider_type: str):
        """Create an AI provider instance"""
        if provider_type == 'ollama':
            return OllamaProvider(self.ai_config)
        elif provider_type == 'gemini':
            return GeminiProvider(self.ai_config)
        elif provider_type == 'mistral':
            return MistralProvider(self.ai_config)
        else:
            logger.error(f"Unsupported AI provider: {provider_type}")
            return None
    
    async def __aenter__(self):
        """Async context manager entry"""
        if self.provider and hasattr(self.provider, '__aenter__'):
            await self.provider.__aenter__()
        if self.fallback_provider and hasattr(self.fallback_provider, '__aenter__'):
            await self.fallback_provider.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.provider and hasattr(self.provider, '__aexit__'):
            await self.provider.__aexit__(exc_type, exc_val, exc_tb)
        if self.fallback_provider and hasattr(self.fallback_provider, '__aexit__'):
            await self.fallback_provider.__aexit__(exc_type, exc_val, exc_tb)

    def _check_rate_limit(self) -> bool:
        """Check if we're within rate limits"""
        if not self.rate_limit_enabled:
            return True

        current_time = time.time()

        # Clean old requests (older than 1 minute)
        if current_time - self._last_cleanup > 60:
            self._request_times = [t for t in self._request_times if current_time - t < 60]
            self._last_cleanup = current_time

        # Check if we're under the limit
        if len(self._request_times) >= self.requests_per_minute:
            logger.warning(f"Rate limit reached: {len(self._request_times)}/{self.requests_per_minute} requests per minute")
            return False

        return True

    def _record_request(self):
        """Record a new API request"""
        if self.rate_limit_enabled:
            self._request_times.append(time.time())

    async def _try_with_fallback(self, operation, *args, **kwargs):
        """Try operation with primary provider, fallback if fails"""
        if not self.provider:
            logger.error("No AI provider available")
            return None

        # Check rate limit
        if not self._check_rate_limit():
            logger.warning("Rate limit exceeded, skipping AI operation")
            return None

        try:
            # Record the request
            self._record_request()

            # Try primary provider
            result = await operation(self.provider, *args, **kwargs)
            if result:  # Check if result is not empty/None
                return result
            elif self.auto_switch and self.fallback_provider:
                logger.warning("Primary provider returned empty result, trying fallback")
                return await operation(self.fallback_provider, *args, **kwargs)
            else:
                return result
        except Exception as e:
            logger.error(f"Primary provider failed: {str(e)}")
            if self.auto_switch and self.fallback_provider:
                logger.info("Switching to fallback provider")
                try:
                    return await operation(self.fallback_provider, *args, **kwargs)
                except Exception as e2:
                    logger.error(f"Fallback provider also failed: {str(e2)}")
                    return None
            else:
                return None
    
    async def summarize_articles(
        self,
        articles: List[Dict[str, Any]],
        max_length: int = 300
    ) -> List[Dict[str, Any]]:
        """
        Summarize multiple articles with optimization

        Args:
            articles: List of articles to summarize
            max_length: Maximum summary length

        Returns:
            Articles with summaries added
        """
        async def _summarize_operation(provider, content, max_length):
            return await provider.summarize_text(content, max_length)

        summarized_articles = []
        articles_to_summarize = []

        # Filter articles that need summarization
        for article in articles:
            content = article.get('content', '')

            # Skip AI processing for short content if enabled
            if self.skip_ai_on_short_content and len(content) < self.min_content_length:
                article['summary'] = content
                article['is_summarized'] = False
                summarized_articles.append(article)
            else:
                articles_to_summarize.append(article)

        # Process articles that need summarization
        if articles_to_summarize and self.batch_processing:
            # Batch processing to reduce API calls
            batch_size = 3  # Process 3 articles at a time
            for i in range(0, len(articles_to_summarize), batch_size):
                batch = articles_to_summarize[i:i + batch_size]

                for article in batch:
                    try:
                        content = article.get('content', '')
                        # Truncate content to save tokens
                        truncated_content = content[:self.ai_config.get('max_content_length', 5000)]

                        summary = await self._try_with_fallback(_summarize_operation, truncated_content, max_length)
                        article['summary'] = summary or content[:max_length]
                        article['is_summarized'] = bool(summary)

                    except Exception as e:
                        logger.error(f"Failed to summarize article: {str(e)}")
                        article['summary'] = article.get('content', '')[:max_length]
                        article['is_summarized'] = False

                    summarized_articles.append(article)

                # Add delay between batches to respect rate limits
                if i + batch_size < len(articles_to_summarize):
                    await asyncio.sleep(1)
        else:
            # Individual processing
            for article in articles_to_summarize:
                try:
                    content = article.get('content', '')
                    truncated_content = content[:self.ai_config.get('max_content_length', 5000)]

                    summary = await self._try_with_fallback(_summarize_operation, truncated_content, max_length)
                    article['summary'] = summary or content[:max_length]
                    article['is_summarized'] = bool(summary)

                except Exception as e:
                    logger.error(f"Failed to summarize article: {str(e)}")
                    article['summary'] = article.get('content', '')[:max_length]
                    article['is_summarized'] = False

                summarized_articles.append(article)

        logger.info(f"Summarized {len(summarized_articles)} articles ({len(articles_to_summarize)} processed with AI)")
        return summarized_articles
    
    async def enhance_search_query(self, query: str) -> str:
        """
        Enhance search query for better results

        Args:
            query: Original search query

        Returns:
            Enhanced query
        """
        async def _enhance_operation(provider, query):
            return await provider.enhance_query(query)

        try:
            enhanced_query = await self._try_with_fallback(_enhance_operation, query)
            if enhanced_query:
                logger.info(f"Enhanced query: '{query}' -> '{enhanced_query}'")
                return enhanced_query
            else:
                logger.warning("Query enhancement failed, returning original query")
                return query
        except Exception as e:
            logger.error(f"Query enhancement failed: {str(e)}")
            return query
    
    async def extract_keywords_from_articles(
        self,
        articles: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Extract keywords from articles with optimization

        Args:
            articles: List of articles

        Returns:
            Articles with keywords added
        """
        async def _extract_keywords_operation(provider, content):
            return await provider.extract_keywords(content, max_keywords=5)  # Limit keywords

        enhanced_articles = []
        processed_count = 0

        for article in articles:
            try:
                content = article.get('content', '')

                # Skip keyword extraction for short content or if rate limited
                if (self.skip_ai_on_short_content and len(content) < self.min_content_length) or not self._check_rate_limit():
                    article['ai_keywords'] = []
                    enhanced_articles.append(article)
                    continue

                if content:
                    # Use summary if available to save tokens
                    text_to_process = article.get('summary', content)[:1000]  # Limit text length

                    keywords = await self._try_with_fallback(_extract_keywords_operation, text_to_process)
                    article['ai_keywords'] = keywords or []
                    processed_count += 1
                else:
                    article['ai_keywords'] = []

                enhanced_articles.append(article)

                # Add small delay between requests
                if processed_count % 3 == 0:  # Every 3 requests
                    await asyncio.sleep(0.5)

            except Exception as e:
                logger.error(f"Failed to extract keywords: {str(e)}")
                article['ai_keywords'] = []
                enhanced_articles.append(article)

        logger.info(f"Extracted keywords for {len(enhanced_articles)} articles ({processed_count} processed with AI)")
        return enhanced_articles
    
    async def perform_rag_search(
        self, 
        query: str, 
        articles: List[Dict[str, Any]], 
        top_k: int = 5
    ) -> Dict[str, Any]:
        """
        Perform Retrieval-Augmented Generation (RAG) search
        
        Args:
            query: Search query
            articles: Available articles for context
            top_k: Number of top articles to use for context
            
        Returns:
            RAG response with sources
        """
        try:
            # Find most relevant articles using semantic search
            relevant_articles = await self.embedding_manager.semantic_search(
                query, articles, top_k
            )
            
            if not relevant_articles:
                logger.warning("No relevant articles found for RAG")
                return {
                    "answer": "لم يتم العثور على معلومات ذات صلة بالاستعلام.",
                    "sources": [],
                    "confidence": 0.0
                }
            
            # Prepare context from relevant articles
            context_parts = []
            sources = []
            
            for i, article in enumerate(relevant_articles):
                title = article.get('title', f'مقال {i+1}')
                content = article.get('summary', article.get('content', ''))[:1000]
                url = article.get('url', '')
                
                context_parts.append(f"المصدر {i+1}: {title}\n{content}")
                sources.append({
                    "title": title,
                    "url": url,
                    "similarity_score": article.get('similarity_score', 0.0)
                })
            
            context = "\n\n".join(context_parts)
            
            # Generate answer using AI provider
            if self.provider:
                system_prompt = """أنت مساعد ذكي متخصص في الإجابة على الأسئلة بناءً على المصادر المتاحة.
                مهمتك هي تقديم إجابة دقيقة ومفيدة بناءً على المعلومات المقدمة فقط."""
                
                prompt = f"""بناءً على المصادر التالية، يرجى الإجابة على السؤال:

السؤال: {query}

المصادر:
{context}

الإجابة:"""
                
                answer = await self.provider.generate_text(prompt, system_prompt)
            else:
                answer = "خدمة الذكاء الاصطناعي غير متاحة حالياً."
            
            # Calculate average confidence
            avg_confidence = sum(s['similarity_score'] for s in sources) / len(sources)
            
            return {
                "answer": answer,
                "sources": sources,
                "confidence": avg_confidence,
                "context_used": len(relevant_articles)
            }
            
        except Exception as e:
            logger.error(f"RAG search failed: {str(e)}")
            return {
                "answer": "حدث خطأ أثناء معالجة الاستعلام.",
                "sources": [],
                "confidence": 0.0
            }
    
    async def analyze_article_sentiment(
        self,
        articles: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Analyze sentiment of articles with optimization (DISABLED to save tokens)

        Args:
            articles: List of articles

        Returns:
            Articles with sentiment analysis added
        """
        # DISABLED sentiment analysis to save API tokens
        # Can be re-enabled by setting skip_sentiment_analysis = False
        skip_sentiment_analysis = True

        analyzed_articles = []

        for article in articles:
            if skip_sentiment_analysis:
                # Skip sentiment analysis to save tokens
                article['sentiment'] = {
                    "sentiment": "محايدة",
                    "confidence": 0.5,
                    "analysis": "تم تعطيل تحليل المشاعر لتوفير الاستهلاك"
                }
            else:
                try:
                    content = article.get('summary', article.get('content', ''))[:500]  # Use summary and limit length

                    if content and len(content) > 50 and self._check_rate_limit():
                        sentiment_analysis = await self._try_with_fallback(
                            lambda provider, content: provider.analyze_sentiment(content),
                            content
                        )
                        article['sentiment'] = sentiment_analysis or {
                            "sentiment": "محايدة",
                            "confidence": 0.0,
                            "analysis": "فشل في التحليل"
                        }
                    else:
                        article['sentiment'] = {
                            "sentiment": "محايدة",
                            "confidence": 0.0,
                            "analysis": "لا يوجد محتوى كافي للتحليل"
                        }

                except Exception as e:
                    logger.error(f"Failed to analyze sentiment: {str(e)}")
                    article['sentiment'] = {
                        "sentiment": "غير محدد",
                        "confidence": 0.0,
                        "analysis": "فشل في التحليل"
                    }

            analyzed_articles.append(article)

        logger.info(f"Processed sentiment for {len(analyzed_articles)} articles (analysis {'disabled' if skip_sentiment_analysis else 'enabled'})")
        return analyzed_articles
    
    async def health_check(self) -> Dict[str, bool]:
        """
        Check health of AI components
        
        Returns:
            Health status of AI components
        """
        health_status = {}
        
        # Check AI provider
        if self.provider:
            try:
                provider_health = await self.provider.health_check()
                health_status['ai_provider'] = provider_health
            except Exception as e:
                logger.error(f"AI provider health check failed: {str(e)}")
                health_status['ai_provider'] = False
        else:
            health_status['ai_provider'] = False
        
        # Check embedding manager
        embedding_info = self.embedding_manager.get_model_info()
        health_status['embedding_manager'] = embedding_info['status'] == 'available'
        
        return health_status
