// Enhanced DeepSearch Agent Interface

const API_BASE_URL = 'http://localhost:8000';

// Global state
let currentArticle = null;
let systemStatus = {
    search: false,
    gemini: false,
    blogger: false
};

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    checkSystemStatus();
    
    // Add enter key support
    document.getElementById('deepSearchQuery').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') performDeepSearch();
    });
    
    document.getElementById('articleTopic').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') generateArticle();
    });
});

// System Status Check
async function checkSystemStatus() {
    try {
        // Check main API
        const healthResponse = await fetch(`${API_BASE_URL}/health`);
        if (healthResponse.ok) {
            systemStatus.search = true;
            updateStatusIndicator('searchStatus', 'success');
        }
        
        // Check Gemini status
        try {
            const geminiResponse = await fetch(`${API_BASE_URL}/articles/status`);
            if (geminiResponse.ok) {
                const geminiStatus = await geminiResponse.json();
                if (geminiStatus.configured) {
                    systemStatus.gemini = true;
                    updateStatusIndicator('geminiStatus', 'success');
                } else {
                    updateStatusIndicator('geminiStatus', 'warning');
                }
            }
        } catch (e) {
            updateStatusIndicator('geminiStatus', 'danger');
        }
        
        // Check Blogger status
        try {
            const bloggerResponse = await fetch(`${API_BASE_URL}/publishing/blogger/status`);
            if (bloggerResponse.ok) {
                const bloggerStatus = await bloggerResponse.json();
                if (bloggerStatus.configured) {
                    systemStatus.blogger = true;
                    updateStatusIndicator('bloggerStatus', 'success');
                } else {
                    updateStatusIndicator('bloggerStatus', 'warning');
                }
            }
        } catch (e) {
            updateStatusIndicator('bloggerStatus', 'danger');
        }
        
    } catch (error) {
        console.error('System status check failed:', error);
        updateStatusIndicator('searchStatus', 'danger');
    }
}

function updateStatusIndicator(elementId, status) {
    const element = document.getElementById(elementId);
    element.className = `status-indicator status-${status}`;
}

// Loading Management
function showLoading(message = 'جاري المعالجة...', details = 'يرجى الانتظار') {
    document.getElementById('loadingMessage').textContent = message;
    document.getElementById('loadingDetails').textContent = details;
    document.getElementById('loadingOverlay').style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// Deep Search
async function performDeepSearch() {
    const query = document.getElementById('deepSearchQuery').value.trim();
    if (!query) {
        showAlert('يرجى إدخال موضوع البحث', 'warning');
        return;
    }

    const articleStyle = document.getElementById('articleStyle').value;
    const targetLanguage = document.getElementById('targetLanguage').value;

    showLoading('جاري البحث العميق...', 'نبحث عن أفضل المقالات ونحللها بعمق');

    try {
        const response = await fetch(`${API_BASE_URL}/deep-search`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: new URLSearchParams({
                query: query,
                target_language: targetLanguage,
                article_style: articleStyle
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        displaySearchResults(result.data);

    } catch (error) {
        console.error('Deep search failed:', error);
        showAlert('فشل البحث العميق: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// Auto News Discovery
async function discoverTrendingTopic() {
    const category = document.getElementById('newsCategory').value;
    const language = document.getElementById('targetLanguage').value;

    showLoading('جاري اكتشاف الأخبار الرائجة...', 'نبحث عن أفضل الأخبار ونحللها تلقائياً');

    try {
        const response = await fetch(`${API_BASE_URL}/discover-trending-topic`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: new URLSearchParams({
                category: category || '',
                language: language
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        displayTrendingTopicResults(result.data);

    } catch (error) {
        console.error('Trending topic discovery failed:', error);
        showAlert('فشل اكتشاف الأخبار: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

function displayTrendingTopicResults(result) {
    const container = document.getElementById('searchResults');

    if (!result.success) {
        container.innerHTML = `
            <div class="alert alert-danger alert-custom">
                <i class="fas fa-exclamation-triangle"></i>
                فشل اكتشاف الأخبار: ${result.error}
            </div>
        `;
        container.style.display = 'block';
        return;
    }

    const discoveredTopic = result.discovered_topic;
    const searchResult = result.deep_search_result;

    container.innerHTML = `
        <div class="result-container">
            <h4><i class="fas fa-newspaper"></i> خبر رائج تم اكتشافه تلقائياً</h4>

            <div class="alert alert-info alert-custom">
                <h6><i class="fas fa-robot"></i> تم اختيار هذا الخبر تلقائياً بواسطة الذكاء الاصطناعي</h6>
                <p><strong>العنوان:</strong> ${discoveredTopic.title}</p>
                <p><strong>المصدر:</strong> ${discoveredTopic.source}</p>
                <p><strong>الفئة:</strong> ${discoveredTopic.matched_categories?.join(', ') || 'عام'}</p>
                <p><strong>درجة الصلة:</strong> ${(discoveredTopic.relevance_score * 20).toFixed(1)}%</p>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <h5>الوصف:</h5>
                    <p>${discoveredTopic.description}</p>

                    ${result.suggested_angles && result.suggested_angles.length > 0 ? `
                        <div class="mt-3">
                            <h6><i class="fas fa-lightbulb"></i> زوايا مقترحة للمقال:</h6>
                            <ul>
                                ${result.suggested_angles.map(angle => `<li>${angle}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${result.suggested_keywords && result.suggested_keywords.length > 0 ? `
                        <div class="mt-3">
                            <h6><i class="fas fa-tags"></i> كلمات مفتاحية مقترحة:</h6>
                            ${result.suggested_keywords.map(keyword =>
                                `<span class="badge bg-primary me-1">${keyword}</span>`
                            ).join('')}
                        </div>
                    ` : ''}
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6>إمكانية المقال</h6>
                            ${result.article_potential ? `
                                <ul class="list-unstyled">
                                    <li><strong>إمكانية SEO:</strong> ${result.article_potential.seo_potential}</li>
                                    <li><strong>إمكانية التفاعل:</strong> ${result.article_potential.engagement_potential}</li>
                                    <li><strong>درجة الرواج:</strong> ${result.article_potential.trending_score}%</li>
                                    <li><strong>مُوصى به:</strong> ${result.article_potential.recommended ? 'نعم' : 'لا'}</li>
                                </ul>
                            ` : 'غير متوفر'}
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-3">
                <button onclick="useDiscoveredTopicForArticle('${discoveredTopic.title}')" class="btn btn-success">
                    <i class="fas fa-arrow-right"></i> استخدم لإنتاج مقال
                </button>
                <button onclick="performDeepSearchOnTopic('${discoveredTopic.title}')" class="btn btn-info ms-2">
                    <i class="fas fa-search-plus"></i> بحث عميق إضافي
                </button>
            </div>
        </div>
    `;

    container.style.display = 'block';

    // Store discovered topic for article generation
    window.discoveredTopic = result;
}

function displaySearchResults(result) {
    const container = document.getElementById('searchResults');
    
    if (!result.success) {
        container.innerHTML = `
            <div class="alert alert-danger alert-custom">
                <i class="fas fa-exclamation-triangle"></i>
                فشل البحث: ${result.error}
            </div>
        `;
        container.style.display = 'block';
        return;
    }
    
    const selectedArticle = result.selected_article;
    const originalArticle = selectedArticle.original_article;
    const deepAnalysis = selectedArticle.deep_analysis;
    
    container.innerHTML = `
        <div class="result-container">
            <h4><i class="fas fa-newspaper"></i> نتائج البحث العميق</h4>
            
            <div class="row">
                <div class="col-md-8">
                    <h5>${originalArticle.title}</h5>
                    <p class="text-muted">
                        <i class="fas fa-link"></i>
                        <a href="${originalArticle.url}" target="_blank">${originalArticle.url}</a>
                    </p>
                    
                    ${deepAnalysis.comprehensive_summary ? `
                        <div class="mt-3">
                            <h6><i class="fas fa-file-alt"></i> الملخص الشامل:</h6>
                            <p>${deepAnalysis.comprehensive_summary}</p>
                        </div>
                    ` : ''}
                    
                    ${selectedArticle.seo_keywords && selectedArticle.seo_keywords.length > 0 ? `
                        <div class="mt-3">
                            <h6><i class="fas fa-tags"></i> الكلمات المفتاحية SEO:</h6>
                            ${selectedArticle.seo_keywords.map(keyword => 
                                `<span class="badge bg-primary me-1">${keyword}</span>`
                            ).join('')}
                        </div>
                    ` : ''}
                    
                    ${selectedArticle.related_topics && selectedArticle.related_topics.length > 0 ? `
                        <div class="mt-3">
                            <h6><i class="fas fa-lightbulb"></i> مواضيع ذات صلة:</h6>
                            <ul>
                                ${selectedArticle.related_topics.map(topic => `<li>${topic}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6>إحصائيات المحتوى</h6>
                            <ul class="list-unstyled">
                                <li><strong>عدد الكلمات:</strong> ${selectedArticle.content_structure?.word_count || 'غير محدد'}</li>
                                <li><strong>وقت القراءة:</strong> ${selectedArticle.content_structure?.estimated_reading_time || 'غير محدد'} دقيقة</li>
                                <li><strong>جودة المحتوى:</strong> ${selectedArticle.quality_metrics?.content_depth || 'غير محدد'}</li>
                                <li><strong>درجة الصلة:</strong> ${selectedArticle.quality_metrics?.relevance_score ? (selectedArticle.quality_metrics.relevance_score * 100).toFixed(1) + '%' : 'غير محدد'}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <button onclick="useForArticle('${result.query}')" class="btn btn-success">
                    <i class="fas fa-arrow-right"></i> استخدم لإنتاج مقال
                </button>
            </div>
        </div>
    `;
    
    container.style.display = 'block';
    
    // Store search result for article generation
    window.searchResult = result;
}

// Article Generation
async function generateArticle() {
    const topic = document.getElementById('articleTopic').value.trim();
    if (!topic) {
        showAlert('يرجى إدخال موضوع المقال', 'warning');
        return;
    }

    if (!systemStatus.gemini) {
        showAlert('يجب تكوين Gemini AI أولاً من تبويب الإعدادات', 'warning');
        return;
    }

    const articleType = document.getElementById('articleType').value;
    const articleLanguage = document.getElementById('articleLanguage').value;
    const arabicDialect = document.getElementById('arabicDialect').value;
    const wordCountRange = document.getElementById('wordCount').value;
    const useDeepSearch = document.getElementById('useDeepSearch').checked;
    const seoKeywords = document.getElementById('seoKeywords').value;
    const egyptianDialect = document.getElementById('egyptianDialect').checked;
    const addSpellingErrors = document.getElementById('addSpellingErrors').checked;

    // Parse word count
    const [minWords, maxWords] = wordCountRange.split('-').map(w => parseInt(w));

    // Parse SEO keywords
    const keywords = seoKeywords ? seoKeywords.split(',').map(k => k.trim()).filter(k => k) : [];

    showLoading('جاري إنتاج المقال...', 'نستخدم الذكاء الاصطناعي لكتابة مقال عالي الجودة');

    try {
        const requestBody = {
            topic: topic,
            article_type: articleType,
            target_language: articleLanguage,
            dialect: arabicDialect,
            seo_keywords: keywords,
            min_words: minWords,
            max_words: maxWords,
            use_deep_search: useDeepSearch,
            egyptian_dialect: egyptianDialect,
            add_spelling_errors: addSpellingErrors
        };

        const response = await fetch(`${API_BASE_URL}/articles/generate`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        displayArticleResults(result);

    } catch (error) {
        console.error('Article generation failed:', error);
        showAlert('فشل إنتاج المقال: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

function displayArticleResults(result) {
    const container = document.getElementById('articleResults');
    
    if (!result.success) {
        container.innerHTML = `
            <div class="alert alert-danger alert-custom">
                <i class="fas fa-exclamation-triangle"></i>
                فشل إنتاج المقال: ${result.error}
            </div>
        `;
        container.style.display = 'block';
        return;
    }
    
    currentArticle = result.article;
    const metadata = result.generation_metadata;
    
    container.innerHTML = `
        <div class="result-container">
            <h4><i class="fas fa-file-alt"></i> المقال المُنتج</h4>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <h5>العنوان:</h5>
                        <div class="code-container">
                            <button class="copy-button" onclick="copyToClipboard('${currentArticle.title}')">نسخ</button>
                            <strong>${currentArticle.title}</strong>
                        </div>
                    </div>
                    
                    ${currentArticle.meta_description ? `
                        <div class="mb-3">
                            <h6>الوصف التعريفي (Meta Description):</h6>
                            <div class="code-container">
                                <button class="copy-button" onclick="copyToClipboard('${currentArticle.meta_description}')">نسخ</button>
                                <p>${currentArticle.meta_description}</p>
                            </div>
                        </div>
                    ` : ''}
                    
                    <div class="mb-3">
                        <h6>محتوى المقال:</h6>
                        <div class="code-container" style="max-height: 400px; overflow-y: auto;">
                            <button class="copy-button" onclick="copyToClipboard(\`${currentArticle.content.replace(/`/g, '\\`')}\`)">نسخ</button>
                            <div style="white-space: pre-wrap;">${currentArticle.content}</div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6>إحصائيات المقال</h6>
                            <ul class="list-unstyled">
                                <li><strong>عدد الكلمات:</strong> ${currentArticle.word_count}</li>
                                <li><strong>وقت القراءة:</strong> ${currentArticle.reading_time_minutes} دقيقة</li>
                                <li><strong>نوع المقال:</strong> ${getArticleTypeName(currentArticle.article_type)}</li>
                                <li><strong>اللهجة المصرية:</strong> ${currentArticle.egyptian_dialect_applied ? 'نعم' : 'لا'}</li>
                                <li><strong>أخطاء إملائية:</strong> ${currentArticle.spelling_errors_added ? 'نعم' : 'لا'}</li>
                            </ul>
                            
                            ${currentArticle.seo_keywords && currentArticle.seo_keywords.length > 0 ? `
                                <h6 class="mt-3">الكلمات المفتاحية:</h6>
                                ${currentArticle.seo_keywords.map(keyword => 
                                    `<span class="badge bg-secondary me-1">${keyword}</span>`
                                ).join('')}
                            ` : ''}
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-body">
                            <h6>معلومات الإنتاج</h6>
                            <ul class="list-unstyled">
                                <li><strong>النموذج:</strong> ${metadata.model_used}</li>
                                <li><strong>وقت المعالجة:</strong> ${metadata.processing_time.toFixed(2)}s</li>
                                <li><strong>مصادر البحث:</strong> ${metadata.research_sources}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <button onclick="switchToPublishTab()" class="btn btn-success">
                    <i class="fas fa-upload"></i> انتقل للنشر
                </button>
                <button onclick="generateHTMLCopy()" class="btn btn-info ms-2">
                    <i class="fas fa-code"></i> إنشاء نسخة HTML
                </button>
            </div>
        </div>
    `;
    
    container.style.display = 'block';
    
    // Enable publishing options
    document.getElementById('publishingOptions').style.display = 'block';
}

// Utility Functions
function useForArticle(topic) {
    document.getElementById('articleTopic').value = topic;
    
    // Switch to article tab
    const articleTab = new bootstrap.Tab(document.getElementById('article-tab'));
    articleTab.show();
}

function switchToPublishTab() {
    const publishTab = new bootstrap.Tab(document.getElementById('publish-tab'));
    publishTab.show();
}

function getArticleTypeName(type) {
    const types = {
        'professional': 'مهني ورسمي',
        'casual': 'غير رسمي',
        'news': 'إخباري',
        'analysis': 'تحليلي',
        'tutorial': 'تعليمي',
        'review': 'مراجعة'
    };
    return types[type] || type;
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showAlert('تم النسخ بنجاح!', 'success');
    }).catch(err => {
        console.error('Failed to copy:', err);
        showAlert('فشل في النسخ', 'danger');
    });
}

function showAlert(message, type = 'info') {
    // Create and show bootstrap alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 10000; max-width: 400px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Configuration Functions
async function configureGemini() {
    const apiKey = document.getElementById('geminiApiKey').value.trim();
    const model = document.getElementById('geminiModel').value;

    if (!apiKey) {
        showAlert('يرجى إدخال Gemini API Key', 'warning');
        return;
    }

    showLoading('جاري تكوين Gemini...', 'نختبر الاتصال والنموذج المحدد');

    try {
        const response = await fetch(`${API_BASE_URL}/articles/configure-gemini`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                api_key: apiKey,
                model_name: model
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('تم تكوين Gemini بنجاح!', 'success');
            systemStatus.gemini = true;
            updateStatusIndicator('geminiStatus', 'success');
        } else {
            showAlert('فشل تكوين Gemini: ' + result.error, 'danger');
        }

    } catch (error) {
        console.error('Gemini configuration failed:', error);
        showAlert('فشل تكوين Gemini: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

async function testGemini() {
    if (!systemStatus.gemini) {
        showAlert('يجب تكوين Gemini أولاً', 'warning');
        return;
    }

    showLoading('جاري اختبار Gemini...', 'نختبر إنتاج مقال تجريبي');

    try {
        const response = await fetch(`${API_BASE_URL}/articles/test`, {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            showAlert('اختبار Gemini نجح!', 'success');
        } else {
            showAlert('فشل اختبار Gemini: ' + result.error, 'danger');
        }

    } catch (error) {
        console.error('Gemini test failed:', error);
        showAlert('فشل اختبار Gemini: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

async function configureBlogger() {
    const serviceAccountJson = document.getElementById('serviceAccountJson').value.trim();
    const blogId = document.getElementById('blogId').value.trim();

    if (!serviceAccountJson || !blogId) {
        showAlert('يرجى إدخال Service Account JSON و Blog ID', 'warning');
        return;
    }

    showLoading('جاري تكوين Blogger...', 'نختبر الاتصال مع المدونة');

    try {
        const response = await fetch(`${API_BASE_URL}/publishing/blogger/configure`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                service_account_json: serviceAccountJson,
                blog_id: blogId
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert('تم تكوين Blogger بنجاح!', 'success');
            systemStatus.blogger = true;
            updateStatusIndicator('bloggerStatus', 'success');
        } else {
            showAlert('فشل تكوين Blogger: ' + result.error, 'danger');
        }

    } catch (error) {
        console.error('Blogger configuration failed:', error);
        showAlert('فشل تكوين Blogger: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

async function testBlogger() {
    if (!systemStatus.blogger) {
        showAlert('يجب تكوين Blogger أولاً', 'warning');
        return;
    }

    showLoading('جاري اختبار Blogger...', 'نختبر الوصول للمدونة');

    try {
        const response = await fetch(`${API_BASE_URL}/publishing/blogger/status`);
        const result = await response.json();

        if (result.configured && result.ready) {
            showAlert(`اختبار Blogger نجح! المدونة: ${result.blog_name}`, 'success');
        } else {
            showAlert('Blogger غير مكون بشكل صحيح', 'warning');
        }

    } catch (error) {
        console.error('Blogger test failed:', error);
        showAlert('فشل اختبار Blogger: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// Publishing Functions
async function publishToBlogger() {
    if (!currentArticle) {
        showAlert('يجب إنتاج مقال أولاً', 'warning');
        return;
    }

    if (!systemStatus.blogger) {
        showAlert('يجب تكوين Blogger أولاً من تبويب الإعدادات', 'warning');
        return;
    }

    const isDraft = document.getElementById('publishAsDraft').checked;
    const customLabels = document.getElementById('customLabels').value;
    const labels = customLabels ? customLabels.split(',').map(l => l.trim()).filter(l => l) : [];

    showLoading('جاري النشر على Blogger...', 'ننشر المقال على مدونتك');

    try {
        const response = await fetch(`${API_BASE_URL}/publishing/blogger/publish`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                title: currentArticle.title,
                content: currentArticle.content,
                labels: [...(currentArticle.seo_keywords || []), ...labels],
                is_draft: isDraft,
                meta_description: currentArticle.meta_description,
                seo_keywords: currentArticle.seo_keywords
            })
        });

        const result = await response.json();

        if (result.success) {
            displayPublishingResults(result, 'blogger');
            showAlert('تم النشر على Blogger بنجاح!', 'success');
        } else {
            showAlert('فشل النشر على Blogger: ' + result.error, 'danger');
        }

    } catch (error) {
        console.error('Blogger publishing failed:', error);
        showAlert('فشل النشر على Blogger: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

async function generateHTMLCopy() {
    if (!currentArticle) {
        showAlert('يجب إنتاج مقال أولاً', 'warning');
        return;
    }

    showLoading('جاري إنشاء نسخة HTML...', 'نحضر النسخة للصق المباشر');

    try {
        const response = await fetch(`${API_BASE_URL}/publishing/html-copy`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                title: currentArticle.title,
                content: currentArticle.content,
                meta_description: currentArticle.meta_description,
                seo_keywords: currentArticle.seo_keywords
            })
        });

        const result = await response.json();

        if (result.success) {
            displayHTMLCopy(result.html_content);
            showAlert('تم إنشاء نسخة HTML بنجاح!', 'success');
        } else {
            showAlert('فشل إنشاء نسخة HTML: ' + result.error, 'danger');
        }

    } catch (error) {
        console.error('HTML copy generation failed:', error);
        showAlert('فشل إنشاء نسخة HTML: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

function displayPublishingResults(result, platform) {
    const container = document.getElementById('publishingResults');

    container.innerHTML = `
        <div class="result-container">
            <h4><i class="fas fa-check-circle text-success"></i> تم النشر بنجاح</h4>

            <div class="alert alert-success alert-custom">
                <h6>تفاصيل النشر:</h6>
                <ul class="mb-0">
                    <li><strong>المنصة:</strong> ${platform === 'blogger' ? 'Blogger' : platform}</li>
                    <li><strong>حالة المقال:</strong> ${result.status === 'draft' ? 'مسودة' : 'منشور'}</li>
                    <li><strong>رابط المقال:</strong> <a href="${result.post_url}" target="_blank">${result.post_url}</a></li>
                    <li><strong>تاريخ النشر:</strong> ${new Date(result.published_date).toLocaleString('ar')}</li>
                    ${result.labels && result.labels.length > 0 ? `
                        <li><strong>التصنيفات:</strong> ${result.labels.join(', ')}</li>
                    ` : ''}
                </ul>
            </div>
        </div>
    `;

    container.style.display = 'block';
}

function displayHTMLCopy(htmlContent) {
    const container = document.getElementById('publishingResults');

    container.innerHTML = `
        <div class="result-container">
            <h4><i class="fas fa-code"></i> نسخة HTML للصق المباشر</h4>

            <div class="alert alert-info alert-custom">
                <i class="fas fa-info-circle"></i>
                يمكنك نسخ الكود أدناه ولصقه مباشرة في محرر HTML في Blogger
            </div>

            <div class="code-container" style="max-height: 400px; overflow-y: auto;">
                <button class="copy-button" onclick="copyToClipboard(\`${htmlContent.replace(/`/g, '\\`')}\`)">نسخ الكود كاملاً</button>
                <pre style="white-space: pre-wrap; font-size: 12px;">${htmlContent}</pre>
            </div>

            <div class="mt-3">
                <button onclick="downloadHTML('${currentArticle.title}', \`${htmlContent.replace(/`/g, '\\`')}\`)" class="btn btn-outline-primary">
                    <i class="fas fa-download"></i> تحميل كملف HTML
                </button>
            </div>
        </div>
    `;

    container.style.display = 'block';
}

function downloadHTML(title, htmlContent) {
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showAlert('تم تحميل الملف بنجاح!', 'success');
}

// Enhanced Search APIs Configuration
async function configureSearchAPIs() {
    const braveApiKey = document.getElementById('braveApiKey').value.trim();
    const tavilyApiKey = document.getElementById('tavilyApiKey').value.trim();
    const newsApiKey = document.getElementById('newsApiKey').value.trim();
    const gnewsApiKey = document.getElementById('gnewsApiKey').value.trim();

    if (!braveApiKey && !tavilyApiKey && !newsApiKey && !gnewsApiKey) {
        showAlert('يرجى إدخال مفتاح API واحد على الأقل', 'warning');
        return;
    }

    showLoading('جاري تكوين APIs...', 'نختبر الاتصال مع الخدمات');

    try {
        const params = new URLSearchParams();
        if (braveApiKey) params.append('brave_api_key', braveApiKey);
        if (tavilyApiKey) params.append('tavily_api_key', tavilyApiKey);
        if (newsApiKey) params.append('newsapi_key', newsApiKey);
        if (gnewsApiKey) params.append('gnews_key', gnewsApiKey);

        const response = await fetch(`${API_BASE_URL}/configure-search-apis`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: params
        });

        const result = await response.json();

        if (result.success) {
            showAlert('تم تكوين APIs بنجاح!', 'success');

            // Update system status
            checkSystemStatus();
        } else {
            showAlert('فشل تكوين APIs: ' + (result.error || 'خطأ غير معروف'), 'danger');
        }

    } catch (error) {
        console.error('Search APIs configuration failed:', error);
        showAlert('فشل تكوين APIs: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

async function testSearchAPIs() {
    showLoading('جاري اختبار APIs...', 'نختبر الاتصال مع خدمات البحث');

    try {
        const response = await fetch(`${API_BASE_URL}/enhanced-search-status`);
        const result = await response.json();

        if (result.success) {
            const searchApis = result.data.search_apis;
            const autoNews = result.data.auto_news;

            let statusMessage = 'حالة APIs:\n';
            statusMessage += `• Brave Search: ${searchApis.brave_search?.configured ? 'مكون' : 'غير مكون'}\n`;
            statusMessage += `• Tavily Search: ${searchApis.tavily_search?.configured ? 'مكون' : 'غير مكون'}\n`;
            statusMessage += `• News APIs: ${autoNews.configured_apis}/${autoNews.total_apis} مكونة`;

            showAlert(statusMessage, 'info');
        } else {
            showAlert('فشل اختبار APIs', 'danger');
        }

    } catch (error) {
        console.error('Search APIs test failed:', error);
        showAlert('فشل اختبار APIs: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// Helper functions for discovered topics
function useDiscoveredTopicForArticle(topic) {
    document.getElementById('articleTopic').value = topic;

    // Switch to article tab
    const articleTab = new bootstrap.Tab(document.getElementById('article-tab'));
    articleTab.show();

    // Pre-fill some settings based on discovered topic
    if (window.discoveredTopic) {
        const suggestedKeywords = window.discoveredTopic.suggested_keywords;
        if (suggestedKeywords && suggestedKeywords.length > 0) {
            document.getElementById('seoKeywords').value = suggestedKeywords.slice(0, 5).join(', ');
        }
    }
}

async function performDeepSearchOnTopic(topic) {
    document.getElementById('deepSearchQuery').value = topic;
    await performDeepSearch();
}
