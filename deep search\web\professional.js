// DeepSearch Professional Articles JavaScript

const API_BASE_URL = 'http://localhost:8001';

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Add enter key support
    document.getElementById('professionalQuery').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            getProfessionalArticles();
        }
    });
});

// Get professional articles
async function getProfessionalArticles() {
    const query = document.getElementById('professionalQuery').value.trim();
    if (!query) {
        showError('يرجى إدخال استعلام البحث');
        return;
    }
    
    const agentId = document.getElementById('agentId').value.trim() || 'default';
    const maxArticles = parseInt(document.getElementById('articleCount').value);
    const extractFullContent = document.getElementById('extractFullContent').checked;
    
    showLoading();
    hideError();
    hideResults();
    
    try {
        const response = await fetch(`${API_BASE_URL}/news/professional`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                query: query,
                agent_id: agentId,
                max_articles: maxArticles,
                extract_full_content: extractFullContent
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        showProfessionalResults(result);
        
    } catch (error) {
        console.error('Professional articles error:', error);
        showError('حدث خطأ أثناء الحصول على المقالات الاحترافية: ' + error.message);
    } finally {
        hideLoading();
    }
}

// Show professional results
function showProfessionalResults(result) {
    showResults();
    
    // Update delivery info
    const deliveryInfo = document.getElementById('deliveryInfo');
    deliveryInfo.innerHTML = `
        <div class="agent-info">
            <i class="fas fa-robot"></i> ${result.agent_id}
        </div>
        <small>
            <i class="fas fa-clock"></i> ${new Date(result.delivery_time).toLocaleString('ar')} |
            <i class="fas fa-list"></i> ${result.total_delivered} مقال |
            <i class="fas fa-stopwatch"></i> ${result.processing_time.toFixed(2)}s
            <span class="no-duplicates-badge ms-2">بدون تكرار</span>
        </small>
    `;
    
    // Show articles
    const container = document.getElementById('articlesContainer');
    container.innerHTML = '';
    
    if (result.professional_articles && result.professional_articles.length > 0) {
        result.professional_articles.forEach((article, index) => {
            const articleCard = createProfessionalArticleCard(article, index + 1);
            container.appendChild(articleCard);
        });
    } else {
        container.innerHTML = `
            <div class="alert alert-info">
                <h6>${result.message}</h6>
                <p>المقالات الموجودة: ${result.total_found} | الجديدة: ${result.total_new || 0}</p>
                <button class="btn btn-outline-primary" onclick="clearAgentHistory()">
                    مسح التاريخ لإعادة الحصول على المقالات
                </button>
            </div>
        `;
    }
}

// Create professional article card
function createProfessionalArticleCard(article, index) {
    const card = document.createElement('div');
    card.className = 'article-card';
    
    const hasFullContent = article.has_full_content && article.full_content;
    const hasProfessionalSummary = article.professional_summary;
    const hasInsights = article.key_insights && article.key_insights.length > 0;
    const hasSuggestions = article.writing_suggestions;
    
    card.innerHTML = `
        <div class="d-flex justify-content-between align-items-start mb-3">
            <h5 class="fw-bold mb-0">${article.title || 'بدون عنوان'}</h5>
            <span class="badge bg-primary">${index}</span>
        </div>
        
        <!-- Content Stats -->
        <div class="content-stats">
            <div class="stat-item">
                <i class="fas fa-star"></i> نقاط الجودة: ${article.news_score?.toFixed(1) || 'N/A'}
            </div>
            ${article.word_count ? `
                <div class="stat-item">
                    <i class="fas fa-file-word"></i> ${article.word_count} كلمة
                </div>
            ` : ''}
            ${article.estimated_reading_time ? `
                <div class="stat-item">
                    <i class="fas fa-clock"></i> ${article.estimated_reading_time}
                </div>
            ` : ''}
            ${article.content_quality_score ? `
                <div class="stat-item">
                    <i class="fas fa-chart-line"></i> جودة المحتوى: ${article.content_quality_score.toFixed(1)}/10
                </div>
            ` : ''}
        </div>
        
        <!-- Professional Summary -->
        ${hasProfessionalSummary ? `
            <div class="professional-summary">
                <h6><i class="fas fa-graduation-cap"></i> الملخص الاحترافي:</h6>
                <p>${article.professional_summary}</p>
            </div>
        ` : ''}
        
        <!-- Key Insights -->
        ${hasInsights ? `
            <div class="insights-section">
                <h6><i class="fas fa-lightbulb"></i> النقاط الرئيسية:</h6>
                <ul class="mb-0">
                    ${article.key_insights.map(insight => `<li>${insight}</li>`).join('')}
                </ul>
            </div>
        ` : ''}
        
        <!-- Writing Suggestions -->
        ${hasSuggestions && hasSuggestions.suggested_angles ? `
            <div class="writing-suggestions">
                <h6><i class="fas fa-pen"></i> اقتراحات الكتابة:</h6>
                <div class="row">
                    ${hasSuggestions.suggested_angles ? `
                        <div class="col-md-6">
                            <strong>زوايا مقترحة:</strong>
                            <ul class="small">
                                ${hasSuggestions.suggested_angles.map(angle => `<li>${angle}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                    ${hasSuggestions.key_quotes && hasSuggestions.key_quotes.length > 0 ? `
                        <div class="col-md-6">
                            <strong>اقتباسات مهمة:</strong>
                            <ul class="small">
                                ${hasSuggestions.key_quotes.map(quote => `<li>"${quote}"</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>
            </div>
        ` : ''}
        
        <!-- Full Content -->
        ${hasFullContent ? `
            <div class="mt-3">
                <h6><i class="fas fa-file-alt"></i> المحتوى الكامل للمقال:</h6>
                <div class="full-content-section">
                    <div class="small">${article.full_content}</div>
                </div>
            </div>
        ` : ''}
        
        <!-- Actions -->
        <div class="d-flex justify-content-between align-items-center mt-3">
            <a href="${article.url}" target="_blank" class="btn btn-outline-primary">
                <i class="fas fa-external-link-alt"></i> المصدر الأصلي
            </a>
            <div>
                <button onclick="copyFullArticle('${article.title}', '${article.url}', \`${hasFullContent ? article.full_content.replace(/`/g, '\\`') : article.content}\`)" 
                        class="btn btn-outline-success btn-sm me-2">
                    <i class="fas fa-copy"></i> نسخ المقال كاملاً
                </button>
                <button onclick="downloadArticle('${article.title}', \`${hasFullContent ? article.full_content.replace(/`/g, '\\`') : article.content}\`)" 
                        class="btn btn-outline-info btn-sm">
                    <i class="fas fa-download"></i> تحميل
                </button>
            </div>
        </div>
        
        <!-- Metadata -->
        <div class="mt-3 pt-3 border-top">
            <small class="text-muted">
                <i class="fas fa-calendar"></i> ${article.extracted_date || new Date(article.scraped_at).toLocaleDateString('ar')} |
                <i class="fas fa-globe"></i> ${article.source_engine} |
                <i class="fas fa-check-circle text-success"></i> تم التسليم بنجاح
            </small>
        </div>
    `;
    
    return card;
}

// Get agent statistics
async function getAgentStats() {
    const agentId = document.getElementById('agentId').value.trim() || 'default';
    
    try {
        const response = await fetch(`${API_BASE_URL}/news/professional/stats/${agentId}`);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const stats = await response.json();
        showStatsModal(stats);
        
    } catch (error) {
        console.error('Stats error:', error);
        showError('حدث خطأ أثناء جلب الإحصائيات: ' + error.message);
    }
}

// Show stats modal
function showStatsModal(stats) {
    const statsContent = document.getElementById('statsContent');
    
    statsContent.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>إحصائيات الوكيل: ${stats.agent_id}</h6>
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between">
                        <span>إجمالي المقالات المسلمة:</span>
                        <strong>${stats.total_articles_delivered}</strong>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span>مقالات آخر 24 ساعة:</span>
                        <strong>${stats.articles_last_24h}</strong>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span>آخر تسليم:</span>
                        <strong>${stats.last_delivery ? new Date(stats.last_delivery).toLocaleString('ar') : 'لا يوجد'}</strong>
                    </li>
                </ul>
                
                <h6 class="mt-3">الفئات المغطاة:</h6>
                <div>
                    ${stats.categories_covered.map(cat => 
                        `<span class="badge bg-secondary me-1">${cat}</span>`
                    ).join('')}
                </div>
            </div>
            <div class="col-md-6">
                <h6>الإحصائيات العامة:</h6>
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between">
                        <span>إجمالي المقالات في النظام:</span>
                        <strong>${stats.overall_stats.total_delivered}</strong>
                    </li>
                    <li class="list-group-item d-flex justify-content-between">
                        <span>مقالات حديثة (24 ساعة):</span>
                        <strong>${stats.overall_stats.recent_24h}</strong>
                    </li>
                </ul>
                
                <h6 class="mt-3">الوكلاء النشطون:</h6>
                <div>
                    ${Object.entries(stats.overall_stats.by_agent).map(([agent, count]) => 
                        `<span class="badge bg-info me-1">${agent}: ${count}</span>`
                    ).join('')}
                </div>
            </div>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('statsModal'));
    modal.show();
}

// Clear agent history
async function clearAgentHistory() {
    const agentId = document.getElementById('agentId').value.trim() || 'default';
    
    if (!confirm(`هل أنت متأكد من مسح تاريخ التسليم للوكيل "${agentId}"؟\nسيتمكن الوكيل من الحصول على المقالات المسلمة سابقاً مرة أخرى.`)) {
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE_URL}/news/professional/history/${agentId}`, {
            method: 'DELETE'
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        alert(result.message);
        
    } catch (error) {
        console.error('Clear history error:', error);
        showError('حدث خطأ أثناء مسح التاريخ: ' + error.message);
    }
}

// Show API guide
function showAPIGuide() {
    const modal = new bootstrap.Modal(document.getElementById('apiGuideModal'));
    modal.show();
}

// Copy full article
function copyFullArticle(title, url, content) {
    const fullText = `العنوان: ${title}\n\nالرابط: ${url}\n\nالمحتوى:\n${content}`;
    
    navigator.clipboard.writeText(fullText).then(() => {
        // Show temporary success message
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
        btn.classList.add('btn-success');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
        }, 2000);
    });
}

// Download article
function downloadArticle(title, content) {
    const blob = new Blob([`العنوان: ${title}\n\n${content}`], { type: 'text/plain;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title.substring(0, 50)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// Utility functions
function showLoading() { document.getElementById('loading').style.display = 'block'; }
function hideLoading() { document.getElementById('loading').style.display = 'none'; }
function showResults() { document.getElementById('results').style.display = 'block'; }
function hideResults() { document.getElementById('results').style.display = 'none'; }
function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('error').style.display = 'block';
}
function hideError() { document.getElementById('error').style.display = 'none'; }
