#!/usr/bin/env python3
"""
Quick run script for DeepSearch Agent
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config_loader import load_config
from src.utils.logger import setup_logging
from src.core.search_engine import DeepSearchEngine
from loguru import logger


async def quick_test():
    """Quick test of the system"""
    print("🔍 DeepSearch Agent - Quick Test")
    print("=" * 50)
    
    # Load configuration
    config = load_config()
    setup_logging(config)
    
    try:
        async with DeepSearchEngine(config) as engine:
            # Health check
            print("📋 Performing health check...")
            health = await engine.health_check()
            
            print(f"Overall Status: {health['overall_status']}")
            
            # Quick search test
            print("\n🔍 Testing search functionality...")
            result = await engine.search(
                query="Python programming",
                max_results=3,
                engines=["duckduckgo"]
            )
            
            print(f"Search Results: {result['total_results']} articles found")
            print(f"Processing Time: {result['processing_time']:.2f}s")
            
            if result['articles']:
                print("\nFirst article:")
                article = result['articles'][0]
                print(f"  Title: {article['title'][:100]}...")
                print(f"  URL: {article['url']}")
            
            print("\n✅ Quick test completed successfully!")
            
    except Exception as e:
        print(f"\n❌ Quick test failed: {str(e)}")
        logger.error(f"Quick test error: {str(e)}")


if __name__ == "__main__":
    asyncio.run(quick_test())
