"""
News API routes for AI agents
"""

from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from loguru import logger

from ..news.news_engine import NewsEngine
from ..utils.config_loader import load_config


# Pydantic models for news API
class NewsSearchRequest(BaseModel):
    """Request model for news search"""
    query: str = Field(..., description="News search query", min_length=1, max_length=200)
    max_results: Optional[int] = Field(10, description="Maximum number of news articles", ge=1, le=50)
    category: Optional[str] = Field(None, description="News category filter")
    hours_back: Optional[int] = Field(24, description="How many hours back to search", ge=1, le=168)


class ProfessionalArticleRequest(BaseModel):
    """Request model for professional articles"""
    query: str = Field(..., description="News search query", min_length=1, max_length=200)
    agent_id: Optional[str] = Field("default", description="Unique identifier for the requesting agent")
    max_articles: Optional[int] = Field(1, description="Number of articles (1 for single, max 3 for multiple)", ge=1, le=3)
    extract_full_content: Optional[bool] = Field(True, description="Whether to extract full article content")


class NewsArticle(BaseModel):
    """News article model"""
    title: str
    url: str
    news_summary: str
    content: str
    source_engine: str
    scraped_at: str
    news_score: float
    extracted_date: Optional[str] = None
    key_entities: List[str] = []
    news_type: str = "regular"
    has_news_summary: bool = False


class ProfessionalArticle(BaseModel):
    """Professional article model with full content"""
    title: str
    url: str
    news_summary: str
    content: str
    full_content: Optional[str] = None
    professional_summary: Optional[str] = None
    key_insights: List[str] = []
    writing_suggestions: Optional[Dict] = None
    source_engine: str
    scraped_at: str
    news_score: float
    selection_score: Optional[float] = None
    extracted_date: Optional[str] = None
    key_entities: List[str] = []
    news_type: str = "regular"
    has_full_content: bool = False
    professional_enhancement: bool = False
    word_count: Optional[int] = None
    estimated_reading_time: Optional[str] = None
    content_quality_score: Optional[float] = None


class NewsSearchResponse(BaseModel):
    """Response model for news search"""
    query: str
    enhanced_query: str
    news_articles: List[NewsArticle]
    total_results: int
    category: str
    search_time: str
    processing_time: float
    sources_used: List[str]
    error: Optional[str] = None


class NewsCategoriesResponse(BaseModel):
    """Response model for news categories"""
    categories: List[str]
    descriptions: dict


class TrendingNewsResponse(BaseModel):
    """Response model for trending news"""
    trending_topics: List[str]
    top_articles: List[NewsArticle]
    generated_at: str


class ProfessionalArticleResponse(BaseModel):
    """Response model for professional articles"""
    query: str
    agent_id: str
    professional_articles: List[ProfessionalArticle]
    total_found: int
    total_new: Optional[int] = None
    total_delivered: int
    processing_time: float
    extraction_enabled: bool
    delivery_time: str
    message: str
    error: Optional[str] = None


# Create router
router = APIRouter(prefix="/news", tags=["news"])

# Global news engine instance
news_engine: NewsEngine = None


async def get_news_engine() -> NewsEngine:
    """Dependency to get news engine instance"""
    global news_engine
    if news_engine is None:
        config = load_config()
        news_engine = NewsEngine(config)
    return news_engine


@router.post("/search", response_model=NewsSearchResponse)
async def search_news(
    request: NewsSearchRequest,
    engine: NewsEngine = Depends(get_news_engine)
):
    """
    Search for latest news articles
    
    This endpoint searches for the most recent news articles on a specific topic.
    Perfect for AI agents that need up-to-date information.
    
    Example queries:
    - "آخر أخبار كرة القدم"
    - "أخبار البيتكوين اليوم"
    - "تطورات الحرب في أوكرانيا"
    - "أخبار التكنولوجيا"
    """
    try:
        async with engine:
            result = await engine.search_latest_news(
                query=request.query,
                max_results=request.max_results,
                category=request.category
            )
        
        return NewsSearchResponse(**result)
        
    except Exception as e:
        logger.error(f"News search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/categories", response_model=NewsCategoriesResponse)
async def get_news_categories():
    """
    Get available news categories
    
    Returns the list of supported news categories for better search targeting.
    """
    categories = [
        "sports", "technology", "crypto", "politics", 
        "economy", "health", "war", "general"
    ]
    
    descriptions = {
        "sports": "أخبار الرياضة وكرة القدم والبطولات",
        "technology": "أخبار التكنولوجيا والذكاء الاصطناعي",
        "crypto": "أخبار العملات الرقمية والبيتكوين",
        "politics": "الأخبار السياسية والحكومية",
        "economy": "الأخبار الاقتصادية والمالية",
        "health": "أخبار الصحة والطب",
        "war": "أخبار الحروب والصراعات",
        "general": "الأخبار العامة"
    }
    
    return NewsCategoriesResponse(
        categories=categories,
        descriptions=descriptions
    )


@router.get("/trending", response_model=TrendingNewsResponse)
async def get_trending_news(
    limit: int = 10,
    engine: NewsEngine = Depends(get_news_engine)
):
    """
    Get trending news topics
    
    Returns the most trending news topics and articles based on recent searches.
    """
    try:
        # This is a simplified version - can be enhanced with proper trending analysis
        trending_topics = [
            "كرة القدم",
            "الذكاء الاصطناعي", 
            "البيتكوين",
            "الحرب في أوكرانيا",
            "التكنولوجيا"
        ]
        
        # Get some recent articles for trending topics
        async with engine:
            result = await engine.search_latest_news(
                query="أخبار عاجلة اليوم",
                max_results=limit
            )
        
        return TrendingNewsResponse(
            trending_topics=trending_topics,
            top_articles=result.get('news_articles', []),
            generated_at=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Failed to get trending news: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/football", response_model=NewsSearchResponse)
async def search_football_news(
    max_results: int = 10,
    engine: NewsEngine = Depends(get_news_engine)
):
    """
    Get latest football/soccer news
    
    Specialized endpoint for football news - perfect for sports AI agents.
    """
    try:
        async with engine:
            result = await engine.search_latest_news(
                query="آخر أخبار كرة القدم اليوم",
                max_results=max_results,
                category="sports"
            )
        
        return NewsSearchResponse(**result)
        
    except Exception as e:
        logger.error(f"Football news search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/crypto", response_model=NewsSearchResponse)
async def search_crypto_news(
    max_results: int = 10,
    engine: NewsEngine = Depends(get_news_engine)
):
    """
    Get latest cryptocurrency news
    
    Specialized endpoint for crypto news - perfect for financial AI agents.
    """
    try:
        async with engine:
            result = await engine.search_latest_news(
                query="آخر أخبار البيتكوين والعملات الرقمية",
                max_results=max_results,
                category="crypto"
            )
        
        return NewsSearchResponse(**result)
        
    except Exception as e:
        logger.error(f"Crypto news search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/technology", response_model=NewsSearchResponse)
async def search_tech_news(
    max_results: int = 10,
    engine: NewsEngine = Depends(get_news_engine)
):
    """
    Get latest technology news
    
    Specialized endpoint for technology news - perfect for tech AI agents.
    """
    try:
        async with engine:
            result = await engine.search_latest_news(
                query="آخر أخبار التكنولوجيا والذكاء الاصطناعي",
                max_results=max_results,
                category="technology"
            )
        
        return NewsSearchResponse(**result)
        
    except Exception as e:
        logger.error(f"Technology news search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/war", response_model=NewsSearchResponse)
async def search_war_news(
    max_results: int = 10,
    engine: NewsEngine = Depends(get_news_engine)
):
    """
    Get latest war and conflict news
    
    Specialized endpoint for war/conflict news - for geopolitical AI agents.
    """
    try:
        async with engine:
            result = await engine.search_latest_news(
                query="آخر أخبار الحروب والصراعات",
                max_results=max_results,
                category="war"
            )
        
        return NewsSearchResponse(**result)
        
    except Exception as e:
        logger.error(f"War news search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/professional", response_model=ProfessionalArticleResponse)
async def get_professional_articles(
    request: ProfessionalArticleRequest,
    engine: NewsEngine = Depends(get_news_engine)
):
    """
    Get professional articles with full content for AI agents

    This endpoint is specifically designed for AI agents that need complete article content
    for professional writing and analysis. Features:

    - Returns 1 article by default (professional single-article mode)
    - Maximum 3 articles when multiple requested
    - Full content extraction from original sources
    - Professional summaries and writing suggestions
    - Automatic duplicate prevention (tracks delivered articles)
    - Enhanced metadata and insights

    Perfect for AI agents that need to:
    - Write professional articles based on news
    - Analyze complete news content
    - Get comprehensive information without duplicates

    Example usage:
    ```json
    {
        "query": "آخر أخبار كرة القدم",
        "agent_id": "sports_writer_bot",
        "max_articles": 1,
        "extract_full_content": true
    }
    ```
    """
    try:
        async with engine:
            result = await engine.get_professional_articles(
                query=request.query,
                agent_id=request.agent_id,
                max_articles=request.max_articles,
                extract_full_content=request.extract_full_content
            )

        return ProfessionalArticleResponse(**result)

    except Exception as e:
        logger.error(f"Professional articles request failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/professional/single")
async def get_single_professional_article(
    query: str,
    agent_id: str = "default",
    extract_full_content: bool = True,
    engine: NewsEngine = Depends(get_news_engine)
):
    """
    Get a single professional article (simplified endpoint)

    This is a simplified endpoint that always returns exactly 1 professional article.
    Perfect for AI agents that need just one high-quality article with full content.

    Query parameters:
    - query: The news topic to search for
    - agent_id: Unique identifier for your AI agent
    - extract_full_content: Whether to extract full article content (default: true)

    Example: GET /news/professional/single?query=آخر أخبار البيتكوين&agent_id=crypto_bot
    """
    try:
        async with engine:
            result = await engine.get_professional_articles(
                query=query,
                agent_id=agent_id,
                max_articles=1,
                extract_full_content=extract_full_content
            )

        return result

    except Exception as e:
        logger.error(f"Single professional article request failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/professional/stats/{agent_id}")
async def get_agent_delivery_stats(
    agent_id: str,
    engine: NewsEngine = Depends(get_news_engine)
):
    """
    Get delivery statistics for a specific AI agent

    Returns information about articles delivered to the specified agent,
    helping track what content has been provided and avoid duplicates.
    """
    try:
        async with engine:
            stats = engine.article_tracker.get_delivery_stats()

            # Filter stats for specific agent
            agent_articles = {
                k: v for k, v in engine.article_tracker.delivered_articles.items()
                if v.get('agent_id') == agent_id
            }

            agent_stats = {
                'agent_id': agent_id,
                'total_articles_delivered': len(agent_articles),
                'articles_last_24h': len([
                    v for v in agent_articles.values()
                    if (datetime.now() - datetime.fromisoformat(v['delivered_at'])).days < 1
                ]),
                'categories_covered': list(set(v.get('category', 'general') for v in agent_articles.values())),
                'last_delivery': max([v.get('delivered_at', '') for v in agent_articles.values()]) if agent_articles else None,
                'overall_stats': stats
            }

            return agent_stats

    except Exception as e:
        logger.error(f"Failed to get agent stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/professional/history/{agent_id}")
async def clear_agent_history(
    agent_id: str,
    engine: NewsEngine = Depends(get_news_engine)
):
    """
    Clear delivery history for a specific AI agent

    This allows the agent to receive previously delivered articles again.
    Use with caution as it will reset the duplicate prevention for this agent.
    """
    try:
        async with engine:
            engine.article_tracker.clear_history(agent_id)

            return {
                "message": f"تم مسح تاريخ التسليم للوكيل {agent_id}",
                "agent_id": agent_id,
                "cleared_at": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Failed to clear agent history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
