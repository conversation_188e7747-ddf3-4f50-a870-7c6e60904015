# دليل التثبيت - DeepSearch Agent

## المتطلبات الأساسية

### متطلبات النظام
- Python 3.9 أو أحدث
- 4 GB RAM (الحد الأدنى)
- 10 GB مساحة تخزين
- اتصال بالإنترنت

### المتطلبات الاختيارية
- Ollama (للذكاء الاصطناعي المحلي)
- Docker (للنشر)
- NVIDIA GPU (لتسريع الذكاء الاصطناعي)

## طرق التثبيت

### 1. التثبيت المحلي

#### الخطوة 1: استنساخ المشروع
```bash
git clone <repository-url>
cd deep-search
```

#### الخطوة 2: إنشاء بيئة افتراضية
```bash
python -m venv venv

# Linux/Mac
source venv/bin/activate

# Windows
venv\Scripts\activate
```

#### الخطوة 3: تشغيل سكريبت الإعداد
```bash
python scripts/setup.py
```

#### الخطوة 4: تكوين متغيرات البيئة
```bash
cp .env.example .env
# قم بتحرير .env وإضافة مفاتيح API الخاصة بك
```

#### الخطوة 5: اختبار التثبيت
```bash
python scripts/run_tests.py --unit
```

### 2. التثبيت باستخدام Docker

#### الخطوة 1: تشغيل باستخدام Docker Compose
```bash
docker-compose up -d
```

#### الخطوة 2: التحقق من الحالة
```bash
docker-compose ps
curl http://localhost:8000/health
```

### 3. التثبيت اليدوي

#### الخطوة 1: تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### الخطوة 2: إنشاء المجلدات
```bash
mkdir -p data logs data/vector_db
```

#### الخطوة 3: تكوين قاعدة البيانات
```bash
python -c "from src.database.database_manager import DatabaseManager; from src.utils.config_loader import load_config; DatabaseManager(load_config())"
```

## إعداد Ollama (اختياري)

### تثبيت Ollama
```bash
# Linux/Mac
curl -fsSL https://ollama.ai/install.sh | sh

# Windows
# قم بتحميل المثبت من https://ollama.ai
```

### تحميل نموذج Mistral
```bash
ollama pull mistral
ollama pull nomic-embed-text  # للتضمينات
```

### التحقق من التثبيت
```bash
ollama list
curl http://localhost:11434/api/tags
```

## إعداد مفاتيح API

### Brave Search API
1. سجل في [Brave Search API](https://api.search.brave.com)
2. احصل على مفتاح API
3. أضف المفتاح في `.env`:
```bash
BRAVE_API_KEY=your_brave_api_key_here
```

### OpenAI API (اختياري)
```bash
OPENAI_API_KEY=your_openai_api_key_here
```

## التحقق من التثبيت

### اختبار سريع
```bash
python main.py --health
```

### اختبار البحث
```bash
python main.py --query "آخر أخبار التكنولوجيا"
```

### اختبار API
```bash
python main.py --api &
curl -X POST "http://localhost:8000/search" \
     -H "Content-Type: application/json" \
     -d '{"query": "artificial intelligence", "max_results": 5}'
```

## استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في تثبيت المتطلبات
```bash
# تحديث pip
pip install --upgrade pip

# تثبيت أدوات البناء
# Ubuntu/Debian
sudo apt-get install build-essential python3-dev

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install python3-devel
```

#### مشاكل في Ollama
```bash
# التحقق من حالة Ollama
ollama serve

# إعادة تشغيل Ollama
sudo systemctl restart ollama
```

#### مشاكل في قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm -f data/deepsearch.db
python -c "from src.database.database_manager import DatabaseManager; from src.utils.config_loader import load_config; DatabaseManager(load_config())"
```

### ملفات السجل
```bash
# عرض السجلات
tail -f logs/deepsearch.log

# سجلات Docker
docker-compose logs -f deepsearch
```

## التحديث

### تحديث محلي
```bash
git pull origin main
pip install -r requirements.txt --upgrade
python scripts/setup.py
```

### تحديث Docker
```bash
docker-compose pull
docker-compose up -d --force-recreate
```

## إلغاء التثبيت

### إلغاء التثبيت المحلي
```bash
# إيقاف الخدمات
pkill -f "python main.py"

# حذف البيئة الافتراضية
rm -rf venv

# حذف البيانات (اختياري)
rm -rf data logs
```

### إلغاء تثبيت Docker
```bash
docker-compose down -v
docker rmi $(docker images -q "*deepsearch*")
```
