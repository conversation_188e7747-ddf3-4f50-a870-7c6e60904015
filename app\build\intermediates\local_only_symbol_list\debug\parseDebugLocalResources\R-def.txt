R_DEF: Internal format may change without notice
local
color accent_color
color accent_dark
color ad_background
color ad_border
color analysis_news_color
color background_color
color background_dark
color black
color border_dark
color border_light
color breaking_news_color
color button_disabled
color button_primary
color button_secondary
color category_addon
color category_map
color category_shader
color category_skin
color category_texture
color error_color
color error_light
color gradient_end
color gradient_start
color info_color
color info_light
color interview_news_color
color overlay_dark
color overlay_light
color primary_color
color primary_dark
color primary_light
color professional_article_bg
color purple_200
color purple_500
color purple_700
color rating_empty
color rating_star
color regular_article_bg
color regular_news_color
color secondary_color
color secondary_dark
color secondary_light
color success_color
color success_light
color surface_color
color teal_200
color teal_700
color text_hint
color text_on_primary
color text_primary
color text_secondary
color text_tertiary
color warning_color
color warning_light
color white
drawable category_background
drawable ic_gaming_placeholder
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_open_in_browser
drawable ic_search
drawable ic_share
drawable image_placeholder_background
drawable professional_article_background
drawable regular_article_background
drawable rounded_background
drawable score_background
id action_open_browser
id action_share
id splash_app_name
id splash_description
id splash_logo
id webView
layout activity_splash
layout activity_web_main
menu article_detail_menu
mipmap ic_launcher
mipmap ic_launcher_round
string about
string ad_failed
string ad_loading
string admob_app_id
string admob_banner_id
string admob_interstitial_id
string admob_rewarded_id
string app_description
string app_name
string category_all
string category_new
string category_popular
string category_updated
string compatible_versions
string download_completed
string download_free
string download_now
string download_premium
string download_starting
string error_occurred
string grant_permission
string loading_message
string minecraft_version
string mod_author
string mod_date
string mod_details
string mod_downloads
string mod_rating
string mod_size
string mod_type_addon
string mod_type_map
string mod_type_shader
string mod_type_skin
string mod_type_texture
string no_internet_message
string no_internet_title
string no_thanks
string permission_required
string please_wait
string privacy_policy
string rate_app
string rate_later
string rate_message
string rate_now
string retry
string reward_granted
string settings
string share_app
string share_message
string share_mod
string storage_permission_message
string terms_of_service
string update_available
string update_later
string update_message
string update_now
string watch_ad_for_reward
style Theme.Modetaris
style Theme.Modetaris.NoActionBar
style Theme.Modetaris.Splash
style Theme.MyApplication
xml backup_rules
xml data_extraction_rules
xml network_security_config
