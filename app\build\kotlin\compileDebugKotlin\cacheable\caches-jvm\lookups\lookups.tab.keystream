  fade_in android.R.anim  fade_out android.R.anim  SuppressLint android.annotation  	Exception android.app.Activity  Handler android.app.Activity  	ImageView android.app.Activity  Intent android.app.Activity  Looper android.app.Activity  MainActivity android.app.Activity  R android.app.Activity  SPLASH_DURATION android.app.Activity  String android.app.Activity  TextView android.app.Activity  Toast android.app.Activity  WebView android.app.Activity  
WebViewClient android.app.Activity  android android.app.Activity  apply android.app.Activity  finish android.app.Activity  java android.app.Activity  onCreate android.app.Activity  overridePendingTransition android.app.Activity  
startActivity android.app.Activity  
trimIndent android.app.Activity  ClipData android.content  ClipboardManager android.content  Context android.content  Intent android.content  	Exception android.content.Context  Handler android.content.Context  	ImageView android.content.Context  Intent android.content.Context  Looper android.content.Context  MainActivity android.content.Context  R android.content.Context  SPLASH_DURATION android.content.Context  String android.content.Context  TextView android.content.Context  Toast android.content.Context  WebView android.content.Context  
WebViewClient android.content.Context  android android.content.Context  apply android.content.Context  java android.content.Context  
trimIndent android.content.Context  	Exception android.content.ContextWrapper  Handler android.content.ContextWrapper  	ImageView android.content.ContextWrapper  Intent android.content.ContextWrapper  Looper android.content.ContextWrapper  MainActivity android.content.ContextWrapper  R android.content.ContextWrapper  SPLASH_DURATION android.content.ContextWrapper  String android.content.ContextWrapper  TextView android.content.ContextWrapper  Toast android.content.ContextWrapper  WebView android.content.ContextWrapper  
WebViewClient android.content.ContextWrapper  android android.content.ContextWrapper  apply android.content.ContextWrapper  java android.content.ContextWrapper  
trimIndent android.content.ContextWrapper  Bundle 
android.os  Handler 
android.os  Looper 
android.os  postDelayed android.os.Handler  
getMainLooper android.os.Looper  ViewPropertyAnimator android.view  	Exception  android.view.ContextThemeWrapper  Handler  android.view.ContextThemeWrapper  	ImageView  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  Looper  android.view.ContextThemeWrapper  MainActivity  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  SPLASH_DURATION  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  TextView  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  WebView  android.view.ContextThemeWrapper  
WebViewClient  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  
trimIndent  android.view.ContextThemeWrapper  alpha android.view.View  animate android.view.View  alpha !android.view.ViewPropertyAnimator  setDuration !android.view.ViewPropertyAnimator  start !android.view.ViewPropertyAnimator  AnimationUtils android.view.animation  AppCompatActivity android.webkit  Bundle android.webkit  	Exception android.webkit  R android.webkit  String android.webkit  SuppressLint android.webkit  Toast android.webkit  WebSettings android.webkit  WebView android.webkit  
WebViewClient android.webkit  apply android.webkit  
trimIndent android.webkit  allowContentAccess android.webkit.WebSettings  allowFileAccess android.webkit.WebSettings  apply android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  	canGoBack android.webkit.WebView  goBack android.webkit.WebView  loadDataWithBaseURL android.webkit.WebView  settings android.webkit.WebView  
webViewClient android.webkit.WebView  onPageFinished android.webkit.WebViewClient  	ImageView android.widget  TextView android.widget  Toast android.widget  LENGTH_LONG android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  	Exception #androidx.activity.ComponentActivity  Handler #androidx.activity.ComponentActivity  	ImageView #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  Looper #androidx.activity.ComponentActivity  MainActivity #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  SPLASH_DURATION #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  TextView #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  WebView #androidx.activity.ComponentActivity  
WebViewClient #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  
onBackPressed #androidx.activity.ComponentActivity  
trimIndent #androidx.activity.ComponentActivity  AppCompatActivity androidx.appcompat.app  	Exception (androidx.appcompat.app.AppCompatActivity  Handler (androidx.appcompat.app.AppCompatActivity  	ImageView (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  Looper (androidx.appcompat.app.AppCompatActivity  MainActivity (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  SPLASH_DURATION (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  TextView (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  WebView (androidx.appcompat.app.AppCompatActivity  
WebViewClient (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  
onBackPressed (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  
trimIndent (androidx.appcompat.app.AppCompatActivity  Bundle #androidx.core.app.ComponentActivity  
Deprecated #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  Handler #androidx.core.app.ComponentActivity  	ImageView #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  Looper #androidx.core.app.ComponentActivity  MainActivity #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  SPLASH_DURATION #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  SuppressLint #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  WebView #androidx.core.app.ComponentActivity  
WebViewClient #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  
trimIndent #androidx.core.app.ComponentActivity  	Exception &androidx.fragment.app.FragmentActivity  Handler &androidx.fragment.app.FragmentActivity  	ImageView &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  Looper &androidx.fragment.app.FragmentActivity  MainActivity &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  SPLASH_DURATION &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  TextView &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  WebView &androidx.fragment.app.FragmentActivity  
WebViewClient &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  
trimIndent &androidx.fragment.app.FragmentActivity  AppCompatActivity com.modetaris.gamingnews  Bundle com.modetaris.gamingnews  
Deprecated com.modetaris.gamingnews  	Exception com.modetaris.gamingnews  Handler com.modetaris.gamingnews  	ImageView com.modetaris.gamingnews  Intent com.modetaris.gamingnews  Looper com.modetaris.gamingnews  MainActivity com.modetaris.gamingnews  R com.modetaris.gamingnews  SPLASH_DURATION com.modetaris.gamingnews  SplashActivity com.modetaris.gamingnews  String com.modetaris.gamingnews  SuppressLint com.modetaris.gamingnews  TextView com.modetaris.gamingnews  Toast com.modetaris.gamingnews  WebView com.modetaris.gamingnews  
WebViewClient com.modetaris.gamingnews  android com.modetaris.gamingnews  apply com.modetaris.gamingnews  java com.modetaris.gamingnews  
trimIndent com.modetaris.gamingnews  R %com.modetaris.gamingnews.MainActivity  Toast %com.modetaris.gamingnews.MainActivity  apply %com.modetaris.gamingnews.MainActivity  findViewById %com.modetaris.gamingnews.MainActivity  finish %com.modetaris.gamingnews.MainActivity  loadWebInterface %com.modetaris.gamingnews.MainActivity  setContentView %com.modetaris.gamingnews.MainActivity  setupWebView %com.modetaris.gamingnews.MainActivity  
trimIndent %com.modetaris.gamingnews.MainActivity  webView %com.modetaris.gamingnews.MainActivity  splash_app_name com.modetaris.gamingnews.R.id  splash_description com.modetaris.gamingnews.R.id  splash_logo com.modetaris.gamingnews.R.id  webView com.modetaris.gamingnews.R.id  activity_splash !com.modetaris.gamingnews.R.layout  activity_web_main !com.modetaris.gamingnews.R.layout  Bundle 'com.modetaris.gamingnews.SplashActivity  
Deprecated 'com.modetaris.gamingnews.SplashActivity  	Exception 'com.modetaris.gamingnews.SplashActivity  Handler 'com.modetaris.gamingnews.SplashActivity  	ImageView 'com.modetaris.gamingnews.SplashActivity  Intent 'com.modetaris.gamingnews.SplashActivity  Looper 'com.modetaris.gamingnews.SplashActivity  MainActivity 'com.modetaris.gamingnews.SplashActivity  R 'com.modetaris.gamingnews.SplashActivity  SPLASH_DURATION 'com.modetaris.gamingnews.SplashActivity  TextView 'com.modetaris.gamingnews.SplashActivity  android 'com.modetaris.gamingnews.SplashActivity  findViewById 'com.modetaris.gamingnews.SplashActivity  finish 'com.modetaris.gamingnews.SplashActivity  java 'com.modetaris.gamingnews.SplashActivity  overridePendingTransition 'com.modetaris.gamingnews.SplashActivity  setContentView 'com.modetaris.gamingnews.SplashActivity  setupAnimations 'com.modetaris.gamingnews.SplashActivity  
startActivity 'com.modetaris.gamingnews.SplashActivity  Handler 1com.modetaris.gamingnews.SplashActivity.Companion  Intent 1com.modetaris.gamingnews.SplashActivity.Companion  Looper 1com.modetaris.gamingnews.SplashActivity.Companion  MainActivity 1com.modetaris.gamingnews.SplashActivity.Companion  R 1com.modetaris.gamingnews.SplashActivity.Companion  SPLASH_DURATION 1com.modetaris.gamingnews.SplashActivity.Companion  android 1com.modetaris.gamingnews.SplashActivity.Companion  java 1com.modetaris.gamingnews.SplashActivity.Companion  ArticleRequest #com.modetaris.gamingnews.data.model  Int #com.modetaris.gamingnews.data.model  
SimpleArticle #com.modetaris.gamingnews.data.model  String #com.modetaris.gamingnews.data.model  content 1com.modetaris.gamingnews.data.model.SimpleArticle  qualityScore 1com.modetaris.gamingnews.data.model.SimpleArticle  readingTime 1com.modetaris.gamingnews.data.model.SimpleArticle  title 1com.modetaris.gamingnews.data.model.SimpleArticle  	wordCount 1com.modetaris.gamingnews.data.model.SimpleArticle  
SimpleArticle com.modetaris.gamingnews.utils  SimpleHtmlGenerator com.modetaris.gamingnews.utils  String com.modetaris.gamingnews.utils  replace com.modetaris.gamingnews.utils  
trimIndent com.modetaris.gamingnews.utils  replace 2com.modetaris.gamingnews.utils.SimpleHtmlGenerator  
trimIndent 2com.modetaris.gamingnews.utils.SimpleHtmlGenerator  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  
Deprecated kotlin  	Function0 kotlin  	Function1 kotlin  apply kotlin  replace 
kotlin.String  
trimIndent 
kotlin.String  message kotlin.Throwable  java 
kotlin.jvm  java kotlin.reflect.KClass  replace kotlin.text  
trimIndent kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               