<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSearch Professional Articles - مقالات احترافية للوكلاء البرمجيين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1400px;
            padding: 30px;
        }
        
        .professional-badge {
            background: linear-gradient(45deg, #6f42c1, #e83e8c);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .article-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-left: 5px solid #6f42c1;
        }
        
        .full-content-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .professional-summary {
            background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #6f42c1;
        }
        
        .insights-section {
            background: #fff3e0;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .writing-suggestions {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .agent-info {
            background: #6f42c1;
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            display: inline-block;
            margin-bottom: 15px;
        }
        
        .content-stats {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        
        .stat-item {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .no-duplicates-badge {
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="text-center mb-4">
                <div class="professional-badge">
                    <i class="fas fa-graduation-cap"></i> مقالات احترافية للوكلاء البرمجيين
                </div>
                <h1 class="display-5" style="color: #6f42c1;">
                    <i class="fas fa-robot"></i>
                    DeepSearch Professional Articles
                </h1>
                <p class="lead text-muted">مقالات كاملة مع محتوى احترافي ومنع التكرار</p>
            </div>

            <!-- Agent Configuration -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6><i class="fas fa-cog"></i> إعدادات الوكيل البرمجي</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="agentId" class="form-label">معرف الوكيل</label>
                            <input type="text" id="agentId" class="form-control" 
                                   placeholder="مثال: sports_writer_bot" value="my_news_agent">
                        </div>
                        <div class="col-md-4">
                            <label for="articleCount" class="form-label">عدد المقالات</label>
                            <select id="articleCount" class="form-select">
                                <option value="1" selected>مقال واحد (موصى)</option>
                                <option value="2">مقالان</option>
                                <option value="3">3 مقالات (الحد الأقصى)</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="extractFullContent" checked>
                                <label class="form-check-label" for="extractFullContent">
                                    استخراج المحتوى الكامل
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Box -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <input type="text" id="professionalQuery" class="form-control" 
                                   placeholder="ابحث عن مقال احترافي... (مثال: آخر أخبار الذكاء الاصطناعي)">
                        </div>
                        <div class="col-md-4">
                            <button onclick="getProfessionalArticles()" class="btn w-100" 
                                    style="background: linear-gradient(45deg, #6f42c1, #e83e8c); color: white;">
                                <i class="fas fa-search"></i> احصل على مقال احترافي
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="text-center mb-4">
                <button class="btn btn-outline-primary me-2" onclick="getAgentStats()">
                    <i class="fas fa-chart-bar"></i> إحصائيات الوكيل
                </button>
                <button class="btn btn-outline-warning me-2" onclick="clearAgentHistory()">
                    <i class="fas fa-trash"></i> مسح التاريخ
                </button>
                <button class="btn btn-outline-info" onclick="showAPIGuide()">
                    <i class="fas fa-code"></i> دليل API
                </button>
            </div>

            <!-- Loading -->
            <div id="loading" class="text-center" style="display: none;">
                <div class="spinner-border" style="color: #6f42c1;" role="status"></div>
                <p class="mt-2">جاري الحصول على المقال الاحترافي...</p>
            </div>

            <!-- Results -->
            <div id="results" style="display: none;">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-newspaper"></i> المقالات الاحترافية</h5>
                    <div id="deliveryInfo" class="text-muted"></div>
                </div>
                <div id="articlesContainer"></div>
            </div>

            <!-- Stats Modal -->
            <div class="modal fade" id="statsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إحصائيات الوكيل</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="statsContent">
                            <!-- Stats will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Guide Modal -->
            <div class="modal fade" id="apiGuideModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">دليل API للوكلاء البرمجيين</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>الحصول على مقال واحد:</h6>
                                    <pre class="bg-light p-3 rounded"><code>GET /news/professional/single?query=آخر أخبار كرة القدم&agent_id=sports_bot</code></pre>
                                    
                                    <h6>الحصول على عدة مقالات:</h6>
                                    <pre class="bg-light p-3 rounded"><code>POST /news/professional
{
  "query": "أخبار التكنولوجيا",
  "agent_id": "tech_writer",
  "max_articles": 3,
  "extract_full_content": true
}</code></pre>
                                </div>
                                <div class="col-md-6">
                                    <h6>إحصائيات الوكيل:</h6>
                                    <pre class="bg-light p-3 rounded"><code>GET /news/professional/stats/my_agent</code></pre>
                                    
                                    <h6>مسح تاريخ الوكيل:</h6>
                                    <pre class="bg-light p-3 rounded"><code>DELETE /news/professional/history/my_agent</code></pre>
                                    
                                    <h6>مثال Python:</h6>
                                    <pre class="bg-light p-3 rounded"><code>import requests

response = requests.get(
    "http://localhost:8000/news/professional/single",
    params={
        "query": "آخر أخبار البيتكوين",
        "agent_id": "crypto_bot"
    }
)

article = response.json()
print(article['professional_articles'][0]['full_content'])</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error -->
            <div id="error" class="alert alert-danger" style="display: none;">
                <h6><i class="fas fa-exclamation-triangle"></i> خطأ</h6>
                <p id="errorMessage"></p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="professional.js"></script>
</body>
</html>
