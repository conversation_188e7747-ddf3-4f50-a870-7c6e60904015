"""
Base AI provider class for different AI services
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import numpy as np


class BaseAIProvider(ABC):
    """Base class for AI providers"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model = config.get('model', 'mistral:latest')
        self.temperature = config.get('temperature', 0.7)
        self.max_tokens = config.get('max_tokens', 2000)
    
    @abstractmethod
    async def generate_text(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Generate text using the AI model
        
        Args:
            prompt: User prompt
            system_prompt: System prompt (optional)
            **kwargs: Additional parameters
            
        Returns:
            Generated text
        """
        pass
    
    @abstractmethod
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for texts
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        pass
    
    async def summarize_text(
        self, 
        text: str, 
        max_length: int = 500,
        language: str = "ar"
    ) -> str:
        """
        Summarize text content
        
        Args:
            text: Text to summarize
            max_length: Maximum summary length
            language: Target language
            
        Returns:
            Summarized text
        """
        if language == "ar":
            system_prompt = """أنت مساعد ذكي متخصص في تلخيص النصوص العربية. 
            مهمتك هي تلخيص النص المعطى بشكل دقيق ومفيد مع الحفاظ على المعلومات الأساسية."""
            
            prompt = f"""يرجى تلخيص النص التالي في حوالي {max_length} حرف:

النص:
{text}

التلخيص:"""
        else:
            system_prompt = """You are an intelligent assistant specialized in text summarization.
            Your task is to summarize the given text accurately and usefully while preserving key information."""
            
            prompt = f"""Please summarize the following text in approximately {max_length} characters:

Text:
{text}

Summary:"""
        
        return await self.generate_text(prompt, system_prompt)
    
    async def enhance_query(self, query: str, language: str = "ar") -> str:
        """
        Enhance search query for better results
        
        Args:
            query: Original query
            language: Query language
            
        Returns:
            Enhanced query
        """
        if language == "ar":
            system_prompt = """أنت مساعد ذكي متخصص في تحسين استعلامات البحث.
            مهمتك هي إعادة صياغة الاستعلام ليكون أكثر فعالية في البحث."""
            
            prompt = f"""يرجى تحسين استعلام البحث التالي ليكون أكثر دقة وفعالية:

الاستعلام الأصلي: {query}

الاستعلام المحسن:"""
        else:
            system_prompt = """You are an intelligent assistant specialized in improving search queries.
            Your task is to rephrase the query to be more effective for search."""
            
            prompt = f"""Please improve the following search query to be more accurate and effective:

Original query: {query}

Improved query:"""
        
        enhanced = await self.generate_text(prompt, system_prompt)
        return enhanced.strip()
    
    async def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """
        Extract keywords from text
        
        Args:
            text: Text to extract keywords from
            max_keywords: Maximum number of keywords
            
        Returns:
            List of keywords
        """
        system_prompt = """أنت مساعد ذكي متخصص في استخراج الكلمات المفتاحية.
        مهمتك هي استخراج أهم الكلمات المفتاحية من النص."""
        
        prompt = f"""يرجى استخراج أهم {max_keywords} كلمات مفتاحية من النص التالي:

النص:
{text}

الكلمات المفتاحية (مفصولة بفواصل):"""
        
        keywords_text = await self.generate_text(prompt, system_prompt)
        keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]
        return keywords[:max_keywords]
    
    async def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """
        Analyze sentiment of text
        
        Args:
            text: Text to analyze
            
        Returns:
            Sentiment analysis results
        """
        system_prompt = """أنت مساعد ذكي متخصص في تحليل المشاعر.
        مهمتك هي تحليل النص وتحديد المشاعر السائدة فيه."""
        
        prompt = f"""يرجى تحليل المشاعر في النص التالي وتحديد:
1. المشاعر العامة (إيجابية/سلبية/محايدة)
2. درجة الثقة (من 0 إلى 1)
3. المشاعر الفرعية إن وجدت

النص:
{text}

التحليل:"""
        
        analysis = await self.generate_text(prompt, system_prompt)
        
        # Parse the analysis (simplified)
        sentiment = "محايدة"
        confidence = 0.5
        
        if "إيجابية" in analysis:
            sentiment = "إيجابية"
            confidence = 0.8
        elif "سلبية" in analysis:
            sentiment = "سلبية"
            confidence = 0.8
        
        return {
            "sentiment": sentiment,
            "confidence": confidence,
            "analysis": analysis
        }
