{"logs": [{"outputFile": "com.modetaris.gamingnews.app-mergeDebugResources-42:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b28f7da774e38c14e96bef101d6db4b2\\transformed\\core-1.16.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "38,39,40,41,42,43,44,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3547,3650,3754,3857,3959,4064,4170,14363", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "3645,3749,3852,3954,4059,4165,4284,14459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c7be0c72e4429312e422d4fb96d3bf68\\transformed\\play-services-base-18.0.0\\res\\values-my\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,586,693,836,964,1083,1189,1361,1463,1629,1768,1922,2105,2171,2240", "endColumns": "102,159,129,106,142,127,118,105,171,101,165,138,153,182,65,68,84", "endOffsets": "295,455,585,692,835,963,1082,1188,1360,1462,1628,1767,1921,2104,2170,2239,2324"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4609,4716,4880,5014,5125,5272,5404,5527,5791,5967,6073,6243,6386,6544,6731,6801,6874", "endColumns": "106,163,133,110,146,131,122,109,175,105,169,142,157,186,69,72,88", "endOffsets": "4711,4875,5009,5120,5267,5399,5522,5632,5962,6068,6238,6381,6539,6726,6796,6869,6958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\69c40aec708a7733e482ec325600ccb6\\transformed\\play-services-basement-18.2.0\\res\\values-my\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "149", "endOffsets": "344"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5637", "endColumns": "153", "endOffsets": "5786"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d64a33053620fe7b1a00c329a8dfc346\\transformed\\material-1.12.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,386,485,561,652,736,842,971,1056,1119,1184,1274,1349,1408,1499,1562,1627,1686,1757,1819,1876,1995,2053,2114,2169,2242,2374,2465,2549,2649,2735,2824,2965,3043,3120,3243,3335,3412,3470,3521,3587,3659,3741,3812,3890,3965,4039,4111,4190,4298,4395,4476,4562,4654,4728,4807,4893,4947,5023,5091,5174,5255,5317,5381,5444,5512,5624,5735,5839,5952,6013,6068,6150,6237,6317", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,100,98,75,90,83,105,128,84,62,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,83,99,85,88,140,77,76,122,91,76,57,50,65,71,81,70,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81,86,79,77", "endOffsets": "280,381,480,556,647,731,837,966,1051,1114,1179,1269,1344,1403,1494,1557,1622,1681,1752,1814,1871,1990,2048,2109,2164,2237,2369,2460,2544,2644,2730,2819,2960,3038,3115,3238,3330,3407,3465,3516,3582,3654,3736,3807,3885,3960,4034,4106,4185,4293,4390,4471,4557,4649,4723,4802,4888,4942,5018,5086,5169,5250,5312,5376,5439,5507,5619,5730,5834,5947,6008,6063,6145,6232,6312,6390"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,146,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3096,3197,3296,3372,3463,4289,4395,4524,7072,7135,7200,7618,7693,7752,7843,7906,7971,8030,8101,8163,8220,8339,8397,8458,8513,8586,8718,8809,8893,8993,9079,9168,9309,9387,9464,9587,9679,9756,9814,9865,9931,10003,10085,10156,10234,10309,10383,10455,10534,10642,10739,10820,10906,10998,11072,11151,11237,11291,11367,11435,11518,11599,11661,11725,11788,11856,11968,12079,12183,12296,12357,13518,14118,14205,14285", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,146,155,156,157", "endColumns": "12,100,98,75,90,83,105,128,84,62,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,83,99,85,88,140,77,76,122,91,76,57,50,65,71,81,70,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81,86,79,77", "endOffsets": "330,3192,3291,3367,3458,3542,4390,4519,4604,7130,7195,7285,7688,7747,7838,7901,7966,8025,8096,8158,8215,8334,8392,8453,8508,8581,8713,8804,8888,8988,9074,9163,9304,9382,9459,9582,9674,9751,9809,9860,9926,9998,10080,10151,10229,10304,10378,10450,10529,10637,10734,10815,10901,10993,11067,11146,11232,11286,11362,11430,11513,11594,11656,11720,11783,11851,11963,12074,12178,12291,12352,12407,13595,14200,14280,14358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3c8ddd354c9ab96e361fff9bc529194f\\transformed\\browser-1.4.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,270,385", "endColumns": "108,105,114,106", "endOffsets": "159,265,380,487"}, "to": {"startLines": "66,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6963,7290,7396,7511", "endColumns": "108,105,114,106", "endOffsets": "7067,7391,7506,7613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e165e972ac01707ea55a93b91866c0a0\\transformed\\appcompat-1.7.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,448,555,671,758,867,990,1069,1147,1238,1331,1426,1520,1620,1713,1808,1902,1993,2084,2169,2284,2393,2492,2618,2725,2833,2993,14032", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "443,550,666,753,862,985,1064,1142,1233,1326,1421,1515,1615,1708,1803,1897,1988,2079,2164,2279,2388,2487,2613,2720,2828,2988,3091,14113"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6268fa03c7ebf543ba887f8bf449bb01\\transformed\\play-services-ads-22.6.0\\res\\values-my\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,243,293,351,420,489,595,669,786,836,961,1015,1163,1253,1300,1392,1428,1467,1528,1614,1657", "endColumns": "43,49,57,68,68,105,73,116,49,124,53,147,89,46,91,35,38,60,85,42,55", "endOffsets": "242,292,350,419,488,594,668,785,835,960,1014,1162,1252,1299,1391,1427,1466,1527,1613,1656,1712"}, "to": {"startLines": "133,134,135,136,137,138,139,140,141,142,143,144,145,147,148,149,150,151,152,153,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12412,12460,12514,12576,12649,12722,12832,12910,13031,13085,13214,13272,13424,13600,13651,13747,13787,13830,13895,13985,14464", "endColumns": "47,53,61,72,72,109,77,120,53,128,57,151,93,50,95,39,42,64,89,46,59", "endOffsets": "12455,12509,12571,12644,12717,12827,12905,13026,13080,13209,13267,13419,13513,13646,13742,13782,13825,13890,13980,14027,14519"}}]}]}