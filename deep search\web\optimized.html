<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSearch Agent - الوضع المحسن</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 900px;
            padding: 30px;
        }
        
        .optimization-badge {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .search-box {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .btn-search {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            color: white;
            font-weight: 600;
        }
        
        .optimization-info {
            background: #e8f5e8;
            border: 1px solid #28a745;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .token-counter {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 10px;
            margin-top: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="text-center mb-4">
                <div class="optimization-badge">
                    <i class="fas fa-leaf"></i> الوضع المحسن - استهلاك منخفض
                </div>
                <h1 class="display-5 text-success">
                    <i class="fas fa-search"></i>
                    DeepSearch Agent
                </h1>
                <p class="lead text-muted">البحث الذكي مع تحسين استهلاك الرموز</p>
            </div>

            <!-- Optimization Info -->
            <div class="optimization-info">
                <h6><i class="fas fa-info-circle"></i> معلومات التحسين:</h6>
                <div class="row">
                    <div class="col-md-6">
                        <small>
                            ✅ تلخيص مُحسن (150 حرف)<br>
                            ❌ استخراج الكلمات المفتاحية (معطل)<br>
                            ❌ تحليل المشاعر (معطل)
                        </small>
                    </div>
                    <div class="col-md-6">
                        <small>
                            🔄 حد الطلبات: 5 طلبات/دقيقة<br>
                            📊 حد النتائج: 5 نتائج كحد أقصى<br>
                            ⚡ محتوى مختصر: 2000 حرف
                        </small>
                    </div>
                </div>
            </div>

            <!-- Search Box -->
            <div class="search-box">
                <div class="row">
                    <div class="col-md-8">
                        <input type="text" id="searchQuery" class="form-control" 
                               placeholder="ابحث بكلمات قصيرة ومحددة... (مثال: أخبار الذكاء الاصطناعي)">
                    </div>
                    <div class="col-md-4">
                        <button onclick="performOptimizedSearch()" class="btn btn-search w-100">
                            <i class="fas fa-search"></i> بحث محسن
                        </button>
                    </div>
                </div>
                
                <!-- Optimized Options -->
                <div class="row mt-3">
                    <div class="col-md-4">
                        <select id="maxResults" class="form-select">
                            <option value="3">3 نتائج (موصى)</option>
                            <option value="5" selected>5 نتائج</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="summarize" checked>
                            <label class="form-check-label" for="summarize">
                                تلخيص مختصر
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="useCache" checked>
                            <label class="form-check-label" for="useCache">
                                استخدام التخزين المؤقت
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Token Counter -->
                <div class="token-counter" id="tokenInfo" style="display: none;">
                    <i class="fas fa-coins"></i> 
                    <span id="tokenCount">تقدير الرموز: 0</span> | 
                    <span id="costEstimate">التكلفة المقدرة: $0.00</span>
                </div>
            </div>

            <!-- Loading -->
            <div id="loading" class="text-center" style="display: none;">
                <div class="spinner-border text-success" role="status"></div>
                <p class="mt-2">جاري البحث بالوضع المحسن...</p>
            </div>

            <!-- Results -->
            <div id="results" style="display: none;">
                <h5><i class="fas fa-list"></i> النتائج المحسنة</h5>
                <div id="resultsContainer"></div>
                
                <!-- Stats -->
                <div id="statsContainer" class="mt-4"></div>
            </div>

            <!-- Error -->
            <div id="error" class="alert alert-danger" style="display: none;">
                <h6><i class="fas fa-exclamation-triangle"></i> خطأ</h6>
                <p id="errorMessage"></p>
            </div>

            <!-- Tips -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6><i class="fas fa-lightbulb"></i> نصائح لتوفير الاستهلاك</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li>🎯 استخدم كلمات بحث محددة وقصيرة</li>
                                <li>📊 ابحث عن 3-5 نتائج فقط</li>
                                <li>💾 فعّل التخزين المؤقت</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li>⏱️ انتظر بين البحثات (حد الطلبات)</li>
                                <li>🔄 استخدم النتائج المحفوظة</li>
                                <li>📝 اختر التلخيص المختصر</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE_URL = 'http://localhost:8000';
        let lastSearchTime = 0;
        const RATE_LIMIT_DELAY = 12000; // 12 seconds between searches

        // Estimate tokens
        function estimateTokens(query, maxResults) {
            const baseTokens = 100; // Base API overhead
            const queryTokens = query.length / 4; // Rough estimate
            const resultTokens = maxResults * 200; // Per result processing
            return Math.ceil(baseTokens + queryTokens + resultTokens);
        }

        // Update token info
        function updateTokenInfo() {
            const query = document.getElementById('searchQuery').value;
            const maxResults = parseInt(document.getElementById('maxResults').value);
            
            if (query.length > 0) {
                const tokens = estimateTokens(query, maxResults);
                const cost = (tokens / 1000) * 0.002; // Rough Mistral pricing
                
                document.getElementById('tokenCount').textContent = `تقدير الرموز: ${tokens}`;
                document.getElementById('costEstimate').textContent = `التكلفة المقدرة: $${cost.toFixed(4)}`;
                document.getElementById('tokenInfo').style.display = 'block';
            } else {
                document.getElementById('tokenInfo').style.display = 'none';
            }
        }

        // Event listeners
        document.getElementById('searchQuery').addEventListener('input', updateTokenInfo);
        document.getElementById('maxResults').addEventListener('change', updateTokenInfo);

        // Optimized search
        async function performOptimizedSearch() {
            const query = document.getElementById('searchQuery').value.trim();
            if (!query) {
                showError('يرجى إدخال استعلام البحث');
                return;
            }

            // Check rate limit
            const now = Date.now();
            if (now - lastSearchTime < RATE_LIMIT_DELAY) {
                const remaining = Math.ceil((RATE_LIMIT_DELAY - (now - lastSearchTime)) / 1000);
                showError(`يرجى الانتظار ${remaining} ثانية قبل البحث التالي`);
                return;
            }

            const maxResults = parseInt(document.getElementById('maxResults').value);
            const summarize = document.getElementById('summarize').checked;
            const useCache = document.getElementById('useCache').checked;

            showLoading();
            hideError();
            hideResults();

            try {
                const response = await fetch(`${API_BASE_URL}/search`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        query: query,
                        max_results: maxResults,
                        summarize: summarize,
                        use_cache: useCache,
                        enhance_query: false // Disabled in optimized mode
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                showOptimizedResults(result);
                lastSearchTime = now;

            } catch (error) {
                console.error('Search error:', error);
                showError('حدث خطأ أثناء البحث: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        // Show optimized results
        function showOptimizedResults(result) {
            showResults();
            
            const container = document.getElementById('resultsContainer');
            container.innerHTML = '';

            if (result.articles && result.articles.length > 0) {
                result.articles.forEach((article, index) => {
                    const card = document.createElement('div');
                    card.className = 'card mb-3';
                    card.innerHTML = `
                        <div class="card-body">
                            <h6 class="card-title">
                                <span class="badge bg-success me-2">${index + 1}</span>
                                ${article.title || 'بدون عنوان'}
                            </h6>
                            <p class="card-text">${article.summary || article.content?.substring(0, 200) + '...' || 'لا يوجد محتوى'}</p>
                            <a href="${article.url}" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-external-link-alt"></i> عرض المصدر
                            </a>
                        </div>
                    `;
                    container.appendChild(card);
                });
            } else {
                container.innerHTML = '<div class="alert alert-info">لم يتم العثور على نتائج</div>';
            }

            // Show stats
            const statsContainer = document.getElementById('statsContainer');
            statsContainer.innerHTML = `
                <div class="card">
                    <div class="card-body">
                        <h6>إحصائيات البحث المحسن</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>${result.total_results}</strong><br>
                                <small>النتائج</small>
                            </div>
                            <div class="col-md-3">
                                <strong>${result.processing_time?.toFixed(2)}s</strong><br>
                                <small>وقت المعالجة</small>
                            </div>
                            <div class="col-md-3">
                                <strong>${result.cached ? 'نعم' : 'لا'}</strong><br>
                                <small>من التخزين المؤقت</small>
                            </div>
                            <div class="col-md-3">
                                <strong>محسن</strong><br>
                                <small>وضع التوفير</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Utility functions
        function showLoading() { document.getElementById('loading').style.display = 'block'; }
        function hideLoading() { document.getElementById('loading').style.display = 'none'; }
        function showResults() { document.getElementById('results').style.display = 'block'; }
        function hideResults() { document.getElementById('results').style.display = 'none'; }
        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('error').style.display = 'block';
        }
        function hideError() { document.getElementById('error').style.display = 'none'; }

        // Enter key support
        document.getElementById('searchQuery').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') performOptimizedSearch();
        });
    </script>
</body>
</html>
