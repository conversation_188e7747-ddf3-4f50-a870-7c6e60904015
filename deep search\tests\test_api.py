"""
Tests for API endpoints
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch, AsyncMock
from src.api.main import app, get_search_engine
from src.core.search_engine import DeepSearchEngine


class TestAPI:
    """Test API endpoints"""
    
    @pytest.fixture
    def mock_search_engine(self):
        """Create a mock search engine"""
        engine = Mock(spec=DeepSearchEngine)
        engine.__aenter__ = AsyncMock(return_value=engine)
        engine.__aexit__ = AsyncMock(return_value=None)
        return engine
    
    @pytest.fixture
    def client(self, mock_search_engine):
        """Create test client with mocked dependencies"""
        app.dependency_overrides[get_search_engine] = lambda: mock_search_engine
        client = TestClient(app)
        yield client
        app.dependency_overrides.clear()
    
    def test_root_endpoint(self, client):
        """Test root endpoint"""
        response = client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "DeepSearch Agent API" in data["message"]
        assert "version" in data["data"]
    
    def test_search_endpoint(self, client, mock_search_engine):
        """Test search endpoint"""
        # Mock search result
        mock_result = {
            "query": "test query",
            "enhanced_query": "enhanced test query",
            "articles": [
                {
                    "url": "https://example.com",
                    "title": "Test Article",
                    "content": "Test content",
                    "summary": "Test summary",
                    "description": "Test description",
                    "author": "Test Author",
                    "language": "ar",
                    "keywords": ["test"],
                    "ai_keywords": ["test", "article"],
                    "source_engine": "duckduckgo",
                    "scraped_at": "2024-01-01T00:00:00",
                    "is_summarized": True,
                    "sentiment": {"sentiment": "محايدة", "confidence": 0.5},
                    "similarity_score": 0.8
                }
            ],
            "total_results": 1,
            "processing_time": 1.5,
            "engines_used": ["duckduckgo"],
            "cached": False,
            "query_id": 1
        }
        
        mock_search_engine.search = AsyncMock(return_value=mock_result)
        
        # Test search request
        search_request = {
            "query": "test query",
            "max_results": 10,
            "summarize": True
        }
        
        response = client.post("/search", json=search_request)
        assert response.status_code == 200
        
        data = response.json()
        assert data["query"] == "test query"
        assert data["total_results"] == 1
        assert len(data["articles"]) == 1
        assert data["articles"][0]["title"] == "Test Article"
    
    def test_search_endpoint_validation(self, client):
        """Test search endpoint input validation"""
        # Test empty query
        response = client.post("/search", json={"query": ""})
        assert response.status_code == 422
        
        # Test missing query
        response = client.post("/search", json={})
        assert response.status_code == 422
        
        # Test invalid max_results
        response = client.post("/search", json={"query": "test", "max_results": 0})
        assert response.status_code == 422
        
        response = client.post("/search", json={"query": "test", "max_results": 200})
        assert response.status_code == 422
    
    def test_rag_endpoint(self, client, mock_search_engine):
        """Test RAG endpoint"""
        # Mock RAG result
        mock_result = {
            "query": "test question",
            "answer": "This is the answer",
            "sources": [
                {
                    "title": "Source Article",
                    "url": "https://example.com",
                    "similarity_score": 0.9
                }
            ],
            "confidence": 0.8,
            "context_used": 1,
            "total_context_articles": 5,
            "processing_time": 2.0
        }
        
        mock_search_engine.rag_search = AsyncMock(return_value=mock_result)
        
        # Test RAG request
        rag_request = {
            "query": "test question",
            "max_context_articles": 10
        }
        
        response = client.post("/rag", json=rag_request)
        assert response.status_code == 200
        
        data = response.json()
        assert data["query"] == "test question"
        assert data["answer"] == "This is the answer"
        assert data["confidence"] == 0.8
        assert len(data["sources"]) == 1
    
    def test_trending_endpoint(self, client, mock_search_engine):
        """Test trending topics endpoint"""
        # Mock trending topics
        mock_topics = [
            {"topic": "AI", "frequency": 10, "articles_count": 10},
            {"topic": "Technology", "frequency": 8, "articles_count": 8}
        ]
        
        mock_search_engine.get_trending_topics = AsyncMock(return_value=mock_topics)
        
        response = client.get("/trending?limit=5")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data["topics"]) == 2
        assert data["topics"][0]["topic"] == "AI"
        assert data["total_topics"] == 2
        assert "generated_at" in data
    
    def test_health_endpoint(self, client, mock_search_engine):
        """Test health check endpoint"""
        # Mock health status
        mock_health = {
            "timestamp": **********.0,
            "overall_status": "healthy",
            "scrapers": {"duckduckgo": True},
            "ai": {"ai_provider": True, "embedding_manager": True},
            "database": {"status": "healthy", "stats": {}}
        }
        
        mock_search_engine.health_check = AsyncMock(return_value=mock_health)
        
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["overall_status"] == "healthy"
        assert "scrapers" in data
        assert "ai" in data
        assert "database" in data
    
    def test_stats_endpoint(self, client, mock_search_engine):
        """Test database stats endpoint"""
        # Mock database stats
        mock_stats = {
            "total_articles": 100,
            "total_queries": 50,
            "total_embeddings": 80,
            "total_cache_entries": 20,
            "recent_articles_24h": 10
        }
        
        mock_search_engine.database_manager = Mock()
        mock_search_engine.database_manager.get_database_stats = AsyncMock(return_value=mock_stats)
        
        response = client.get("/stats")
        assert response.status_code == 200
        
        data = response.json()
        assert data["total_articles"] == 100
        assert data["total_queries"] == 50
        assert data["recent_articles_24h"] == 10
    
    def test_cache_clear_endpoint(self, client, mock_search_engine):
        """Test cache clear endpoint"""
        mock_search_engine.database_manager = Mock()
        mock_search_engine.database_manager.cleanup_expired_cache = AsyncMock()
        
        response = client.post("/cache/clear")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "cleared" in data["message"]
    
    def test_engines_endpoint(self, client, mock_search_engine):
        """Test available engines endpoint"""
        mock_engines = ["duckduckgo", "brave"]
        mock_search_engine.scraper_manager = Mock()
        mock_search_engine.scraper_manager.get_available_engines = Mock(return_value=mock_engines)
        
        response = client.get("/engines")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert data["data"]["engines"] == mock_engines
    
    def test_error_handling(self, client, mock_search_engine):
        """Test API error handling"""
        # Mock search engine to raise an exception
        mock_search_engine.search = AsyncMock(side_effect=Exception("Test error"))
        
        search_request = {"query": "test query"}
        response = client.post("/search", json=search_request)
        
        assert response.status_code == 500
        data = response.json()
        assert "error" in data


@pytest.mark.integration
class TestAPIIntegration:
    """Integration tests for API"""
    
    @pytest.fixture
    def client(self):
        """Create test client for integration tests"""
        return TestClient(app)
    
    def test_api_startup(self, client):
        """Test API startup and basic functionality"""
        response = client.get("/")
        assert response.status_code == 200
    
    @pytest.mark.skip(reason="Requires full system setup")
    def test_full_search_integration(self, client):
        """Test full search integration (requires system setup)"""
        search_request = {
            "query": "Python programming",
            "max_results": 2,
            "engines": ["duckduckgo"]
        }
        
        response = client.post("/search", json=search_request)
        
        # This test would require full system setup
        # Skip for now but keep as example
        pass
