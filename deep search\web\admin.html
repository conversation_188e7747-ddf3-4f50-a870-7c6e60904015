<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSearch Agent - لوحة الإدارة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }
        
        .provider-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        
        .provider-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .provider-active {
            border: 2px solid #28a745;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .provider-inactive {
            border: 2px solid #6c757d;
        }
        
        .provider-error {
            border: 2px solid #dc3545;
            background: linear-gradient(45deg, #dc3545, #e74c3c);
            color: white;
        }
        
        .status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-healthy { background: #28a745; color: white; }
        .status-unhealthy { background: #dc3545; color: white; }
        .status-unknown { background: #6c757d; color: white; }
        
        .btn-switch {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-switch:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="text-center mb-4">
                <h1 class="display-4 text-primary">
                    <i class="fas fa-cogs"></i>
                    لوحة إدارة DeepSearch
                </h1>
                <p class="lead text-muted">إدارة مقدمي خدمات الذكاء الاصطناعي</p>
                <div class="mt-3">
                    <a href="index.html" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> واجهة البحث
                    </a>
                    <button onclick="refreshProviders()" class="btn btn-outline-secondary">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>

            <!-- Loading -->
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>جاري تحميل معلومات المقدمين...</p>
            </div>

            <!-- Error -->
            <div id="error" class="alert alert-danger" style="display: none;">
                <h5><i class="fas fa-exclamation-triangle"></i> خطأ</h5>
                <p id="errorMessage"></p>
            </div>

            <!-- Success -->
            <div id="success" class="alert alert-success" style="display: none;">
                <h5><i class="fas fa-check-circle"></i> نجح</h5>
                <p id="successMessage"></p>
            </div>

            <!-- Providers Info -->
            <div id="providersInfo" style="display: none;">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-robot"></i> المقدم الأساسي</h5>
                            </div>
                            <div class="card-body" id="primaryProvider">
                                <!-- Primary provider info will be loaded here -->
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-life-ring"></i> المقدم الاحتياطي</h5>
                            </div>
                            <div class="card-body" id="fallbackProvider">
                                <!-- Fallback provider info will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Auto Switch Setting -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-toggle-on"></i> إعدادات التبديل التلقائي</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoSwitch">
                            <label class="form-check-label" for="autoSwitch">
                                تفعيل التبديل التلقائي للمقدم الاحتياطي عند فشل المقدم الأساسي
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Available Providers -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> المقدمون المتاحون</h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="availableProviders">
                            <!-- Available providers will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Health Status -->
            <div id="healthStatus" class="mt-4" style="display: none;">
                <h4><i class="fas fa-heartbeat"></i> حالة النظام</h4>
                <div id="healthInfo"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE_URL = 'http://localhost:8000';

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadProviders();
            loadHealthStatus();
        });

        // Load providers information
        async function loadProviders() {
            showLoading();
            hideError();
            hideSuccess();

            try {
                const response = await fetch(`${API_BASE_URL}/ai/providers`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                displayProviders(result.data);
                showProvidersInfo();

            } catch (error) {
                console.error('Error loading providers:', error);
                showError('فشل في تحميل معلومات المقدمين: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        // Display providers information
        function displayProviders(data) {
            // Display primary provider
            const primaryDiv = document.getElementById('primaryProvider');
            if (data.primary_provider) {
                primaryDiv.innerHTML = createProviderHTML(data.primary_provider, true);
            } else {
                primaryDiv.innerHTML = '<p class="text-muted">لا يوجد مقدم أساسي</p>';
            }

            // Display fallback provider
            const fallbackDiv = document.getElementById('fallbackProvider');
            if (data.fallback_provider) {
                fallbackDiv.innerHTML = createProviderHTML(data.fallback_provider, false);
            } else {
                fallbackDiv.innerHTML = '<p class="text-muted">لا يوجد مقدم احتياطي</p>';
            }

            // Set auto switch checkbox
            document.getElementById('autoSwitch').checked = data.auto_switch;

            // Display available providers for switching
            displayAvailableProviders();
        }

        // Create provider HTML
        function createProviderHTML(provider, isPrimary) {
            const statusClass = provider.api_key_configured ? 'text-success' : 'text-danger';
            const statusIcon = provider.api_key_configured ? 'fa-check-circle' : 'fa-times-circle';
            
            return `
                <h6>${provider.provider || 'غير محدد'}</h6>
                <p><strong>النموذج:</strong> ${provider.model || 'غير محدد'}</p>
                <p><strong>مفتاح API:</strong> 
                    <span class="${statusClass}">
                        <i class="fas ${statusIcon}"></i>
                        ${provider.api_key_configured ? 'مُكوّن' : 'غير مُكوّن'}
                    </span>
                </p>
                <p><strong>دعم المحادثة:</strong> ${provider.supports_chat ? 'نعم' : 'لا'}</p>
                <p><strong>دعم التضمينات:</strong> ${provider.supports_embeddings ? 'نعم' : 'لا'}</p>
                ${!isPrimary ? `
                    <button onclick="switchProvider('${provider.provider}')" class="btn btn-switch btn-sm">
                        <i class="fas fa-exchange-alt"></i> جعله أساسي
                    </button>
                ` : ''}
            `;
        }

        // Display available providers
        function displayAvailableProviders() {
            const container = document.getElementById('availableProviders');
            const providers = ['gemini', 'mistral', 'ollama'];
            
            container.innerHTML = providers.map(provider => `
                <div class="col-md-4 mb-3">
                    <div class="provider-card position-relative">
                        <h6>${getProviderDisplayName(provider)}</h6>
                        <p class="text-muted">${getProviderDescription(provider)}</p>
                        <button onclick="switchProvider('${provider}')" class="btn btn-switch btn-sm">
                            <i class="fas fa-exchange-alt"></i> تبديل
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Switch AI provider
        async function switchProvider(provider) {
            showLoading();
            hideError();
            hideSuccess();

            try {
                const response = await fetch(`${API_BASE_URL}/ai/switch?provider=${provider}`, {
                    method: 'POST'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                showSuccess(`تم التبديل بنجاح إلى ${getProviderDisplayName(provider)}`);
                
                // Reload providers info
                setTimeout(() => {
                    loadProviders();
                }, 1000);

            } catch (error) {
                console.error('Error switching provider:', error);
                showError('فشل في تبديل المقدم: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        // Load health status
        async function loadHealthStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const health = await response.json();
                
                displayHealthStatus(health);
                showHealthStatus();

            } catch (error) {
                console.error('Error loading health status:', error);
            }
        }

        // Display health status
        function displayHealthStatus(health) {
            const container = document.getElementById('healthInfo');
            
            container.innerHTML = `
                <div class="row">
                    <div class="col-md-4">
                        <div class="card ${health.overall_status === 'healthy' ? 'border-success' : 'border-danger'}">
                            <div class="card-body text-center">
                                <h5>الحالة العامة</h5>
                                <h3 class="${health.overall_status === 'healthy' ? 'text-success' : 'text-danger'}">
                                    ${health.overall_status === 'healthy' ? 'سليم' : 'مشاكل'}
                                </h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5>الكاشطات</h5>
                                <p>${Object.keys(health.scrapers || {}).length} متاح</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5>الذكاء الاصطناعي</h5>
                                <p>${health.ai?.ai_provider ? 'متاح' : 'غير متاح'}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Refresh providers
        function refreshProviders() {
            loadProviders();
            loadHealthStatus();
        }

        // Utility functions
        function getProviderDisplayName(provider) {
            const names = {
                'gemini': 'Google Gemini',
                'mistral': 'Mistral AI',
                'ollama': 'Ollama (محلي)'
            };
            return names[provider] || provider;
        }

        function getProviderDescription(provider) {
            const descriptions = {
                'gemini': 'نموذج Gemini 2.0 Flash من Google',
                'mistral': 'نماذج Mistral AI المتقدمة',
                'ollama': 'نماذج محلية عبر Ollama'
            };
            return descriptions[provider] || 'مقدم خدمة ذكاء اصطناعي';
        }

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        function showProvidersInfo() {
            document.getElementById('providersInfo').style.display = 'block';
        }

        function showHealthStatus() {
            document.getElementById('healthStatus').style.display = 'block';
        }

        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('error').style.display = 'block';
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }

        function showSuccess(message) {
            document.getElementById('successMessage').textContent = message;
            document.getElementById('success').style.display = 'block';
        }

        function hideSuccess() {
            document.getElementById('success').style.display = 'none';
        }
    </script>
</body>
</html>
