"""
Publishing API routes for Blogger and other platforms
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
from loguru import logger

from .main import get_search_engine
from ..core.search_engine import DeepSearchEngine
from ..publishing.blogger_publisher import BloggerPublisher


router = APIRouter(prefix="/publishing", tags=["publishing"])


class BloggerConfigRequest(BaseModel):
    service_account_json: str
    blog_id: str


class PublishRequest(BaseModel):
    title: str
    content: str
    labels: Optional[List[str]] = None
    is_draft: Optional[bool] = False
    meta_description: Optional[str] = None
    seo_keywords: Optional[List[str]] = None


class HTMLCopyRequest(BaseModel):
    title: str
    content: str
    meta_description: Optional[str] = None
    seo_keywords: Optional[List[str]] = None


class ConfigResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    blog_id: Optional[str] = None
    service_account_email: Optional[str] = None
    error: Optional[str] = None


class PublishResponse(BaseModel):
    success: bool
    post_id: Optional[str] = None
    post_url: Optional[str] = None
    published_date: Optional[str] = None
    status: Optional[str] = None
    labels: Optional[List[str]] = None
    error: Optional[str] = None


class HTMLCopyResponse(BaseModel):
    success: bool
    html_content: Optional[str] = None
    error: Optional[str] = None


class StatusResponse(BaseModel):
    configured: bool
    ready: bool
    blog_id: Optional[str] = None
    blog_name: Optional[str] = None
    blog_url: Optional[str] = None
    posts_count: Optional[int] = None
    auto_publish: bool
    default_labels: List[str]


# Global publisher instance
blogger_publisher: BloggerPublisher = None


def get_blogger_publisher() -> BloggerPublisher:
    """Get or create Blogger publisher instance"""
    global blogger_publisher
    if blogger_publisher is None:
        blogger_publisher = BloggerPublisher({})
    return blogger_publisher


@router.post("/blogger/configure", response_model=ConfigResponse)
async def configure_blogger(request: BloggerConfigRequest):
    """
    Configure Blogger API with service account credentials
    
    This endpoint configures the Blogger API using service account JSON
    and tests the connection to the specified blog.
    """
    try:
        publisher = get_blogger_publisher()
        
        result = publisher.configure_service_account(
            service_account_json=request.service_account_json,
            blog_id=request.blog_id
        )
        
        return ConfigResponse(**result)
        
    except Exception as e:
        logger.error(f"Failed to configure Blogger: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/blogger/status", response_model=StatusResponse)
async def get_blogger_status():
    """
    Get current status of Blogger publisher
    
    Returns information about configuration, blog details, and settings.
    """
    try:
        publisher = get_blogger_publisher()
        status = publisher.get_status()
        
        return StatusResponse(**status)
        
    except Exception as e:
        logger.error(f"Failed to get Blogger status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/blogger/publish", response_model=PublishResponse)
async def publish_to_blogger(request: PublishRequest):
    """
    Publish article to Blogger
    
    This endpoint publishes an article to the configured Blogger blog
    with SEO optimization and proper formatting.
    """
    try:
        publisher = get_blogger_publisher()
        
        if not publisher.is_ready():
            raise HTTPException(
                status_code=400,
                detail="Blogger is not configured. Please configure service account first."
            )
        
        result = await publisher.publish_article(
            title=request.title,
            content=request.content,
            labels=request.labels,
            is_draft=request.is_draft,
            meta_description=request.meta_description,
            seo_keywords=request.seo_keywords
        )
        
        return PublishResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to publish to Blogger: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/html-copy", response_model=HTMLCopyResponse)
async def generate_html_copy(request: HTMLCopyRequest):
    """
    Generate HTML copy for manual pasting into Blogger
    
    This endpoint generates properly formatted HTML that can be copied
    and pasted directly into Blogger's HTML editor.
    """
    try:
        publisher = get_blogger_publisher()
        
        html_content = publisher.generate_html_copy(
            title=request.title,
            content=request.content,
            meta_description=request.meta_description,
            seo_keywords=request.seo_keywords
        )
        
        return HTMLCopyResponse(
            success=True,
            html_content=html_content
        )
        
    except Exception as e:
        logger.error(f"Failed to generate HTML copy: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/complete-workflow")
async def complete_article_workflow(
    topic: str,
    article_type: str = "professional",
    publish_to_blogger: bool = False,
    is_draft: bool = True,
    engine: DeepSearchEngine = Depends(get_search_engine)
):
    """
    Complete workflow: Deep search + Article generation + Publishing
    
    This endpoint performs the complete workflow from topic research
    to article generation and optional publishing to Blogger.
    """
    try:
        async with engine:
            # Step 1: Deep search
            logger.info(f"Starting complete workflow for topic: {topic}")
            
            search_result = await engine.deep_single_article_search(
                query=topic,
                target_language="ar",
                article_style=article_type
            )
            
            if not search_result.get('success'):
                raise HTTPException(
                    status_code=400,
                    detail=f"Deep search failed: {search_result.get('error')}"
                )
            
            # Step 2: Generate article
            article_generator = engine.ai_manager.article_generator
            
            if not article_generator.is_configured():
                raise HTTPException(
                    status_code=400,
                    detail="Gemini is not configured for article generation"
                )
            
            article_result = await article_generator.generate_article(
                topic=topic,
                research_data=search_result,
                article_type=article_type,
                target_language="ar",
                min_words=800,
                max_words=1500
            )
            
            if not article_result.get('success'):
                raise HTTPException(
                    status_code=500,
                    detail=f"Article generation failed: {article_result.get('error')}"
                )
            
            article_data = article_result['article']
            
            # Step 3: Publish to Blogger (if requested)
            publishing_result = None
            if publish_to_blogger:
                publisher = get_blogger_publisher()
                
                if publisher.is_ready():
                    publishing_result = await publisher.publish_article(
                        title=article_data['title'],
                        content=article_data['content'],
                        labels=article_data.get('seo_keywords', []),
                        is_draft=is_draft,
                        meta_description=article_data.get('meta_description'),
                        seo_keywords=article_data.get('seo_keywords')
                    )
                else:
                    publishing_result = {
                        "success": False,
                        "error": "Blogger is not configured"
                    }
            
            # Step 4: Generate HTML copy
            html_copy = get_blogger_publisher().generate_html_copy(
                title=article_data['title'],
                content=article_data['content'],
                meta_description=article_data.get('meta_description'),
                seo_keywords=article_data.get('seo_keywords')
            )
            
            return {
                "success": True,
                "workflow_completed": True,
                "deep_search": search_result,
                "article": article_result,
                "publishing": publishing_result,
                "html_copy": html_copy,
                "processing_summary": {
                    "topic": topic,
                    "article_type": article_type,
                    "word_count": article_data.get('word_count'),
                    "seo_keywords_count": len(article_data.get('seo_keywords', [])),
                    "published_to_blogger": publish_to_blogger and publishing_result and publishing_result.get('success'),
                    "completed_at": datetime.now().isoformat()
                }
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Complete workflow failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
