# دليل المساهمة في DeepSearch Agent

نرحب بمساهماتكم في تطوير DeepSearch Agent! هذا الدليل يوضح كيفية المساهمة في المشروع.

## 🚀 البدء السريع

### 1. إعداد بيئة التطوير
```bash
# استنساخ المشروع
git clone <repository-url>
cd deep-search

# إنشاء فرع جديد
git checkout -b feature/your-feature-name

# إعداد البيئة
python scripts/setup.py
```

### 2. تشغيل الاختبارات
```bash
# اختبارات سريعة
python scripts/run_tests.py --quick

# جميع الاختبارات
python scripts/run_tests.py --all
```

## 📋 أنواع المساهمات

### 🐛 الإبلاغ عن الأخطاء
- استخدم قالب Issue للأخطاء
- قدم معلومات مفصلة عن الخطأ
- أرفق ملفات السجل إن أمكن

### ✨ اقتراح ميزات جديدة
- استخدم قالب Issue للميزات
- اشرح الحاجة للميزة
- قدم أمثلة على الاستخدام

### 🔧 تطوير الكود
- اتبع معايير الكود المحددة
- اكتب اختبارات للكود الجديد
- حدث الوثائق عند الحاجة

## 🛠️ معايير التطوير

### هيكل الكود
```
src/
├── core/           # المكونات الأساسية
├── scrapers/       # كاشطات المواقع
├── ai/            # معالجة الذكاء الاصطناعي
├── database/      # قاعدة البيانات
├── api/           # واجهة برمجة التطبيقات
└── utils/         # أدوات مساعدة
```

### معايير الكود
- استخدم Python 3.9+
- اتبع PEP 8
- أضف type hints
- اكتب docstrings للدوال
- استخدم أسماء متغيرات واضحة

### مثال على الكود الجيد
```python
async def search_articles(
    query: str, 
    max_results: int = 10,
    language: str = "ar"
) -> List[Dict[str, Any]]:
    """
    البحث عن المقالات
    
    Args:
        query: استعلام البحث
        max_results: الحد الأقصى للنتائج
        language: لغة البحث
        
    Returns:
        قائمة بالمقالات المطابقة
    """
    # تنفيذ الدالة
    pass
```

## 🧪 الاختبارات

### كتابة الاختبارات
```python
import pytest
from src.module import function_to_test

class TestModule:
    def test_function_basic(self):
        """اختبار الوظيفة الأساسية"""
        result = function_to_test("input")
        assert result == "expected_output"
    
    @pytest.mark.asyncio
    async def test_async_function(self):
        """اختبار دالة غير متزامنة"""
        result = await async_function()
        assert result is not None
```

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
python scripts/run_tests.py --unit

# اختبارات التكامل
python scripts/run_tests.py --integration

# اختبار ملف محدد
python scripts/run_tests.py --test tests/test_scrapers.py
```

## 📝 الوثائق

### تحديث الوثائق
- حدث README.md عند إضافة ميزات جديدة
- أضف أمثلة للاستخدام
- اكتب وثائق API للدوال الجديدة

### كتابة الوثائق
```markdown
## الميزة الجديدة

### الوصف
وصف مفصل للميزة

### الاستخدام
```python
# مثال على الاستخدام
result = new_feature(parameter="value")
```

### المعاملات
- `parameter`: وصف المعامل
```

## 🔄 عملية المراجعة

### قبل إرسال Pull Request
1. تأكد من نجاح جميع الاختبارات
2. تأكد من تنسيق الكود
3. حدث الوثائق
4. اكتب رسالة commit واضحة

### رسائل Commit
```bash
# جيد
feat: إضافة دعم للبحث الدلالي
fix: إصلاح خطأ في كاشط DuckDuckGo
docs: تحديث دليل التثبيت

# سيء
update code
fix bug
```

### مراجعة الكود
- سيتم مراجعة جميع Pull Requests
- قد نطلب تعديلات
- كن صبوراً ومتعاوناً

## 🏷️ إصدار الإصدارات

### نظام الإصدارات
نستخدم [Semantic Versioning](https://semver.org/):
- `MAJOR.MINOR.PATCH`
- `1.0.0` → `1.0.1` (إصلاح أخطاء)
- `1.0.0` → `1.1.0` (ميزة جديدة)
- `1.0.0` → `2.0.0` (تغيير جذري)

## 🤝 قواعد السلوك

### التوقعات
- كن محترماً ومهذباً
- ساعد الآخرين
- اقبل النقد البناء
- ركز على ما هو أفضل للمجتمع

### غير المقبول
- التحرش أو التنمر
- اللغة المسيئة
- الهجمات الشخصية
- السلوك غير المهني

## 📞 التواصل

### قنوات التواصل
- GitHub Issues: للأخطاء والميزات
- GitHub Discussions: للنقاشات العامة
- Email: للمسائل الحساسة

### الحصول على المساعدة
- اقرأ الوثائق أولاً
- ابحث في Issues الموجودة
- اطرح أسئلة واضحة ومفصلة

## 🎯 أولويات التطوير

### المجالات المطلوبة
1. **تحسين دقة البحث**
   - خوارزميات بحث أفضل
   - فلترة النتائج المحسنة

2. **دعم لغات إضافية**
   - تحسين دعم العربية
   - إضافة لغات أخرى

3. **تحسين الأداء**
   - تحسين سرعة الكشط
   - تحسين استهلاك الذاكرة

4. **ميزات جديدة**
   - دعم أنواع محتوى إضافية
   - تحليلات متقدمة

### كيفية المساهمة
1. اختر مجالاً يهمك
2. ابحث عن Issues ذات العلامة `good first issue`
3. اطلب تعيين Issue لك
4. ابدأ العمل!

## 📚 موارد مفيدة

### التوثيق التقني
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Scrapy Documentation](https://docs.scrapy.org/)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)

### أدوات التطوير
- [Black](https://black.readthedocs.io/) - تنسيق الكود
- [Flake8](https://flake8.pycqa.org/) - فحص الكود
- [Pytest](https://docs.pytest.org/) - الاختبارات

شكراً لمساهمتك في DeepSearch Agent! 🙏
