"""
Automatic news discovery and topic selection system
"""

import asyncio
import random
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from loguru import logger
import httpx
import re
from collections import Counter


class AutoNewsDiscovery:
    """Intelligent system for discovering and selecting trending news topics"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.news_config = config.get('auto_news', {})
        
        # News sources and APIs
        self.news_apis = {
            'newsapi': {
                'url': 'https://newsapi.org/v2/top-headlines',
                'key_param': 'apiKey',
                'api_key': self.news_config.get('newsapi_key')
            },
            'gnews': {
                'url': 'https://gnews.io/api/v4/top-headlines',
                'key_param': 'token',
                'api_key': self.news_config.get('gnews_key')
            }
        }
        
        # Topic categories and their weights
        self.topic_categories = {
            'technology': {'weight': 0.25, 'keywords': ['تقنية', 'ذكاء اصطناعي', 'تكنولوجيا', 'برمجة', 'تطبيقات']},
            'health': {'weight': 0.20, 'keywords': ['صحة', 'طب', 'علاج', 'دواء', 'فيروس']},
            'business': {'weight': 0.20, 'keywords': ['اقتصاد', 'أعمال', 'شركة', 'استثمار', 'مال']},
            'science': {'weight': 0.15, 'keywords': ['علوم', 'بحث', 'اكتشاف', 'دراسة', 'تجربة']},
            'education': {'weight': 0.10, 'keywords': ['تعليم', 'جامعة', 'مدرسة', 'طلاب', 'دراسة']},
            'sports': {'weight': 0.05, 'keywords': ['رياضة', 'كرة', 'مباراة', 'بطولة', 'فريق']},
            'entertainment': {'weight': 0.05, 'keywords': ['ترفيه', 'فيلم', 'مسلسل', 'فنان', 'موسيقى']}
        }
        
        # Quality filters
        self.quality_filters = {
            'min_title_length': 10,
            'max_title_length': 200,
            'min_description_length': 50,
            'required_keywords_count': 2,
            'exclude_keywords': ['وفاة', 'حادث', 'جريمة', 'قتل', 'إرهاب']
        }
        
        # Language preferences
        self.language_preferences = ['ar', 'en']
        
    async def discover_trending_topics(
        self, 
        category: str = None,
        language: str = 'ar',
        max_topics: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Discover trending news topics automatically
        
        Args:
            category: Specific category to focus on
            language: Preferred language
            max_topics: Maximum number of topics to return
            
        Returns:
            List of trending topics with metadata
        """
        try:
            logger.info(f"Discovering trending topics for category: {category}, language: {language}")
            
            # Fetch news from multiple sources
            all_news = await self._fetch_news_from_sources(language, category)
            
            if not all_news:
                logger.warning("No news found from sources")
                return []
            
            # Filter and score news articles
            filtered_news = self._filter_news_quality(all_news)
            scored_news = self._score_news_articles(filtered_news, category)
            
            # Select best topics
            selected_topics = self._select_best_topics(scored_news, max_topics)
            
            # Enhance topics with additional metadata
            enhanced_topics = await self._enhance_topics(selected_topics)
            
            logger.info(f"Discovered {len(enhanced_topics)} trending topics")
            return enhanced_topics
            
        except Exception as e:
            logger.error(f"Failed to discover trending topics: {str(e)}")
            return []
    
    async def _fetch_news_from_sources(
        self, 
        language: str, 
        category: str = None
    ) -> List[Dict[str, Any]]:
        """Fetch news from multiple sources"""
        all_news = []
        
        try:
            # Fetch from available APIs
            tasks = []
            
            for source_name, source_config in self.news_apis.items():
                if source_config.get('api_key'):
                    tasks.append(self._fetch_from_source(source_name, source_config, language, category))
            
            if not tasks:
                logger.warning("No news APIs configured")
                return []
            
            # Execute requests in parallel
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Combine results
            for result in results:
                if isinstance(result, list):
                    all_news.extend(result)
                elif isinstance(result, Exception):
                    logger.error(f"News source failed: {str(result)}")
            
            # Remove duplicates
            unique_news = self._deduplicate_news(all_news)
            
            logger.info(f"Fetched {len(unique_news)} unique news articles")
            return unique_news
            
        except Exception as e:
            logger.error(f"Failed to fetch news from sources: {str(e)}")
            return []
    
    async def _fetch_from_source(
        self, 
        source_name: str, 
        source_config: Dict[str, Any],
        language: str,
        category: str = None
    ) -> List[Dict[str, Any]]:
        """Fetch news from a specific source"""
        try:
            params = {
                source_config['key_param']: source_config['api_key'],
                'language': language,
                'pageSize': 50
            }
            
            if category:
                params['category'] = category
            
            if language == 'ar':
                params['country'] = 'sa'  # Saudi Arabia for Arabic news
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(source_config['url'], params=params)
                
                if response.status_code == 200:
                    data = response.json()
                    articles = data.get('articles', [])
                    
                    formatted_articles = []
                    for article in articles:
                        formatted_articles.append({
                            'title': article.get('title', ''),
                            'description': article.get('description', ''),
                            'url': article.get('url', ''),
                            'published_at': article.get('publishedAt', ''),
                            'source': source_name,
                            'language': language,
                            'category': category or 'general'
                        })
                    
                    logger.info(f"Fetched {len(formatted_articles)} articles from {source_name}")
                    return formatted_articles
                else:
                    logger.error(f"{source_name} API error: {response.status_code}")
                    return []
                    
        except Exception as e:
            logger.error(f"Failed to fetch from {source_name}: {str(e)}")
            return []
    
    def _deduplicate_news(self, news_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate news articles"""
        seen_titles = set()
        unique_news = []
        
        for article in news_list:
            title = article.get('title', '').lower().strip()
            if title and title not in seen_titles:
                seen_titles.add(title)
                unique_news.append(article)
        
        return unique_news
    
    def _filter_news_quality(self, news_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter news articles based on quality criteria"""
        filtered_news = []
        
        for article in news_list:
            title = article.get('title', '')
            description = article.get('description', '')
            
            # Check title length
            if not (self.quality_filters['min_title_length'] <= len(title) <= self.quality_filters['max_title_length']):
                continue
            
            # Check description length
            if len(description) < self.quality_filters['min_description_length']:
                continue
            
            # Check for excluded keywords
            full_text = f"{title} {description}".lower()
            if any(keyword in full_text for keyword in self.quality_filters['exclude_keywords']):
                continue
            
            # Check for minimum keyword relevance
            keyword_count = 0
            for category_data in self.topic_categories.values():
                for keyword in category_data['keywords']:
                    if keyword in full_text:
                        keyword_count += 1
            
            if keyword_count >= self.quality_filters['required_keywords_count']:
                filtered_news.append(article)
        
        logger.info(f"Filtered to {len(filtered_news)} quality articles")
        return filtered_news
    
    def _score_news_articles(
        self, 
        news_list: List[Dict[str, Any]], 
        preferred_category: str = None
    ) -> List[Dict[str, Any]]:
        """Score news articles based on relevance and trending potential"""
        scored_news = []
        
        for article in news_list:
            title = article.get('title', '')
            description = article.get('description', '')
            full_text = f"{title} {description}".lower()
            
            score = 0.0
            matched_categories = []
            
            # Category relevance scoring
            for category, category_data in self.topic_categories.items():
                category_score = 0
                for keyword in category_data['keywords']:
                    if keyword in full_text:
                        category_score += 1
                
                if category_score > 0:
                    weighted_score = category_score * category_data['weight']
                    score += weighted_score
                    matched_categories.append(category)
            
            # Boost score for preferred category
            if preferred_category and preferred_category in matched_categories:
                score *= 1.5
            
            # Freshness bonus (newer articles get higher scores)
            try:
                published_at = article.get('published_at', '')
                if published_at:
                    pub_date = datetime.fromisoformat(published_at.replace('Z', '+00:00'))
                    hours_old = (datetime.now().replace(tzinfo=pub_date.tzinfo) - pub_date).total_seconds() / 3600
                    
                    if hours_old < 6:
                        score *= 1.3  # Very fresh
                    elif hours_old < 24:
                        score *= 1.1  # Fresh
                    elif hours_old > 72:
                        score *= 0.8  # Old news
            except Exception:
                pass
            
            # Title engagement potential
            engagement_keywords = ['جديد', 'اكتشاف', 'تطوير', 'إنجاز', 'نجاح', 'ثورة', 'مستقبل']
            for keyword in engagement_keywords:
                if keyword in title.lower():
                    score *= 1.2
                    break
            
            article['relevance_score'] = score
            article['matched_categories'] = matched_categories
            scored_news.append(article)
        
        # Sort by score
        scored_news.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        logger.info(f"Scored {len(scored_news)} articles")
        return scored_news
    
    def _select_best_topics(
        self, 
        scored_news: List[Dict[str, Any]], 
        max_topics: int
    ) -> List[Dict[str, Any]]:
        """Select the best topics ensuring diversity"""
        selected_topics = []
        used_categories = set()
        
        for article in scored_news:
            if len(selected_topics) >= max_topics:
                break
            
            # Ensure category diversity
            article_categories = set(article.get('matched_categories', []))
            
            if not used_categories or not article_categories.intersection(used_categories):
                selected_topics.append(article)
                used_categories.update(article_categories)
            elif len(selected_topics) < max_topics // 2:
                # Allow some overlap for high-scoring articles
                selected_topics.append(article)
        
        logger.info(f"Selected {len(selected_topics)} diverse topics")
        return selected_topics
    
    async def _enhance_topics(self, topics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhance topics with additional metadata and suggestions"""
        enhanced_topics = []
        
        for topic in topics:
            enhanced_topic = topic.copy()
            
            # Generate article suggestions
            title = topic.get('title', '')
            description = topic.get('description', '')
            
            # Suggest article angles
            article_angles = self._suggest_article_angles(title, description)
            enhanced_topic['suggested_angles'] = article_angles
            
            # Estimate article potential
            potential_score = self._estimate_article_potential(topic)
            enhanced_topic['article_potential'] = potential_score
            
            # Suggest target keywords
            suggested_keywords = self._extract_potential_keywords(title, description)
            enhanced_topic['suggested_keywords'] = suggested_keywords
            
            enhanced_topics.append(enhanced_topic)
        
        return enhanced_topics
    
    def _suggest_article_angles(self, title: str, description: str) -> List[str]:
        """Suggest different angles for writing about the topic"""
        angles = []
        
        # Basic angles based on content
        if any(word in title.lower() for word in ['تقنية', 'ذكاء', 'تكنولوجيا']):
            angles.extend([
                'التأثير على المستقبل',
                'الفوائد والتحديات',
                'مقارنة مع التقنيات الأخرى',
                'التطبيقات العملية'
            ])
        
        if any(word in title.lower() for word in ['صحة', 'طب', 'علاج']):
            angles.extend([
                'الفوائد الصحية',
                'آراء الخبراء',
                'التجارب السريرية',
                'التكلفة والتوفر'
            ])
        
        if any(word in title.lower() for word in ['اقتصاد', 'أعمال', 'شركة']):
            angles.extend([
                'التأثير الاقتصادي',
                'فرص الاستثمار',
                'تحليل السوق',
                'التوقعات المستقبلية'
            ])
        
        # Default angles if no specific category
        if not angles:
            angles = [
                'نظرة شاملة',
                'التأثير على المجتمع',
                'الآراء المختلفة',
                'الخطوات التالية'
            ]
        
        return angles[:3]  # Return top 3 angles
    
    def _estimate_article_potential(self, topic: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate the potential success of an article about this topic"""
        score = topic.get('relevance_score', 0)
        
        potential = {
            'seo_potential': 'high' if score > 2.0 else 'medium' if score > 1.0 else 'low',
            'engagement_potential': 'high' if score > 1.5 else 'medium' if score > 0.8 else 'low',
            'trending_score': min(100, int(score * 20)),
            'recommended': score > 1.0
        }
        
        return potential
    
    def _extract_potential_keywords(self, title: str, description: str) -> List[str]:
        """Extract potential SEO keywords from title and description"""
        text = f"{title} {description}".lower()
        
        # Extract meaningful words (Arabic and English)
        words = re.findall(r'[\u0600-\u06FF\w]+', text)
        
        # Filter out common words and short words
        stop_words = {'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        
        meaningful_words = [
            word for word in words 
            if len(word) > 3 and word not in stop_words
        ]
        
        # Count frequency and return most common
        word_counts = Counter(meaningful_words)
        top_keywords = [word for word, count in word_counts.most_common(8)]
        
        return top_keywords
    
    async def get_random_trending_topic(
        self, 
        language: str = 'ar',
        category: str = None
    ) -> Optional[Dict[str, Any]]:
        """Get a single random trending topic"""
        try:
            topics = await self.discover_trending_topics(
                category=category,
                language=language,
                max_topics=10
            )
            
            if topics:
                # Select randomly from top topics
                selected_topic = random.choice(topics[:5])
                logger.info(f"Selected random topic: {selected_topic.get('title', 'Unknown')}")
                return selected_topic
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get random trending topic: {str(e)}")
            return None
    
    def configure_news_api(self, api_name: str, api_key: str) -> Dict[str, Any]:
        """Configure a news API"""
        try:
            if api_name in self.news_apis:
                self.news_apis[api_name]['api_key'] = api_key
                logger.info(f"Configured {api_name} API")
                return {
                    "success": True,
                    "message": f"{api_name} API configured successfully"
                }
            else:
                return {
                    "success": False,
                    "error": f"Unknown API: {api_name}"
                }
                
        except Exception as e:
            logger.error(f"Failed to configure {api_name} API: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Get status of auto news discovery system"""
        configured_apis = sum(1 for api in self.news_apis.values() if api.get('api_key'))
        
        return {
            'configured_apis': configured_apis,
            'total_apis': len(self.news_apis),
            'available_categories': list(self.topic_categories.keys()),
            'supported_languages': self.language_preferences,
            'quality_filters': self.quality_filters
        }
