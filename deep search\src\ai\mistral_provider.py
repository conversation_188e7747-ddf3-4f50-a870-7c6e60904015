"""
Mistral AI provider implementation
"""

import os
import aiohttp
import asyncio
from typing import List, Dict, Any, Optional
from loguru import logger
from .base_ai_provider import BaseAIProvider


class MistralProvider(BaseAIProvider):
    """AI provider using Mistral API"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = os.getenv('MISTRAL_API_KEY')
        self.base_url = "https://api.mistral.ai/v1"
        self.model_name = config.get('model', 'mistral-large-latest')
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _ensure_session(self):
        """Ensure session is available"""
        if not self.session:
            self.session = aiohttp.ClientSession()
    
    async def generate_text(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Generate text using Mistral API
        
        Args:
            prompt: User prompt
            system_prompt: System prompt (optional)
            **kwargs: Additional parameters
            
        Returns:
            Generated text
        """
        if not self.api_key:
            logger.error("Mistral API key not available")
            return ""
        
        await self._ensure_session()
        
        url = f"{self.base_url}/chat/completions"
        
        # Prepare messages
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        payload = {
            "model": self.model_name,
            "messages": messages,
            "temperature": kwargs.get('temperature', self.temperature),
            "max_tokens": kwargs.get('max_tokens', self.max_tokens),
            "top_p": 0.8
        }
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            async with self.session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if 'choices' in data and len(data['choices']) > 0:
                        content = data['choices'][0]['message']['content']
                        logger.info(f"Generated text with Mistral model: {self.model_name}")
                        return content.strip()
                    else:
                        logger.warning("No choices in Mistral response")
                        return ""
                else:
                    error_text = await response.text()
                    logger.error(f"Mistral API error {response.status}: {error_text}")
                    return ""
                    
        except Exception as e:
            logger.error(f"Mistral generation failed: {str(e)}")
            return ""
    
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings using Mistral API
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        if not self.api_key:
            logger.error("Mistral API key not available")
            return []
        
        await self._ensure_session()
        
        url = f"{self.base_url}/embeddings"
        
        embeddings = []
        
        for text in texts:
            payload = {
                "model": "mistral-embed",
                "input": [text]
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            try:
                async with self.session.post(url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if 'data' in data and len(data['data']) > 0:
                            embedding = data['data'][0]['embedding']
                            embeddings.append(embedding)
                        else:
                            logger.warning(f"No embedding data for text: {text[:50]}...")
                            embeddings.append([])
                    else:
                        error_text = await response.text()
                        logger.error(f"Mistral embedding error {response.status}: {error_text}")
                        embeddings.append([])
                        
            except Exception as e:
                logger.error(f"Mistral embedding failed: {str(e)}")
                embeddings.append([])
        
        logger.info(f"Generated {len(embeddings)} embeddings with Mistral")
        return embeddings
    
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> str:
        """
        Chat completion using Mistral API
        
        Args:
            messages: List of chat messages
            **kwargs: Additional parameters
            
        Returns:
            Chat response
        """
        if not self.api_key:
            logger.error("Mistral API key not available")
            return ""
        
        await self._ensure_session()
        
        url = f"{self.base_url}/chat/completions"
        
        payload = {
            "model": self.model_name,
            "messages": messages,
            "temperature": kwargs.get('temperature', self.temperature),
            "max_tokens": kwargs.get('max_tokens', self.max_tokens),
            "top_p": 0.8
        }
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            async with self.session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if 'choices' in data and len(data['choices']) > 0:
                        content = data['choices'][0]['message']['content']
                        logger.info(f"Chat completion with Mistral model: {self.model_name}")
                        return content.strip()
                    else:
                        logger.warning("No choices in Mistral chat response")
                        return ""
                else:
                    error_text = await response.text()
                    logger.error(f"Mistral chat API error {response.status}: {error_text}")
                    return ""
                    
        except Exception as e:
            logger.error(f"Mistral chat completion failed: {str(e)}")
            return ""
    
    async def health_check(self) -> bool:
        """
        Check if Mistral service is available
        
        Returns:
            True if service is healthy
        """
        if not self.api_key:
            return False
        
        try:
            # Simple test generation
            test_response = await self.generate_text("Hello", max_tokens=10)
            
            if test_response:
                logger.info("Mistral health check passed")
                return True
            else:
                logger.error("Mistral health check failed: empty response")
                return False
                
        except Exception as e:
            logger.error(f"Mistral health check failed: {str(e)}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the Mistral model
        
        Returns:
            Model information dictionary
        """
        return {
            "provider": "mistral",
            "model": self.model_name,
            "api_key_configured": bool(self.api_key),
            "supports_embeddings": True,
            "supports_chat": True,
            "supports_text_generation": True
        }
