{"time":"2025-08-06T16:02:16.0191402Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-06T16:02:16.0231384Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-06T16:02:16.5260326Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-06T16:02:17.9782885Z","level":"INFO","msg":"OK   20250424200609_initial.sql (2.57ms)"}
{"time":"2025-08-06T16:02:17.9793208Z","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (1.03ms)"}
{"time":"2025-08-06T16:02:17.9798339Z","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (513.1µs)"}
{"time":"2025-08-06T16:02:17.9803463Z","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (512.4µs)"}
{"time":"2025-08-06T16:02:17.9803463Z","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-08-06T16:02:17.9808618Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-06T16:02:17.989626Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-06T16:02:17.9901422Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-06T16:02:17.9991786Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-06T16:02:17.9991786Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-06T16:02:38.5717734Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-06T16:02:38.5717734Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-06T16:02:38.579181Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-06T16:02:38.579181Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-06T16:02:45.3197903Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":627},"msg":"Tool call started","toolCall":{"id":"chatcmpl-tool-3579732d71b94dd1a24f576d093cb60d","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-06T16:02:45.4830625Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":627},"msg":"Tool call started","toolCall":{"id":"chatcmpl-tool-df91dd73062f40a987dc9bb1679c59c5","name":"agent","input":"","type":"","finished":false}}
{"time":"2025-08-06T16:02:45.7947939Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":627},"msg":"Tool call started","toolCall":{"id":"chatcmpl-tool-5d2656a75fe5413282d91d8bed37000c","name":"agent","input":"","type":"","finished":false}}
{"time":"2025-08-06T16:02:49.1299314Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:02:49.1299314Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":1,"max_retries":8}
{"time":"2025-08-06T16:02:54.9909913Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:02:54.9909913Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":2,"max_retries":8}
{"time":"2025-08-06T16:03:18.4080944Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":627},"msg":"Tool call started","toolCall":{"id":"chatcmpl-tool-f21e4f6f719c4d79ad4792db9ad7e742","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-06T16:03:18.4110928Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":627},"msg":"Tool call started","toolCall":{"id":"chatcmpl-tool-39f4d0cfe2fd48debfb5604c5065b1c9","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-06T16:03:18.5952492Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/tools.init.func1","file":"/home/<USER>/work/crush/crush/internal/llm/tools/rg.go","line":18},"msg":"Ripgrep (rg) not found in $PATH. Some grep features might be limited or slower."}
{"time":"2025-08-06T16:03:21.9390394Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:03:21.9390394Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":1,"max_retries":8}
{"time":"2025-08-06T16:03:27.6825063Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:03:27.6825063Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":2,"max_retries":8}
{"time":"2025-08-06T16:03:35.9001582Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:03:35.9001582Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":3,"max_retries":8}
{"time":"2025-08-06T16:03:48.9651948Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:03:48.9651948Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":4,"max_retries":8}
{"time":"2025-08-06T16:04:09.1782101Z","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Run.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":347},"msg":"failed to process events: POST \"https://openrouter.ai/api/v1/chat/completions\": 400 Bad Request {\"message\":\"Provider returned error\",\"code\":400,\"metadata\":{\"raw\":\"{\\\"details\\\":{\\\"_errors\\\":[],\\\"messages\\\":{\\\"2\\\":{\\\"_errors\\\":[],\\\"role\\\":{\\\"_errors\\\":[\\\"Invalid literal value, expected \\\\\\\"user\\\\\\\"\\\",\\\"Invalid literal value, expected \\\\\\\"tool\\\\\\\"\\\",\\\"Invalid literal value, expected \\\\\\\"system\\\\\\\"\\\"]},\\\"content\\\":{\\\"_errors\\\":[\\\"Required\\\",\\\"Required\\\",\\\"Required\\\",\\\"Required\\\",\\\"Required\\\",\\\"Required\\\",\\\"Required\\\"]},\\\"tool_call_id\\\":{\\\"_errors\\\":[\\\"Required\\\"]}},\\\"_errors\\\":[]}},\\\"issues\\\":[{\\\"code\\\":\\\"invalid_union\\\",\\\"unionErrors\\\":[{\\\"issues\\\":[{\\\"received\\\":\\\"assistant\\\",\\\"code\\\":\\\"invalid_literal\\\",\\\"expected\\\":\\\"user\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"role\\\"],\\\"message\\\":\\\"Invalid literal value, expected \\\\\\\"user\\\\\\\"\\\"},{\\\"code\\\":\\\"invalid_union\\\",\\\"unionErrors\\\":[{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"string\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Required\\\"}],\\\"name\\\":\\\"ZodError\\\"},{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"array\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Required\\\"}],\\\"name\\\":\\\"ZodError\\\"}],\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Invalid input\\\"}],\\\"name\\\":\\\"ZodError\\\"},{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_union\\\",\\\"unionErrors\\\":[{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"string\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Required\\\"}],\\\"name\\\":\\\"ZodError\\\"},{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"array\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Required\\\"}],\\\"name\\\":\\\"ZodError\\\"}],\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Invalid input\\\"}],\\\"name\\\":\\\"ZodError\\\"},{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"string\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Required\\\"},{\\\"received\\\":\\\"assistant\\\",\\\"code\\\":\\\"invalid_literal\\\",\\\"expected\\\":\\\"tool\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"role\\\"],\\\"message\\\":\\\"Invalid literal value, expected \\\\\\\"tool\\\\\\\"\\\"},{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"string\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"tool_call_id\\\"],\\\"message\\\":\\\"Required\\\"}],\\\"name\\\":\\\"ZodError\\\"},{\\\"issues\\\":[{\\\"received\\\":\\\"assistant\\\",\\\"code\\\":\\\"invalid_literal\\\",\\\"expected\\\":\\\"system\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"role\\\"],\\\"message\\\":\\\"Invalid literal value, expected \\\\\\\"system\\\\\\\"\\\"},{\\\"code\\\":\\\"invalid_union\\\",\\\"unionErrors\\\":[{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"string\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Required\\\"}],\\\"name\\\":\\\"ZodError\\\"},{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"array\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Required\\\"}],\\\"name\\\":\\\"ZodError\\\"}],\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Invalid input\\\"}],\\\"name\\\":\\\"ZodError\\\"}],\\\"path\\\":[\\\"messages\\\",2],\\\"message\\\":\\\"Invalid input\\\"}]}\",\"provider_name\":\"Venice\"}}"}
{"time":"2025-08-06T16:04:09.1782101Z","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).streamAndHandleEvents","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":555},"msg":"Tool execution error","toolCall":"chatcmpl-tool-df91dd73062f40a987dc9bb1679c59c5","error":"error generating agent: failed to process events: POST \"https://openrouter.ai/api/v1/chat/completions\": 400 Bad Request {\"message\":\"Provider returned error\",\"code\":400,\"metadata\":{\"raw\":\"{\\\"details\\\":{\\\"_errors\\\":[],\\\"messages\\\":{\\\"2\\\":{\\\"_errors\\\":[],\\\"role\\\":{\\\"_errors\\\":[\\\"Invalid literal value, expected \\\\\\\"user\\\\\\\"\\\",\\\"Invalid literal value, expected \\\\\\\"tool\\\\\\\"\\\",\\\"Invalid literal value, expected \\\\\\\"system\\\\\\\"\\\"]},\\\"content\\\":{\\\"_errors\\\":[\\\"Required\\\",\\\"Required\\\",\\\"Required\\\",\\\"Required\\\",\\\"Required\\\",\\\"Required\\\",\\\"Required\\\"]},\\\"tool_call_id\\\":{\\\"_errors\\\":[\\\"Required\\\"]}},\\\"_errors\\\":[]}},\\\"issues\\\":[{\\\"code\\\":\\\"invalid_union\\\",\\\"unionErrors\\\":[{\\\"issues\\\":[{\\\"received\\\":\\\"assistant\\\",\\\"code\\\":\\\"invalid_literal\\\",\\\"expected\\\":\\\"user\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"role\\\"],\\\"message\\\":\\\"Invalid literal value, expected \\\\\\\"user\\\\\\\"\\\"},{\\\"code\\\":\\\"invalid_union\\\",\\\"unionErrors\\\":[{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"string\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Required\\\"}],\\\"name\\\":\\\"ZodError\\\"},{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"array\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Required\\\"}],\\\"name\\\":\\\"ZodError\\\"}],\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Invalid input\\\"}],\\\"name\\\":\\\"ZodError\\\"},{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_union\\\",\\\"unionErrors\\\":[{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"string\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Required\\\"}],\\\"name\\\":\\\"ZodError\\\"},{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"array\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Required\\\"}],\\\"name\\\":\\\"ZodError\\\"}],\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Invalid input\\\"}],\\\"name\\\":\\\"ZodError\\\"},{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"string\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Required\\\"},{\\\"received\\\":\\\"assistant\\\",\\\"code\\\":\\\"invalid_literal\\\",\\\"expected\\\":\\\"tool\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"role\\\"],\\\"message\\\":\\\"Invalid literal value, expected \\\\\\\"tool\\\\\\\"\\\"},{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"string\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"tool_call_id\\\"],\\\"message\\\":\\\"Required\\\"}],\\\"name\\\":\\\"ZodError\\\"},{\\\"issues\\\":[{\\\"received\\\":\\\"assistant\\\",\\\"code\\\":\\\"invalid_literal\\\",\\\"expected\\\":\\\"system\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"role\\\"],\\\"message\\\":\\\"Invalid literal value, expected \\\\\\\"system\\\\\\\"\\\"},{\\\"code\\\":\\\"invalid_union\\\",\\\"unionErrors\\\":[{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"string\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Required\\\"}],\\\"name\\\":\\\"ZodError\\\"},{\\\"issues\\\":[{\\\"code\\\":\\\"invalid_type\\\",\\\"expected\\\":\\\"array\\\",\\\"received\\\":\\\"undefined\\\",\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Required\\\"}],\\\"name\\\":\\\"ZodError\\\"}],\\\"path\\\":[\\\"messages\\\",2,\\\"content\\\"],\\\"message\\\":\\\"Invalid input\\\"}],\\\"name\\\":\\\"ZodError\\\"}],\\\"path\\\":[\\\"messages\\\",2],\\\"message\\\":\\\"Invalid input\\\"}]}\",\"provider_name\":\"Venice\"}}"}
{"time":"2025-08-06T16:04:12.611757Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:04:12.611757Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":1,"max_retries":8}
{"time":"2025-08-06T16:04:18.2416985Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:04:18.2416985Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":2,"max_retries":8}
{"time":"2025-08-06T16:04:26.3023757Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:04:26.3023757Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":3,"max_retries":8}
{"time":"2025-08-06T16:04:39.3090827Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:04:39.3090827Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":4,"max_retries":8}
{"time":"2025-08-06T16:05:06.8543052Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":627},"msg":"Tool call started","toolCall":{"id":"chatcmpl-tool-d3aaf11a4f4d43b0bf32838af9c4eb54","name":"grep","input":"","type":"","finished":false}}
{"time":"2025-08-06T16:05:12.5024406Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:05:12.5024406Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":1,"max_retries":8}
{"time":"2025-08-06T16:05:18.2731156Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:05:18.2731156Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":2,"max_retries":8}
{"time":"2025-08-06T16:05:26.4089326Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:05:26.4089326Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":3,"max_retries":8}
{"time":"2025-08-06T16:05:39.523765Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:05:39.523765Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":4,"max_retries":8}
{"time":"2025-08-06T16:06:02.1125423Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:06:02.1125423Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":5,"max_retries":8}
{"time":"2025-08-06T16:06:45.5803757Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:06:45.5803757Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":6,"max_retries":8}
{"time":"2025-08-06T16:08:06.313308Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:08:06.313308Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":7,"max_retries":8}
{"time":"2025-08-06T16:09:02.8680879Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Cancel","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":241},"msg":"Request cancellation initiated","session_id":"dc438219-22b3-499b-b26e-500d1afa2b61"}
{"time":"2025-08-06T16:09:22.4612103Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-06T16:09:22.4648271Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-06T16:09:23.0325609Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-06T16:09:23.9110608Z","level":"INFO","msg":"goose: no migrations to run. current version: 20250627000000"}
{"time":"2025-08-06T16:09:23.9110608Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-06T16:09:23.9210531Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-06T16:09:23.9210531Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-06T16:09:23.9320489Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-06T16:09:23.9320489Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-06T16:10:16.9856929Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:10:16.98572Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":1,"max_retries":8}
{"time":"2025-08-06T16:10:22.9441948Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:10:22.9441948Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":2,"max_retries":8}
{"time":"2025-08-06T16:10:31.3782404Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:10:31.3782404Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":3,"max_retries":8}
{"time":"2025-08-06T16:10:44.5209038Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:10:44.5209038Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":4,"max_retries":8}
{"time":"2025-08-06T16:11:07.182021Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:11:07.182021Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":5,"max_retries":8}
{"time":"2025-08-06T16:11:49.3884417Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).shouldRetry","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":516},"msg":"OpenAI API error","status_code":429,"message":"Rate limit exceeded: limit_rpm/qwen/qwen3-4b-04-28/2e98edb5-b21b-455b-afb4-d5c01aad515d. High demand for qwen/qwen3-4b:free on OpenRouter - limited to 1 requests per minute. Please retry shortly.","type":""}
{"time":"2025-08-06T16:11:49.3884417Z","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*openaiClient).stream.func1","file":"/home/<USER>/work/crush/crush/internal/llm/provider/openai.go","line":465},"msg":"Retrying due to rate limit","attempt":6,"max_retries":8}
{"time":"2025-08-06T16:12:06.3706237Z","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Cancel","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":241},"msg":"Request cancellation initiated","session_id":"2855291d-5425-458f-925c-4b3d35ef671b"}
