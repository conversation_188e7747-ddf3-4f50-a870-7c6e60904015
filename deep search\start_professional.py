#!/usr/bin/env python3
"""
Professional News Agent Server - Simplified startup without embedding
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    print("🚀 Starting DeepSearch Professional News Agent")
    print("📰 Specialized for AI agents with full article content")
    print("=" * 60)
    
    # Load configuration
    print("Loading configuration...")
    from src.utils.config_loader import load_config
    config = load_config()
    print("✅ Configuration loaded")
    
    # Display professional settings
    news_config = config.get('news', {})
    print("\n📋 Professional Settings:")
    print(f"  • Professional mode: {'✅' if news_config.get('professional_mode', True) else '❌'}")
    print(f"  • Single article default: {'✅' if news_config.get('single_article_default', True) else '❌'}")
    print(f"  • Max articles (multi): {news_config.get('max_articles_multi', 3)}")
    print(f"  • Full content extraction: {'✅' if news_config.get('extract_full_content_default', True) else '❌'}")
    print(f"  • Duplicate prevention: ✅ (7 days history)")
    
    print("\nSetting up logging...")
    from src.utils.logger import setup_logging
    setup_logging(config)
    print("✅ Logging configured")
    
    print("Starting FastAPI server...")
    import uvicorn
    from src.api.main import app
    
    # Get API configuration
    api_config = config.get('api', {})
    host = api_config.get('host', '0.0.0.0')
    port = api_config.get('port', 8000)
    
    print(f"\n🌐 Server starting on http://{host}:{port}")
    print(f"📰 Professional Interface: http://localhost:{port}/web/professional.html")
    print(f"📱 News Interface: http://localhost:{port}/web/news.html")
    print(f"📚 API Docs: http://localhost:{port}/docs")
    print()
    print("🤖 Professional API Endpoints:")
    print("  • POST /news/professional - Get professional articles")
    print("  • GET /news/professional/single - Get single article (simplified)")
    print("  • GET /news/professional/stats/{agent_id} - Agent statistics")
    print("  • DELETE /news/professional/history/{agent_id} - Clear agent history")
    print()
    print("💡 Example usage:")
    print("  curl 'http://localhost:8000/news/professional/single?query=آخر أخبار كرة القدم&agent_id=sports_bot'")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    # Start server
    uvicorn.run(
        app,
        host=host,
        port=port,
        reload=False,
        log_level="info"
    )
    
except KeyboardInterrupt:
    print("\n⏹️ Server stopped by user")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install required packages:")
    print("pip install fastapi uvicorn loguru pydantic aiohttp beautifulsoup4")
except Exception as e:
    print(f"❌ Error starting server: {e}")
    import traceback
    traceback.print_exc()
