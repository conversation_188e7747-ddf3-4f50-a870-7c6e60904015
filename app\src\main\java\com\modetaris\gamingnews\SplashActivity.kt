package com.modetaris.gamingnews

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.animation.AnimationUtils
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.modetaris.gamingnews.R

class SplashActivity : AppCompatActivity() {
    
    companion object {
        private const val SPLASH_DURATION = 2500L // 2.5 ثانية
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)

        // إعداد الرسوم المتحركة
        setupAnimations()

        // الانتقال إلى النشاط الرئيسي بعد التأخير
        Handler(Looper.getMainLooper()).postDelayed({
            val intent = Intent(this@SplashActivity, MainActivity::class.java)
            startActivity(intent)
            finish()
            
            // تأثير انتقال سلس
            overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
        }, SPLASH_DURATION)
    }

    private fun setupAnimations() {
        // العثور على العناصر
        val logo = findViewById<ImageView>(R.id.splash_logo)
        val appName = findViewById<TextView>(R.id.splash_app_name)
        val appDescription = findViewById<TextView>(R.id.splash_description)

        // تطبيق رسوم متحركة بسيطة
        try {
            // تأثير fade in بسيط
            logo.alpha = 0f
            appName.alpha = 0f
            appDescription.alpha = 0f

            logo.animate().alpha(1f).setDuration(800).start()

            Handler(Looper.getMainLooper()).postDelayed({
                appName.animate().alpha(1f).setDuration(600).start()
            }, 300)

            Handler(Looper.getMainLooper()).postDelayed({
                appDescription.animate().alpha(1f).setDuration(600).start()
            }, 600)
        } catch (e: Exception) {
            // إذا حدث خطأ، اعرض العناصر مباشرة
            logo.alpha = 1f
            appName.alpha = 1f
            appDescription.alpha = 1f
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        // منع المستخدم من العودة أثناء شاشة البداية
        // لا نفعل شيئاً هنا
    }
}
