"""
News Engine - Specialized news search and aggregation for AI agents
"""

import asyncio
import re
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from loguru import logger
from ..scrapers.scraper_manager import ScraperManager
from ..ai.ai_manager import AIManager
from ..database.database_manager import DatabaseManager
from .article_tracker import ArticleTracker
from .content_extractor import ContentExtractor


class NewsEngine:
    """Specialized news search engine for AI agents"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.scraper_manager = ScraperManager(config)
        self.database_manager = DatabaseManager(config)
        self.ai_manager = None  # Will be initialized in async context
        self.article_tracker = ArticleTracker(config)
        self.content_extractor = None  # Will be initialized in async context

        # News-specific configuration
        self.news_config = config.get('news', {})
        self.max_news_age_hours = self.news_config.get('max_age_hours', 24)
        self.preferred_sources = self.news_config.get('preferred_sources', [])
        self.news_keywords = self._load_news_keywords()

        # Professional article settings
        self.single_article_mode = True  # Default to single article
        self.max_articles_multi = 3     # Maximum 3 articles when multiple requested
    
    def _load_news_keywords(self) -> Dict[str, List[str]]:
        """Load news category keywords for better search"""
        return {
            'sports': ['كرة القدم', 'كرة السلة', 'تنس', 'رياضة', 'مباراة', 'بطولة', 'فريق'],
            'technology': ['تكنولوجيا', 'ذكاء اصطناعي', 'برمجة', 'تطبيق', 'شركة تقنية', 'ابتكار'],
            'crypto': ['بيتكوين', 'عملة رقمية', 'كريبتو', 'بلوك تشين', 'إيثيريوم'],
            'politics': ['سياسة', 'حكومة', 'انتخابات', 'رئيس', 'وزير', 'برلمان'],
            'economy': ['اقتصاد', 'بورصة', 'أسهم', 'تضخم', 'بنك', 'استثمار'],
            'health': ['صحة', 'طب', 'مرض', 'علاج', 'دواء', 'مستشفى'],
            'war': ['حرب', 'صراع', 'عسكري', 'جيش', 'قتال', 'نزاع'],
            'general': ['خبر', 'أخبار', 'جديد', 'عاجل', 'حدث', 'تطور']
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.ai_manager = AIManager(self.config)
        await self.ai_manager.__aenter__()
        self.content_extractor = ContentExtractor(self.config)
        await self.content_extractor.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.ai_manager:
            await self.ai_manager.__aexit__(exc_type, exc_val, exc_tb)
        if self.content_extractor:
            await self.content_extractor.__aexit__(exc_type, exc_val, exc_tb)
    
    def _enhance_news_query(self, query: str) -> str:
        """Enhance query for better news results"""
        # Add time-based keywords
        today = datetime.now()
        time_keywords = [
            f"اليوم {today.strftime('%Y-%m-%d')}",
            "آخر الأخبار",
            "أخبار حديثة",
            today.strftime('%Y'),
            "عاجل"
        ]
        
        # Detect category and add relevant keywords
        query_lower = query.lower()
        category_keywords = []
        
        for category, keywords in self.news_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                category_keywords.extend(keywords[:2])  # Add top 2 keywords
                break
        
        # Combine query with enhancements
        enhanced_parts = [query]
        enhanced_parts.extend(time_keywords[:2])  # Add 2 time keywords
        enhanced_parts.extend(category_keywords[:1])  # Add 1 category keyword
        
        return " ".join(enhanced_parts)
    
    def _is_recent_news(self, article: Dict[str, Any]) -> bool:
        """Check if article is recent news"""
        try:
            scraped_at = article.get('scraped_at')
            if isinstance(scraped_at, str):
                scraped_time = datetime.fromisoformat(scraped_at.replace('Z', '+00:00'))
            else:
                scraped_time = datetime.now()
            
            # Check if article is within the max age
            max_age = timedelta(hours=self.max_news_age_hours)
            return datetime.now() - scraped_time <= max_age
            
        except Exception:
            return True  # Default to including the article
    
    def _extract_date_from_content(self, content: str) -> Optional[str]:
        """Extract date information from article content"""
        # Common Arabic date patterns
        date_patterns = [
            r'(\d{1,2}[-/]\d{1,2}[-/]\d{4})',  # DD/MM/YYYY or DD-MM-YYYY
            r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})',  # YYYY/MM/DD or YYYY-MM-DD
            r'(اليوم|أمس|البارحة)',
            r'(\d{1,2}\s+(يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر)\s+\d{4})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, content)
            if match:
                return match.group(1)
        
        return None
    
    def _calculate_news_score(self, article: Dict[str, Any], query: str) -> float:
        """Calculate relevance score for news article"""
        score = 0.0
        
        title = article.get('title', '').lower()
        content = article.get('content', '').lower()
        query_lower = query.lower()
        
        # Title relevance (high weight)
        if query_lower in title:
            score += 3.0
        
        # Content relevance
        if query_lower in content:
            score += 1.0
        
        # Recency bonus
        if self._is_recent_news(article):
            score += 2.0
        
        # Date mention bonus
        if self._extract_date_from_content(content):
            score += 1.0
        
        # News keywords bonus
        news_indicators = ['خبر', 'أخبار', 'عاجل', 'جديد', 'تطور', 'حدث']
        for indicator in news_indicators:
            if indicator in title or indicator in content:
                score += 0.5
                break
        
        # Source reliability bonus (if from preferred sources)
        url = article.get('url', '')
        for source in self.preferred_sources:
            if source in url:
                score += 1.0
                break
        
        return score
    
    async def search_latest_news(
        self,
        query: str,
        max_results: int = 10,
        category: str = None
    ) -> Dict[str, Any]:
        """
        Search for latest news on a specific topic
        
        Args:
            query: News search query
            max_results: Maximum number of results
            category: News category (optional)
            
        Returns:
            News search results with metadata
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"Searching latest news for: '{query}'")
            
            # Enhance query for news search
            enhanced_query = self._enhance_news_query(query)
            logger.info(f"Enhanced news query: '{enhanced_query}'")
            
            # Search for articles
            articles = await self.scraper_manager.search_and_scrape(
                enhanced_query,
                max_results=max_results * 2,  # Get more to filter
                engines=['duckduckgo']  # Focus on free engines for news
            )
            
            if not articles:
                return {
                    "query": query,
                    "enhanced_query": enhanced_query,
                    "news_articles": [],
                    "total_results": 0,
                    "category": category,
                    "search_time": datetime.now().isoformat(),
                    "processing_time": (datetime.now() - start_time).total_seconds()
                }
            
            # Filter and score articles for news relevance
            news_articles = []
            for article in articles:
                # Calculate news relevance score
                news_score = self._calculate_news_score(article, query)
                article['news_score'] = news_score
                article['extracted_date'] = self._extract_date_from_content(
                    article.get('content', '')
                )
                
                # Only include articles with decent news score
                if news_score >= 1.0:
                    news_articles.append(article)
            
            # Sort by news score (descending)
            news_articles.sort(key=lambda x: x.get('news_score', 0), reverse=True)
            
            # Limit to requested number
            news_articles = news_articles[:max_results]
            
            # Process with AI for summaries and analysis
            if self.ai_manager and news_articles:
                # Generate news summaries
                news_articles = await self._generate_news_summaries(news_articles)
                
                # Extract key information
                news_articles = await self._extract_news_info(news_articles)
            
            # Save to database
            for article in news_articles:
                await self.database_manager.save_article(article)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = {
                "query": query,
                "enhanced_query": enhanced_query,
                "news_articles": news_articles,
                "total_results": len(news_articles),
                "category": category or self._detect_category(query),
                "search_time": datetime.now().isoformat(),
                "processing_time": processing_time,
                "sources_used": list(set(article.get('source_engine', '') for article in news_articles))
            }
            
            logger.info(f"Found {len(news_articles)} news articles in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"News search failed: {str(e)}")
            return {
                "query": query,
                "enhanced_query": query,
                "news_articles": [],
                "total_results": 0,
                "category": category,
                "search_time": datetime.now().isoformat(),
                "processing_time": (datetime.now() - start_time).total_seconds(),
                "error": str(e)
            }
    
    def _detect_category(self, query: str) -> str:
        """Detect news category from query"""
        query_lower = query.lower()
        
        for category, keywords in self.news_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                return category
        
        return 'general'

    async def get_professional_articles(
        self,
        query: str,
        agent_id: str = "default",
        max_articles: int = 1,
        extract_full_content: bool = True
    ) -> Dict[str, Any]:
        """
        Get professional articles with full content for AI agents

        Args:
            query: News search query
            agent_id: Unique identifier for the requesting agent
            max_articles: Number of articles (1 for single, max 3 for multiple)
            extract_full_content: Whether to extract full article content

        Returns:
            Professional articles with full content and metadata
        """
        start_time = datetime.now()

        try:
            logger.info(f"Getting professional articles for agent {agent_id}: '{query}'")

            # Limit articles based on request
            if max_articles == 1:
                requested_count = 1
            else:
                requested_count = min(max_articles, self.max_articles_multi)

            # Search for more articles to have options after filtering
            search_count = requested_count * 5

            # Get initial articles
            search_result = await self.search_latest_news(
                query=query,
                max_results=search_count,
                category=self._detect_category(query)
            )

            if not search_result.get('news_articles'):
                return {
                    "query": query,
                    "agent_id": agent_id,
                    "professional_articles": [],
                    "total_found": 0,
                    "total_delivered": 0,
                    "processing_time": (datetime.now() - start_time).total_seconds(),
                    "message": "لم يتم العثور على مقالات جديدة"
                }

            # Filter out already delivered articles
            new_articles = self.article_tracker.filter_new_articles(
                search_result['news_articles']
            )

            if not new_articles:
                return {
                    "query": query,
                    "agent_id": agent_id,
                    "professional_articles": [],
                    "total_found": len(search_result['news_articles']),
                    "total_delivered": 0,
                    "processing_time": (datetime.now() - start_time).total_seconds(),
                    "message": "جميع المقالات الموجودة تم تقديمها مسبقاً"
                }

            # Select best articles based on quality score
            selected_articles = self._select_best_articles(new_articles, requested_count)

            # Extract full content if requested
            if extract_full_content and self.content_extractor:
                logger.info("Extracting full content for professional articles...")
                selected_articles = await self.content_extractor.extract_multiple_contents(
                    selected_articles
                )

            # Generate professional summaries and analysis
            if self.ai_manager:
                selected_articles = await self._enhance_for_professional_use(selected_articles)

            # Mark articles as delivered
            self.article_tracker.mark_articles_delivered(selected_articles, agent_id)

            # Save to database
            for article in selected_articles:
                await self.database_manager.save_article(article)

            processing_time = (datetime.now() - start_time).total_seconds()

            result = {
                "query": query,
                "agent_id": agent_id,
                "professional_articles": selected_articles,
                "total_found": len(search_result['news_articles']),
                "total_new": len(new_articles),
                "total_delivered": len(selected_articles),
                "processing_time": processing_time,
                "extraction_enabled": extract_full_content,
                "delivery_time": datetime.now().isoformat(),
                "message": f"تم تقديم {len(selected_articles)} مقال احترافي جديد"
            }

            logger.info(f"Delivered {len(selected_articles)} professional articles to agent {agent_id} in {processing_time:.2f}s")
            return result

        except Exception as e:
            logger.error(f"Professional articles retrieval failed: {str(e)}")
            return {
                "query": query,
                "agent_id": agent_id,
                "professional_articles": [],
                "total_found": 0,
                "total_delivered": 0,
                "processing_time": (datetime.now() - start_time).total_seconds(),
                "error": str(e)
            }
    
    async def _generate_news_summaries(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate news-focused summaries"""
        for article in articles:
            try:
                content = article.get('content', '')
                if len(content) > 200:
                    # Create news-focused summary prompt
                    news_summary = await self._create_news_summary(content)
                    article['news_summary'] = news_summary
                    article['has_news_summary'] = True
                else:
                    article['news_summary'] = content
                    article['has_news_summary'] = False
            except Exception as e:
                logger.error(f"Failed to generate news summary: {str(e)}")
                article['news_summary'] = article.get('content', '')[:300]
                article['has_news_summary'] = False
        
        return articles
    
    async def _create_news_summary(self, content: str) -> str:
        """Create a news-focused summary"""
        if not self.ai_manager or not self.ai_manager.provider:
            return content[:300]
        
        try:
            system_prompt = """أنت محرر أخبار متخصص. مهمتك تلخيص الأخبار بشكل دقيق ومفيد.
            ركز على: من، ماذا، متى، أين، لماذا، كيف.
            اكتب ملخصاً إخبارياً واضحاً ومختصراً."""
            
            prompt = f"""لخص هذا الخبر في 2-3 جمل تتضمن أهم المعلومات:

{content[:2000]}

الملخص الإخباري:"""
            
            summary = await self.ai_manager.provider.generate_text(prompt, system_prompt)
            return summary.strip() if summary else content[:300]
            
        except Exception as e:
            logger.error(f"AI summary generation failed: {str(e)}")
            return content[:300]
    
    async def _extract_news_info(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract key news information"""
        for article in articles:
            try:
                # Extract key entities and information
                content = article.get('content', '')
                
                # Simple extraction (can be enhanced with NLP)
                article['key_entities'] = self._extract_entities(content)
                article['news_type'] = self._classify_news_type(content)
                
            except Exception as e:
                logger.error(f"Failed to extract news info: {str(e)}")
                article['key_entities'] = []
                article['news_type'] = 'general'
        
        return articles
    
    def _extract_entities(self, content: str) -> List[str]:
        """Simple entity extraction"""
        # This is a simplified version - can be enhanced with proper NLP
        entities = []
        
        # Look for common entity patterns
        patterns = [
            r'(الرئيس\s+\w+)',
            r'(الوزير\s+\w+)',
            r'(شركة\s+\w+)',
            r'(فريق\s+\w+)',
            r'(\w+\s+دولار)',
            r'(\d+%)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content)
            entities.extend(matches)
        
        return entities[:5]  # Return top 5 entities
    
    def _classify_news_type(self, content: str) -> str:
        """Classify type of news"""
        content_lower = content.lower()
        
        if any(word in content_lower for word in ['عاجل', 'طارئ', 'فوري']):
            return 'breaking'
        elif any(word in content_lower for word in ['تحليل', 'رأي', 'تعليق']):
            return 'analysis'
        elif any(word in content_lower for word in ['مقابلة', 'حوار', 'لقاء']):
            return 'interview'
        else:
            return 'regular'

    def _select_best_articles(self, articles: List[Dict[str, Any]], count: int) -> List[Dict[str, Any]]:
        """Select best articles based on quality metrics"""
        # Sort by news score and content quality
        scored_articles = []

        for article in articles:
            score = article.get('news_score', 0)

            # Bonus for recent articles
            if self._is_recent_news(article):
                score += 1.0

            # Bonus for longer content
            content_length = len(article.get('content', ''))
            if content_length > 1000:
                score += 0.5
            if content_length > 2000:
                score += 0.5

            # Bonus for having extracted date
            if article.get('extracted_date'):
                score += 0.3

            # Bonus for key entities
            if article.get('key_entities'):
                score += 0.2

            article['selection_score'] = score
            scored_articles.append(article)

        # Sort by selection score (descending)
        scored_articles.sort(key=lambda x: x.get('selection_score', 0), reverse=True)

        return scored_articles[:count]

    async def _enhance_for_professional_use(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhance articles for professional use by AI agents"""
        enhanced_articles = []

        for article in articles:
            try:
                # Generate professional summary
                professional_summary = await self._generate_professional_summary(article)

                # Extract key insights
                key_insights = await self._extract_key_insights(article)

                # Generate writing suggestions
                writing_suggestions = await self._generate_writing_suggestions(article)

                # Update article
                enhanced_article = article.copy()
                enhanced_article.update({
                    'professional_summary': professional_summary,
                    'key_insights': key_insights,
                    'writing_suggestions': writing_suggestions,
                    'professional_enhancement': True,
                    'enhancement_time': datetime.now().isoformat()
                })

                enhanced_articles.append(enhanced_article)

            except Exception as e:
                logger.error(f"Failed to enhance article professionally: {str(e)}")
                enhanced_articles.append(article)  # Add original

        return enhanced_articles

    async def _generate_professional_summary(self, article: Dict[str, Any]) -> str:
        """Generate professional summary for AI agents"""
        if not self.ai_manager or not self.ai_manager.provider:
            return article.get('news_summary', article.get('content', '')[:500])

        try:
            content = article.get('full_content') or article.get('content', '')
            if not content:
                return "لا يوجد محتوى متاح"

            system_prompt = """أنت محرر محترف متخصص في تحليل الأخبار للوكلاء البرمجيين.
            مهمتك إنشاء ملخص احترافي شامل يساعد الوكيل على كتابة مقال متميز.
            ركز على: الحقائق الرئيسية، السياق، التأثير، والزوايا المهمة."""

            prompt = f"""حلل هذا المقال الإخباري واكتب ملخصاً احترافياً شاملاً:

العنوان: {article.get('title', 'غير محدد')}

المحتوى:
{content[:3000]}

اكتب ملخصاً احترافياً يتضمن:
1. الحدث الرئيسي والحقائق المهمة
2. السياق والخلفية
3. الأطراف المعنية والتأثيرات
4. الزوايا المهمة للكتابة

الملخص الاحترافي:"""

            summary = await self.ai_manager.provider.generate_text(prompt, system_prompt)
            return summary.strip() if summary else article.get('news_summary', content[:500])

        except Exception as e:
            logger.error(f"Professional summary generation failed: {str(e)}")
            return article.get('news_summary', article.get('content', '')[:500])

    async def _extract_key_insights(self, article: Dict[str, Any]) -> List[str]:
        """Extract key insights for professional writing"""
        if not self.ai_manager or not self.ai_manager.provider:
            return []

        try:
            content = article.get('full_content') or article.get('content', '')
            if not content:
                return []

            system_prompt = """أنت محلل أخبار خبير. استخرج النقاط الرئيسية والرؤى المهمة من المقال."""

            prompt = f"""استخرج أهم 5 نقاط ورؤى من هذا المقال:

{content[:2000]}

اكتب النقاط الرئيسية (نقطة واحدة في كل سطر):"""

            insights_text = await self.ai_manager.provider.generate_text(prompt, system_prompt)

            if insights_text:
                # Split into individual insights
                insights = [insight.strip() for insight in insights_text.split('\n') if insight.strip()]
                return insights[:5]  # Limit to 5 insights

            return []

        except Exception as e:
            logger.error(f"Key insights extraction failed: {str(e)}")
            return []

    async def _generate_writing_suggestions(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """Generate writing suggestions for AI agents"""
        if not self.ai_manager or not self.ai_manager.provider:
            return {}

        try:
            content = article.get('full_content') or article.get('content', '')
            title = article.get('title', '')

            suggestions = {
                'suggested_angles': [],
                'key_quotes': [],
                'related_topics': [],
                'writing_tips': []
            }

            # Extract key quotes
            quotes = re.findall(r'"([^"]*)"', content)
            if quotes:
                suggestions['key_quotes'] = quotes[:3]  # Top 3 quotes

            # Suggest writing angles
            system_prompt = """أنت مستشار كتابة محترف. اقترح زوايا كتابة مختلفة للمقال."""

            prompt = f"""بناءً على هذا المقال:
العنوان: {title}
المحتوى: {content[:1000]}

اقترح 3 زوايا مختلفة لكتابة مقال احترافي حول هذا الموضوع:"""

            angles_text = await self.ai_manager.provider.generate_text(prompt, system_prompt)
            if angles_text:
                angles = [angle.strip() for angle in angles_text.split('\n') if angle.strip()]
                suggestions['suggested_angles'] = angles[:3]

            # Add general writing tips
            suggestions['writing_tips'] = [
                "ابدأ بخلاصة الحدث الرئيسي",
                "استخدم الاقتباسات المباشرة لإضافة المصداقية",
                "اربط الحدث بالسياق الأوسع",
                "اختتم بالتأثيرات المستقبلية المحتملة"
            ]

            return suggestions

        except Exception as e:
            logger.error(f"Writing suggestions generation failed: {str(e)}")
            return {}
