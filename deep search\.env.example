# API Keys
BRAVE_API_KEY=your_brave_search_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here

# Ollama Configuration (for local AI models)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=mistral:latest

# Database Configuration
DATABASE_URL=sqlite:///data/deepsearch.db
VECTOR_DB_PATH=data/vector_db

# Search Configuration
MAX_SEARCH_RESULTS=20
DEFAULT_LANGUAGE=ar
SEARCH_TIMEOUT=30

# Scraping Configuration
USER_AGENT=DeepSearch-Agent/1.0
REQUEST_DELAY=1
CONCURRENT_REQUESTS=5
DOWNLOAD_TIMEOUT=30

# AI Processing Configuration
MAX_CONTENT_LENGTH=10000
SUMMARY_MAX_LENGTH=500
EMBEDDING_MODEL=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1
DEBUG=True

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/deepsearch.log
