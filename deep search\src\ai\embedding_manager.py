"""
Embedding manager for semantic search and similarity
"""

import asyncio
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from loguru import logger


class EmbeddingManager:
    """Manages text embeddings for semantic search"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model_name = config.get('embedding_model', 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2')
        self.model = None
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize the embedding model"""
        try:
            self.model = SentenceTransformer(self.model_name)
            logger.info(f"Embedding model initialized: {self.model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {str(e)}")
            # Fallback to a smaller model
            try:
                self.model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
                logger.info("Fallback embedding model initialized")
            except Exception as e2:
                logger.error(f"Failed to initialize fallback model: {str(e2)}")
                self.model = None
    
    async def generate_embeddings(self, texts: List[str]) -> List[np.ndarray]:
        """
        Generate embeddings for a list of texts
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        if not self.model:
            logger.error("Embedding model not available")
            return []
        
        try:
            # Run embedding generation in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            embeddings = await loop.run_in_executor(
                None, 
                self.model.encode, 
                texts
            )
            
            logger.info(f"Generated embeddings for {len(texts)} texts")
            return embeddings.tolist()
            
        except Exception as e:
            logger.error(f"Embedding generation failed: {str(e)}")
            return []
    
    async def generate_single_embedding(self, text: str) -> Optional[np.ndarray]:
        """
        Generate embedding for a single text
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector or None if failed
        """
        embeddings = await self.generate_embeddings([text])
        return embeddings[0] if embeddings else None
    
    def calculate_similarity(
        self, 
        embedding1: np.ndarray, 
        embedding2: np.ndarray
    ) -> float:
        """
        Calculate cosine similarity between two embeddings
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            Similarity score (0-1)
        """
        try:
            # Reshape for sklearn
            emb1 = np.array(embedding1).reshape(1, -1)
            emb2 = np.array(embedding2).reshape(1, -1)
            
            similarity = cosine_similarity(emb1, emb2)[0][0]
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Similarity calculation failed: {str(e)}")
            return 0.0
    
    def find_most_similar(
        self, 
        query_embedding: np.ndarray, 
        candidate_embeddings: List[np.ndarray],
        top_k: int = 5
    ) -> List[Tuple[int, float]]:
        """
        Find most similar embeddings to query
        
        Args:
            query_embedding: Query embedding vector
            candidate_embeddings: List of candidate embeddings
            top_k: Number of top results to return
            
        Returns:
            List of (index, similarity_score) tuples
        """
        try:
            if not candidate_embeddings:
                return []
            
            # Calculate similarities
            similarities = []
            for i, candidate in enumerate(candidate_embeddings):
                similarity = self.calculate_similarity(query_embedding, candidate)
                similarities.append((i, similarity))
            
            # Sort by similarity (descending)
            similarities.sort(key=lambda x: x[1], reverse=True)
            
            # Return top k
            return similarities[:top_k]
            
        except Exception as e:
            logger.error(f"Similarity search failed: {str(e)}")
            return []
    
    async def semantic_search(
        self, 
        query: str, 
        documents: List[Dict[str, Any]], 
        top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Perform semantic search on documents
        
        Args:
            query: Search query
            documents: List of documents with 'content' field
            top_k: Number of top results
            
        Returns:
            List of most relevant documents with similarity scores
        """
        try:
            # Generate query embedding
            query_embedding = await self.generate_single_embedding(query)
            if query_embedding is None:
                logger.error("Failed to generate query embedding")
                return []
            
            # Generate document embeddings
            document_texts = [doc.get('content', '') for doc in documents]
            document_embeddings = await self.generate_embeddings(document_texts)
            
            if not document_embeddings:
                logger.error("Failed to generate document embeddings")
                return []
            
            # Find most similar documents
            similar_indices = self.find_most_similar(
                query_embedding, 
                document_embeddings, 
                top_k
            )
            
            # Prepare results
            results = []
            for idx, similarity in similar_indices:
                if idx < len(documents):
                    doc = documents[idx].copy()
                    doc['similarity_score'] = similarity
                    results.append(doc)
            
            logger.info(f"Semantic search returned {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Semantic search failed: {str(e)}")
            return []
    
    async def cluster_documents(
        self, 
        documents: List[Dict[str, Any]], 
        num_clusters: int = 5
    ) -> Dict[int, List[Dict[str, Any]]]:
        """
        Cluster documents based on semantic similarity
        
        Args:
            documents: List of documents
            num_clusters: Number of clusters
            
        Returns:
            Dictionary mapping cluster IDs to documents
        """
        try:
            from sklearn.cluster import KMeans
            
            # Generate embeddings
            document_texts = [doc.get('content', '') for doc in documents]
            embeddings = await self.generate_embeddings(document_texts)
            
            if not embeddings:
                logger.error("Failed to generate embeddings for clustering")
                return {}
            
            # Perform clustering
            embeddings_array = np.array(embeddings)
            kmeans = KMeans(n_clusters=min(num_clusters, len(documents)), random_state=42)
            cluster_labels = kmeans.fit_predict(embeddings_array)
            
            # Group documents by cluster
            clusters = {}
            for i, label in enumerate(cluster_labels):
                if label not in clusters:
                    clusters[label] = []
                if i < len(documents):
                    clusters[label].append(documents[i])
            
            logger.info(f"Clustered {len(documents)} documents into {len(clusters)} clusters")
            return clusters
            
        except Exception as e:
            logger.error(f"Document clustering failed: {str(e)}")
            return {}
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the embedding model
        
        Returns:
            Model information dictionary
        """
        if not self.model:
            return {"status": "not_available"}
        
        return {
            "status": "available",
            "model_name": self.model_name,
            "max_seq_length": getattr(self.model, 'max_seq_length', 'unknown'),
            "embedding_dimension": self.model.get_sentence_embedding_dimension()
        }
